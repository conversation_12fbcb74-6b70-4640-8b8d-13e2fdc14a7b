// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Component matches snapshot when rendering search data 1`] = `
<div class="signup_multiple_destination_selection">
  <div class="selected_showcase" style="display: none;">
    <ul></ul>
  </div>
  <div class="destination_dropdown">
    <div class="search_area">
      <svg class="klk-icon klk-icon-search" style="width: 20px; height: 20px;">
        <use xlink:href="#search"></use>
      </svg>
      <input type="text" placeholder="top.search.destination" class="destination_dropdown_search_input">
    </div>
    <ul class="side_menu"></ul>
    <div class="search_city_result_wrapper" style="display: none;">
      <ul class="searched_result">
        <li>
          <p class="search-title">
            <svg class="klk-icon klk-icon-environment" style="width: 16px; height: 16px;">
              <use xlink:href="#environment"></use>
            </svg> <span>香港</span></p>
          <!---->
        </li>
        <li>
          <p class="search-title">
            <!----><span>香港机场快线车票（QR Code迅速入闸）</span></p>
          <div class="searched-city-name">香港</div>
        </li>
        <li>
          <p class="search-title">
            <!----><span>香港迪士尼乐园门票(一日 / 两日) - 无需换票扫码入园</span></p>
          <div class="searched-city-name">香港</div>
        </li>
        <li>
          <p class="search-title">
            <!----><span>【优惠】太平山顶缆车 ＆ 景点套票（快捷通道）</span></p>
          <div class="searched-city-name">香港</div>
        </li>
        <li>
          <p class="search-title">
            <!----><span>香港海洋公园门票 - 无需换票 扫码入园</span></p>
          <div class="searched-city-name">香港</div>
        </li>
        <li>
          <p class="search-title">
            <!----><span>香港迪士尼乐园餐券</span></p>
          <div class="searched-city-name">香港</div>
        </li>
        <li>
          <p class="search-title">
            <!----><span>香港八达通车票（香港机场领取）</span></p>
          <div class="searched-city-name">香港</div>
        </li>
      </ul>
      <div class="no_data_result" style="display: none;">
        no_results_find
      </div>
    </div>
  </div>
</div>
`;
