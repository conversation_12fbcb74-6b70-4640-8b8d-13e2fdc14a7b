import { shallowMount, mount, createLocalVue, RouterLinkStub } from '@vue/test-utils'
import Vue from 'vue'
import Component from '@/pages/tpl/signup_multiple_destination_selection.vue'
import Header from '@/pages/tpl/header.vue'
import loading from '@/directives/loading.js'
import { searchData } from '../mockData'

// createLocalVue 返回一个 Vue 的类供你添加组件、混入和安装插件而不会污染全局的 Vue 类。
const localVue = createLocalVue()
localVue.directive('loading',  loading)

describe('Component', () => {
  it('Component is vue instance', async () => {
    const wrapper = shallowMount(Component, {
      localVue,
      sync: false
    })
    expect(wrapper.isVueInstance()).toBeTruthy()
  })

  // 组件props测试
  it('renders props when passed', async () => {
    const wrapper = shallowMount(Component, {
      propsData: {
        hide_search: false
      },
      localVue,
      sync: false
    })

    expect(wrapper.find('.search_area').isVisible()).toBeTruthy()
  })

  // 快照测试
  it('matches snapshot when rendering search data', (done) => {
    const processData = jest.fn((list) => {
      let cities = [],
          activities = [];
      if (list.cities && list.cities.length) {
          cities = list.cities.map(city => {
              return Object.assign({}, city, {
                  city_id: city.id,
                  type: 'city'
              })
          })
      }

      if (list.activities && list.activities.length) {
          activities = list.activities.map(activity => {
             return Object.assign({}, activity, {
                  type: 'activity'
              })
          }).splice(0, 6)
      }
      return [].concat(cities).concat(activities)
    })
    
    const wrapper = mount(Component, {
      localVue,
      sync: false
    })

    wrapper.setData({
      searched_city_list: processData(searchData.result)
    })

    //由于 Vue 进行异步更新 DOM 的情况
    //一些依赖 DOM 更新结果的断言必须在 Vue.nextTick 回调中进行
    Vue.nextTick(() => {
      expect(wrapper.html()).toMatchSnapshot()
      done()
    })
  })

  // 组件methods测试
  it('search methods have been called by input keydown', (done) => {
    const wrapper = mount(Header, {
      localVue,
      // router,
      mocks: {
        "$route": {
          query: {keyword: ''}
        }
      },
      stubs: {
        RouterLink: RouterLinkStub
      },
      computed: { isLoggedIn: () => true },
      sync: false
    });

    const mockFn = jest.fn();

    wrapper.setMethods({ searchByKeyword: mockFn })

    const input = wrapper.find('input')
    input.trigger('keydown.enter')

    expect(mockFn).toHaveBeenCalled();
    done()
  })
})