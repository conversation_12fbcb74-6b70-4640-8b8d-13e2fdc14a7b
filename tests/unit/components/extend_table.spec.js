import { shallowMount, mount, createLocalVue, RouterLinkStub } from '@vue/test-utils'
import Component from '@/components/tableExtend/TableExtend.vue'
import { batchOrderSearchData } from '../../mockData'

const localVue = createLocalVue()

describe('Component', () => {
  // 组件props测试
  it('matches snapshot when rendering search data', async () => {
    const $t = jest.fn(msg => msg)
    const processData = jest.fn((data) => {//为了把对象转换成表格列表的形式
        var list = [];
        function getActivityRowSpan(activityItem){
            var rowspan = 0;
            activityItem.packages.forEach(function(packageItem){
                packageItem.rowspan = packageItem.ticket_price_count.length;
                rowspan += packageItem.rowspan;
            })
            return rowspan;
        }
        data.forEach(function(activityItem){
            var repeatActivity = 1;

            activityItem.rowspan= getActivityRowSpan(activityItem);
            activityItem.packages.forEach(function(packageItem){
                packageItem.rowspan = packageItem.ticket_price_count.length;

                var repeatPackage = 1;//控制td的显示
                packageItem.ticket_price_count.forEach(function(unitsInfoItem){
                    var row = {};
                    unitsInfoItem.units = unitsInfoItem.price_name+' x '+unitsInfoItem.count;
                    unitsInfoItem.settlementPrice = unitsInfoItem.selling_price;

                    if(repeatActivity == 1){

                        row = {
                            show_package:true,
                            show_activity:true,
                            batch_status: activityItem.batch_status,
                            totalAmount:activityItem.total_amount,
                            poundage:activityItem.poundage,
                            paymentAmount:activityItem.payment_amount,
                            activity_rowspan:activityItem.rowspan,
                            package_rowspan:packageItem.rowspan,
                            createDate:activityItem.booking_date,
                            bookingNumber:activityItem.order_no,
                            activityName:activityItem.activity_name,
                            packages: activityItem.packages,
                            packageName:packageItem.package_name,
                            settlementPrice: unitsInfoItem.settlementPrice,
                            units:unitsInfoItem.units,
                            expiryDate:packageItem.expire_date,
                            relatedBookingNumber:activityItem.agent_booking_ref_no,
                            // statusStr:self.order_status_list.find(status => status.value == activityItem.batch_status).label || '',
                            voucherUrl: activityItem.voucherUrl ? `/v1/agentwebserv/batchorder/download?orderId=${activityItem.bookingId}` : ''
                        }
                    }else{
                        if(repeatPackage == 1){

                            row = {
                                show_package : true,
                                show_activity:false,
                                package_rowspan:packageItem.rowspan,
                                packages: activityItem.packages,
                                units:unitsInfoItem.units,
                                settlementPrice: unitsInfoItem.settlementPrice,
                                expiryDate:packageItem.expire_date,
                                packageName:packageItem.package_name,
                            }
                        }else{

                            row = {
                                show_package:false,
                                show_activity:false,
                                units:unitsInfoItem.units,
                                settlementPrice: unitsInfoItem.settlementPrice,
                                expiryDate:unitsInfoItem.expire_date,
                            }
                        }
                    }
                    row.batchOrderId = activityItem.id;//重新把bookingId补上
                    row.bookingId = activityItem.id;//重新把bookingId补上

                    list.push(row);
                    repeatActivity++;
                    repeatPackage ++ ;
                })
            })
            // activity.rowsplan = item.package.length ;
        })

        return list
    })
    let cols = [
        { width: '94px', label: $t('booking_date')},
        { width: '84px', label: $t('order_no') },
        { width: '120px', label: $t('activity_name')},
        { width: '100px', label: $t('package_name')},
        { width: '80px', label: $t('units')},
        { width: '90px', label: $t('expiry_date')},
        { width: '110px', label: $t('order_status') },
        { width: '110px', label: $t('settlement_price') },
        { width: '90px', label: $t('total_amount') },
        { width: '90px', label: $t('handling_fee') },
        { width: '90px', label: $t('payment_amount') },
        { width: '120px', label: $t('agent_booking_ref_num')},
        { width: '100px', label: $t('operation'), fixed: "right"}
    ]

    const wrapper = mount(Component, {
        propsData: {
            cols,
            table_data: processData(batchOrderSearchData.result.orders)
        },
        scopedSlots: {
            default: `<template slot-scope="scope">
                <tr>
                    <td v-show="scope.row.show_activity" :rowspan="scope.row.activity_rowspan">{{ scope.row.createDate }}</td>
                    <td v-show="scope.row.show_activity" :rowspan="scope.row.activity_rowspan">{{ scope.row.bookingNumber }}</td>
                    <td v-show="scope.row.show_activity" :rowspan="scope.row.activity_rowspan">{{ scope.row.activityName }}</td>
                    <td v-show="scope.row.show_package" :rowspan="scope.row.package_rowspan">{{ scope.row.packageName }}</td>
                    <td>{{ scope.row.units }}</td>
                    <td v-show="scope.row.show_package" :rowspan="scope.row.package_rowspan">{{ scope.row.expiryDate }}</td>
                    <td v-show="scope.row.show_activity" :rowspan="scope.row.activity_rowspan">{{ scope.row.statusStr }}</td>
                    <td>{{ scope.row.settlementPrice }}</td>
                    <td v-show="scope.row.show_activity" :rowspan="scope.row.activity_rowspan">{{ scope.row.totalAmount }}</td>
                    <td v-show="scope.row.show_activity" :rowspan="scope.row.activity_rowspan">{{ scope.row.poundage }}</td>
                    <td v-show="scope.row.show_activity" :rowspan="scope.row.activity_rowspan">{{ scope.row.paymentAmount }}</td>

                    <td v-show="scope.row.show_activity" :rowspan="scope.row.activity_rowspan" >
                        
                    </td>

                    <td v-show="scope.row.show_activity" :rowspan="scope.row.activity_rowspan">
                        
                    </td>
                </tr>
            </template>`
        },
        localVue,
        sync: false
    })
    expect(wrapper.html()).toMatchSnapshot()
  })
})