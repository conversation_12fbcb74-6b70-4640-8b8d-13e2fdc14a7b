#!/bin/bash

# abort on errors
set -e

# use nvm
. ~/.nvm/nvm.sh

node_env='production'
if [ "$NODE_ENV" = "test" ]; then
    node_env=$NODE_ENV
fi

echo "当前NODE_ENV"
echo $node_env

echo "当前npm版本"
npm -v

echo "当前nvm版本"
nvm --version

test -n "$NODE_VERSION" || NODE_VERSION="v12.18.3"
nvm use $NODE_VERSION || nvm install $NODE_VERSION

echo "当前node版本"
node -v

branch=$1
if [ -z "$branch" ]; then
    echo -e "\033[31mthe branch or tag or commit is null that default origin/master.\033[0m"
    branch='origin/master'
fi

# build

buildDir=/srv/builds/klook-agent-web

if [ ! -d "$buildDir" ]; then
    cd /srv/builds/
    <NAME_EMAIL>:klook/klook-agent-web.git
fi

cd $buildDir

git checkout -- .
git fetch
git checkout $branch
if [ "$?" = "1" ]; then
    echo -e "\033[31mcheckout error and exit.\033[0m"
    exit 1
fi

echo "------ Last Commit: -------"
git status | head -1
git show -s --date=relative | head -3
echo "-------------------------------"

npm install

# add test env staticfile build
if [ "$NODE_ENV" = "production" ]; then
    npm run build
else
    npm run build:test
fi

time_now=`date "+%Y-%m-%d %H:%M:%S"`
commit_hash=`git log --pretty=oneline | head -n 1`
echo "[$time_now] [build] branch=> $branch, hash => $commit_hash success " >> /srv/builds/.klook_agent_log

# deploy
cd /srv/klook-agent-web
git checkout -- .
git fetch
git checkout $branch
if [ "$?" = "1" ]; then
    echo -e "\033[31mcheckout error and exit.\033[0m"
    exit 1
fi
npm install --production

# sync file from build folder to deploy folder
echo -e "\033[32msync the dist from build to deploy.\033[0m"
rsync -rvpog --checksum --exclude 'index.html' /srv/builds/klook-agent-web/dist /srv/klook-agent-web/
rsync -rvpog --checksum /srv/builds/klook-agent-web/dist/index.html /srv/klook-agent-web/dist/

if [ "$(pm2 id klook-agent)" = "[]" ]; then
    NODE_ENV=$node_env pm2 start ./server/index.js --name klook-agent --interpreter=node@12.18.3 --output /root/.pm2/logs/klook-agent.log --error /root/.pm2/logs/klook-agent.log --log-date-format "YYYY-MM-DD HH:mm:ss.SSS"
else
    NODE_ENV=$node_env pm2 reload klook-agent
fi

time_now=`date "+%Y-%m-%d %H:%M:%S"`
commit_hash=`git log --pretty=oneline | head -n 1`
echo "[$time_now] [deploy] branch=> $branch, hash => $commit_hash success " >> /srv/builds/.klook_agent_log

# npm install --production
# npm prune --production
