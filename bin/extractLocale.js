const fs = require('fs');
const path = require('path');

const messages = {};

const read = (dir) => {
    console.log(`read dir ${dir}`);
    const files = fs.readdirSync(dir);
    if (!files) {
        console.log('***************Error*********************');
    }
    files.forEach((item, index) => {
        const filePath = path.join(dir, item);
        const fileStat = fs.statSync(filePath);
        if (fileStat && fileStat.isFile()) {
            if (!/\.(js|vue)$/.test(item)) { return ;}
            console.log(`read file ${filePath}`);
            const content = fs.readFileSync(filePath, 'utf-8');
            let len = 0;
            const reg = /\$t\(['"]([\w_\.-]*)['"]\s*,/g;
            let matched = reg.exec(content);
            while (matched && matched[1]) {
                messages[matched[1]] = '';
                len++;
                matched = reg.exec(content);
            }
            console.log('*************read File Success',filePath, len);
        } else if (fileStat && fileStat.isDirectory()) {
            read(filePath);
        }
    });
}

read(path.join('.', '/src'));
console.log(messages, Object.keys(messages).length);

const writeJsonToFile = (json, file) => {
    const wSteam = fs.createWriteStream(file);
    wSteam.write(JSON.stringify(json));
    wSteam.end();
    console.log('**********write file success', file);
};

const replace = (dir) => {
    const langFiles = fs.readdirSync(dir);
    if (!langFiles) {
        console.log('***************Error*********************', dir);
    }
    langFiles.forEach(item => {
        if (!/\.json$/.test(item)) { return; }
        const filePath = path.join(dir, item);
        const langContent = fs.readFileSync(filePath, 'utf-8');
        try {
            const jsonContent = JSON.parse(langContent);
            const newMessages = Object.assign({}, messages);
            Object.keys(newMessages).forEach(item => newMessages[item] = jsonContent[item]);
            writeJsonToFile(newMessages, path.join(dir, `new_${item}`));
        } catch (e) {
            console.log('parse error', filePath);
            throw e;
        }
    });
};

// replace(path.join('.', '/locales'));


