# this file is mainly for local test docker image
# 测试的时候记得替换 nginx/nginx_conf 下的 upstream.conf 的 localhost 为 node_server 便于容器之间通信
version: "3"
services: # 服务
    node: # node 服务
        image: klook-agent-web/node
        container_name: node_server # 容器名称 
        ports: # 端口映射
           - "3006:3006"
        # expose:
        #     - "3000"
        # restart: always # 自动重启
        # environment: 
        #     - NODE_ENV=production
        networks: # 加入网络
            - "my-network"
        # command: npm run start # 覆盖容器启动后默认执行的命令
    nginx:
        image: klook-agent-web/nginx
        ports:
            - "80:80"            
        container_name: nginx_server
        networks: 
            - "my-network"
        depends_on: # 指定依赖于哪个服务
            - node
networks: # 网络
    my-network: # 网络名称
        driver: bridge