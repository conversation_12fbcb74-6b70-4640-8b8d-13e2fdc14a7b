const path = require('path');
module.exports = {
    root: true,

    env: {
        es6: true,
        browser: true,
        node: true,
        commonjs: true,
    },

    settings: {
        'import/extensions': ['.js', '.jsx', '.json', '.vue'],
        'import/resolver': {
            alias: {
                map: [
                    ['~', path.resolve(__dirname)],
                    ['@', path.join(__dirname, '/src')],
                    ['klook', path.join(__dirname, '/src/common/core.js')],
                ],
                extensions: ['.js', '.jsx', '.json', '.vue'],
            },
        },
    },

    extends: ['plugin:vue/essential', 'airbnb-base', 'eslint:recommended'],

    rules: {
        'no-empty': 1,
        'no-var': 2,
        'import/no-extraneous-dependencies': 0,
        'import/prefer-default-export': 0,
        'no-console': process.env.NODE_ENV === 'production' ? 'error' : 'off',
        'no-debugger': process.env.NODE_ENV === 'production' ? 'error' : 'off',
    },

    plugins: ['vue'],

    globals: {
        klook: true,
    },

    parserOptions: {
        parser: 'babel-eslint',
        extends: 'standard',
        ecmaVersion: 2018,
        sourceType: 'module',
        ecmaFeatures: {
            jsx: true,
        },
    },
};
