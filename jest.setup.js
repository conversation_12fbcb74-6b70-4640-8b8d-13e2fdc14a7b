import VueTestUtils from '@vue/test-utils';
import klkModal from '@/components/modal/src/modal.vue';
import klkTable from '@/components/table/Table.vue';
import klkIcon from '@/components/icon/Icon.vue';

VueTestUtils.config.stubs = {
    klkModal,
    klkTable,
    klkIcon,
};
VueTestUtils.config.mocks.$t = msg => msg;

const klook = require('@/common/core.js');

window.KLK_USER_INFO = {
    agent_category: 'China',
    agent_type: 0,
    currency: 'KRW',
    family_name: 'h',
    first_name: 'h',
    gender: 'MR',
    id: 217,
    language: 'zh_CN',
    permissions: { api_list: false, balance_lookup: true, batch_order: true },
    price_tier: 3,
    joint_venture_id: 1,
    user_id: 873550,
};
window.klook = klook;
window.scrollTo = () => {};
