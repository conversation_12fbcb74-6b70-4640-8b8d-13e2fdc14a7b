# Agent Web 本地开发环境搭建 & 备注

## 环境依赖

* Node12 及以上(推荐12.18.3)

## 本地开发步骤

* 安装依赖： npm install
* 运行： npm run dev 然后访问 [首页localhost:8081(端口可能不一致)/index](localhost:8080/index)

本地开发依赖后端接口环境，修改根目录下面的config.js中的`SERVER_API_HOST_URL` 然后重新启动；这个地方可以进行优化（目前node server端的代码修改后，需要重新启动）；整个项目的搭建是基于 [@vue/cli 3](https://cli.vuejs.org/)、打包流程也是通过里面的vue-cli-service进行的。

## 测试环境部署
```
kcli d klook-agent-web/node 分支

```

## kv配置
https://klook.larksuite.com/docx/PlN9dUlhdofWajxUr7yutfmNsue?from=from_copylink

## 接入极验
https://knpm.klook.io/-/web/detail/@klook/captcha
首先要和【用户中心】沟通好，是在【V2】的接口还是在【V3】的接口上做改动
* 如果是V2，按照knpm文档上开发就好
* 如果是V3，可以用V3的校验方法，V3验证方法内部直接包含了注册，所以不需要初始化，直接调用captchaVerify_V3进行校验

## 日志上报
1. 页面/接口的请求上报，使用@klook/logquery
    1.1 server: 使用Nats模式，只用实例化一次保持长连接。
    1.2 web
    
<!-- 移除掉 -->
<!-- 2. 页面/接口的性能上报：使用@klook/client-report -->

## 图标处理

目前图标都是svg转成font文件、然后引入进来；新增图标的方式是：svg文件放入 /src/assets/font-icon-svg目录，然后运行根目录下bin文件夹中的webfont.js重新生成font资源文件；如果不是新增图标，则不需要处理

## 支付流程

支付的流程：进入支付页面，填写信息、点击提交 -> 生成订单 -> 走具体的支付方式 -> 支付成功后跳转结果页； 当前【2019-04-02】Agent Web 的支付方式包括

* 余额支付

    直接post余额支付的接口，支付成功即跳转

* 信用卡支付

    信用卡的支付流程主要受两个字段影响：是否旧卡、是否走3DS （在生成订单接口中返回）。

    如果是旧卡，首先请求接口（传递credit_card_token字段表示旧卡的唯一标识，当前限制为数字类型）
    信用卡支付接入了三种类型的网关SDK：Adyen 、Stripe 、Braintree（具体通过哪种网关支付是在生成订单接口的返回字段种决定的）。

    Braintree: 

        1、如果是新卡且不走3DS，则直接请求支付接口、成功则跳转到结果页
        2、否则走3DS, 新卡和旧卡的区别在于传递的数据不一样，3DS走通后，请求支付接口、成功则跳转

    Stripe:

        1、新卡 + 3DS -> 新卡数据构造 card source (参考stripe文档)、stripe验证3DS -> stripe返回 （需要走3ds则继续，否则直接走支付接口）
        2、新卡 不走3DS -> 新卡数据构造 card token (参考stripe文档) 、stripe验证3DS-> stripe返回 （需要走3ds则继续，否则直接走支付接口）
        3、旧卡 + 3DS -> post接口请求旧卡的card souce、stripe验证3DS  -> stripe返回 （需要走3ds则继续，否则直接走支付接口）
        4、旧卡 + 不需要3DS -> 直接走支付接口

    Adyen:

        1、如果是新卡，则使用新卡数据构造card_encrypted_json
        2、如果走3DS, 则验证3DS、成功则走支付接口；
        3、如果不走3DS, 则直接走支付接口

    Adyen 和 Stripe 网关验证3DS成功后，会跳转到一个3DS的中间页面（/pay/3ds），在中间页继续走最后的支付接口；Stripe 验证3DS的过程中，可能认为当前信用卡的安全风险低、不需要走3DS，这个时候就直接走支付接口

* 支付宝

    直接请求支付宝支付的接口，然后跳转到支付宝付款页

* paypal

    根据生成订单的返回数据，跳转到指定的paypal付款页面，付款确认完成后，跳转到中间页面(/pay/3ds)请求最后的支付接口


