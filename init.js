process.env.KL_TEAM = 'service';
// 配置服务名
process.env.KL_SERVICE_NAME = 'node';
process.env.KL_PROJECT = 'klook-agent-web';
const {
    initConfigSync,
    getCurrentConfig,
    getProperties,
} = require('@klook/kv');

let kvConfig = {};
try {
    // 只在 docker 环境获取kv 配置, 本地开发，编译时不获取
    initConfigSync();
    console.log('当前配置', JSON.stringify(getCurrentConfig()));
    console.log('当前环境', JSON.stringify(getProperties()));
    kvConfig = getCurrentConfig() || {};
    console.log(kvConfig, 'kvConfig');
    // 链路追踪需要依赖 process 的环境变量
    // require('@klook/otel');
} catch (e) {
    console.log('get kv error:');
    console.log(e);
}
module.exports = { kvConfig };
