#!/bin/bash
if [[ -z "${CODEBUILD_WEBHOOK_TRIGGER}" ]]; then
  echo "trigger from console"
else
	export CODEBUILD_SOURCE_VERSION=$(echo $CODEBUILD_WEBHOOK_TRIGGER | sed 's/branch\///1')
fi
export CODEBUILD_SOURCE_VERSION=$(echo $CODEBUILD_SOURCE_VERSION | sed 's/\//-/1')
echo "branch:" $CODEBUILD_SOURCE_VERSION

# build node image
docker build -t $DOCKER_IMAGE  .
REMOTE_IMAGE_TAG=$AWS_ACCOUNT_ID.dkr.ecr.$AWS_DEFAULT_REGION.amazonaws.com/$DOCKER_IMAGE:$CODEBUILD_SOURCE_VERSION-${CODEBUILD_RESOLVED_SOURCE_VERSION:0:8}
REMOTE_IMAGE_BRANCH_TAG=$AWS_ACCOUNT_ID.dkr.ecr.$AWS_DEFAULT_REGION.amazonaws.com/$DOCKER_IMAGE:$CODEBUILD_SOURCE_VERSION
docker tag $DOCKER_IMAGE $REMOTE_IMAGE_TAG
docker tag $DOCKER_IMAGE $REMOTE_IMAGE_BRANCH_TAG
echo "Pushing the node Docker image, $REMOTE_IMAGE_TAG"
docker push $REMOTE_IMAGE_TAG
docker push $REMOTE_IMAGE_BRANCH_TAG

# build nginx image
cd nginx
docker build -t $NGINX_DOCKER_IMAGE  .
NGINX_REMOTE_IMAGE_TAG_NGINX=$AWS_ACCOUNT_ID.dkr.ecr.$AWS_DEFAULT_REGION.amazonaws.com/$NGINX_DOCKER_IMAGE:$CODEBUILD_SOURCE_VERSION-${CODEBUILD_RESOLVED_SOURCE_VERSION:0:8}
NGINX_REMOTE_IMAGE_BRANCH_TAG=$AWS_ACCOUNT_ID.dkr.ecr.$AWS_DEFAULT_REGION.amazonaws.com/$NGINX_DOCKER_IMAGE:$CODEBUILD_SOURCE_VERSION
docker tag $NGINX_DOCKER_IMAGE $NGINX_REMOTE_IMAGE_TAG_NGINX
docker tag $NGINX_DOCKER_IMAGE $NGINX_REMOTE_IMAGE_BRANCH_TAG
echo "Pushing the nginx Docker image, $NGINX_REMOTE_IMAGE_TAG_NGINX"
docker push $NGINX_REMOTE_IMAGE_TAG_NGINX
docker push $NGINX_REMOTE_IMAGE_BRANCH_TAG