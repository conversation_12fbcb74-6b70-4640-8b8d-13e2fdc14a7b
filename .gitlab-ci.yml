stages:
  - test
  - pre-build
  - build
  - deploy

variables:
  DIST_PATH: dist
  IMAGE_REPO_NAME: klook-agent-web/node
  NGINX_DOCKER_IMAGE: klook-agent-web/nginx
  NODE_CDN_S3_BUCKET_PATH: klook-agent-web/dist # edit it
  BUILD_SCRIPT: build:prod
  # NODE_CDN_S3_BUCKET: klook-staticfile/dist_web # 有默认值，如果使用其他 Bucket 需要修改。

# 新建common_rules 仅 master cn分支 stage分支走构建流程

.common_rules: &common_rules
  rules: 
    - if: $CI_COMMIT_REF_NAME =~ "master" || $CI_COMMIT_REF_NAME =~ /feat\/cn|stage|env-release-/ 

workflow:
  rules:
    - if: $CI_COMMIT_REF_NAME =~ /feat\/cn|stage|env-release-/  # 非产线docker环境
      variables:
        BUILD_SCRIPT: build:staging
        NODE_CDN_S3_BUCKET_PATH: klook-agent-web_staging/dist
    - when: always

include:
  - project: 'ops-public/ci-templates'
    file: 'build/multiple/node/web-base.yml'

  - project: 'ops-public/ci-templates'
    file: 'build/multiple/node/base.yml'

  - project: 'ops-public/ci-templates'
    file: 'build/multiple/node/build-docker.yml'
    <<: *common_rules

  - project: 'ops-public/ci-templates'
    file: 'build/multiple/node/deploy-dist.yml'
    <<: *common_rules

cr-robot:
  extends: .cr-robot


# 若需要上传文件到 CDN 请添加以下 Job，注意静态文件需要输出到 dist 目录
build:dist:
  extends: .build:dist
  image: registry.klook.io/node/node:16-slim  # 注意这里 node 版本需要与 Dockerfile 关联
  script:
    - npm install
    - npm run $BUILD_SCRIPT
  <<: *common_rules