module.exports = {
    presets: [
        [
            '@vue/app',
            {
                modules: false,
            },
        ],
    ],
    ignore: ['./config.js'],
    plugins: [
        '@babel/plugin-syntax-dynamic-import',
        '@babel/plugin-proposal-optional-chaining',
        '@babel/plugin-proposal-nullish-coalescing-operator',
        'date-fns',
        [
            'import',
            {
                libraryName: '@klook/klook-ui',
                style: false,
            },
        ],
    ],
};
