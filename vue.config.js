const path = require('path');
const webpack = require('webpack');
const HtmlHarddiskPlugin = require('html-webpack-harddisk-plugin');
// const CompressionPlugin = require('compression-webpack-plugin');
// const BundleAnalyzerPlugin = require('webpack-bundle-analyzer').BundleAnalyzerPlugin;

const is_staging = process.env.DEPLOY_ENV === 'staging';
const public_path_suffix = is_staging ? '_staging' : '';

module.exports = {
    productionSourceMap: false,
    chainWebpack: config => {
        config.plugin('html').tap(args => {
            args[0].alwaysWriteToDisk = true;
            return args;
        });
        config.plugin('prefetch').tap(args => {
            // /[login|home]/不管用，为什么？
            args[0].fileWhitelist = [
                /login/,
                /agent_index/,
                /city_activity/,
                /activity_detail/,
                /signup/,
                /404/,
                /pay/,
            ];
            return args;
        });
        const svgRule = config.module.rule('svg');
        svgRule.uses.clear();
        svgRule.use('svg-sprite-loader').loader('svg-sprite-loader');
    },
    configureWebpack: {
        resolve: {
            alias: {
                '~': path.resolve(__dirname),
                klook: path.join(__dirname, '/src/common/core.js'),
            },
        },
        plugins: [
            new HtmlHarddiskPlugin(),
            new webpack.ProvidePlugin({
                klook: 'klook',
            }),
            // new BundleAnalyzerPlugin()
        ],
        module: {
            noParse: [/node_modules\/adyen-cse-js/],
        },
    },
    devServer: {
        setup(app) {
            // 根路径访问重定向到主页面的路径，进入 node 渲染的逻辑处理
            app.get('/', (req, res) => {
                let queryPart = req.url.slice(1);
                let indexPath = `/index${queryPart}`;
                res.redirect(indexPath);
            });
        },
        proxy: {
            '/wlog': {
                target: 'https://log.klook.com',
            },
            '^((?!(websocket|webpack|\\.js(?!on)|\\.css)).)*$': {
                target: 'http://localhost:3006',
                onProxyReq: null,
            },
        },
    },
    publicPath:
        process.env.NODE_ENV === 'production'
            ? `https://cdn.klook.com/s/dist_web/klook-agent-web${public_path_suffix}/dist`
            : '/',
    outputDir: 'dist',
};
