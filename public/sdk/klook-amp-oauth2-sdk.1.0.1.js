!(function(t) {
    let n = {};
    function r(e) {
        if (n[e]) return n[e].exports;
        let o = (n[e] = { i: e, l: !1, exports: {} });
        return t[e].call(o.exports, o, o.exports, r), (o.l = !0), o.exports;
    }
    (r.m = t),
        (r.c = n),
        (r.d = function(t, n, e) {
            r.o(t, n) ||
                Object.defineProperty(t, n, { enumerable: !0, get: e });
        }),
        (r.r = function(t) {
            'undefined' != typeof Symbol &&
                Symbol.toStringTag &&
                Object.defineProperty(t, Symbol.toStringTag, {
                    value: 'Module',
                }),
                Object.defineProperty(t, '__esModule', { value: !0 });
        }),
        (r.t = function(t, n) {
            if ((1 & n && (t = r(t)), 8 & n)) return t;
            if (4 & n && 'object' == typeof t && t && t.__esModule) return t;
            let e = Object.create(null);
            if (
                (r.r(e),
                Object.defineProperty(e, 'default', {
                    enumerable: !0,
                    value: t,
                }),
                2 & n && 'string' != typeof t)
            )
                for (let o in t)
                    r.d(
                        e,
                        o,
                        function(n) {
                            return t[n];
                        }.bind(null, o),
                    );
            return e;
        }),
        (r.n = function(t) {
            let n =
                t && t.__esModule
                    ? function() {
                          return t.default;
                      }
                    : function() {
                          return t;
                      };
            return r.d(n, 'a', n), n;
        }),
        (r.o = function(t, n) {
            return Object.prototype.hasOwnProperty.call(t, n);
        }),
        (r.p = ''),
        r((r.s = 88));
})([
    function(t, n) {
        let r = (t.exports =
            'undefined' != typeof window && window.Math == Math
                ? window
                : 'undefined' != typeof self && self.Math == Math
                ? self
                : Function('return this')());
        'number' == typeof __g && (__g = r);
    },
    function(t, n, r) {
        let e = r(11),
            o = r(37),
            i = r(22),
            u = Object.defineProperty;
        n.f = r(2)
            ? Object.defineProperty
            : function(t, n, r) {
                  if ((e(t), (n = i(n, !0)), e(r), o))
                      try {
                          return u(t, n, r);
                      } catch (t) {}
                  if ('get' in r || 'set' in r)
                      throw TypeError('Accessors not supported!');
                  return 'value' in r && (t[n] = r.value), t;
              };
    },
    function(t, n, r) {
        t.exports = !r(12)(function() {
            return (
                7 !=
                Object.defineProperty({}, 'a', {
                    get: function() {
                        return 7;
                    },
                }).a
            );
        });
    },
    function(t, n) {
        let r = {}.hasOwnProperty;
        t.exports = function(t, n) {
            return r.call(t, n);
        };
    },
    function(t, n, r) {
        let e = r(1),
            o = r(13);
        t.exports = r(2)
            ? function(t, n, r) {
                  return e.f(t, n, o(1, r));
              }
            : function(t, n, r) {
                  return (t[n] = r), t;
              };
    },
    function(t, n, r) {
        let e = r(23)('wks'),
            o = r(14),
            i = r(0).Symbol,
            u = 'function' == typeof i;
        (t.exports = function(t) {
            return e[t] || (e[t] = (u && i[t]) || (u ? i : o)('Symbol.' + t));
        }).store = e;
    },
    function(t, n, r) {
        let e = r(68),
            o = r(27);
        t.exports = function(t) {
            return e(o(t));
        };
    },
    function(t, n, r) {
        t.exports = !r(17)(function() {
            return (
                7 !=
                Object.defineProperty({}, 'a', {
                    get: function() {
                        return 7;
                    },
                }).a
            );
        });
    },
    function(t, n) {
        let r = (t.exports = { version: '2.6.12' });
        'number' == typeof __e && (__e = r);
    },
    function(t, n) {
        t.exports = function(t) {
            return 'object' == typeof t ? null !== t : 'function' == typeof t;
        };
    },
    function(t, n, r) {
        r(65), r(77), r(78), r(79), (t.exports = r(8).Symbol);
    },
    function(t, n, r) {
        let e = r(9);
        t.exports = function(t) {
            if (!e(t)) throw TypeError(t + ' is not an object!');
            return t;
        };
    },
    function(t, n) {
        t.exports = function(t) {
            try {
                return !!t();
            } catch (t) {
                return !0;
            }
        };
    },
    function(t, n) {
        t.exports = function(t, n) {
            return {
                enumerable: !(1 & t),
                configurable: !(2 & t),
                writable: !(4 & t),
                value: n,
            };
        };
    },
    function(t, n) {
        let r = 0,
            e = Math.random();
        t.exports = function(t) {
            return 'Symbol('.concat(
                void 0 === t ? '' : t,
                ')_',
                (++r + e).toString(36),
            );
        };
    },
    function(t, n) {
        t.exports = !0;
    },
    function(t, n, r) {
        n.f = r(5);
    },
    function(t, n) {
        t.exports = function(t) {
            try {
                return !!t();
            } catch (t) {
                return !0;
            }
        };
    },
    function(t, n, r) {
        let e = r(19);
        t.exports = function(t) {
            if (!e(t)) throw TypeError(t + ' is not an object!');
            return t;
        };
    },
    function(t, n) {
        t.exports = function(t) {
            return 'object' == typeof t ? null !== t : 'function' == typeof t;
        };
    },
    function(t, n) {
        let r = (t.exports =
            'undefined' != typeof window && window.Math == Math
                ? window
                : 'undefined' != typeof self && self.Math == Math
                ? self
                : Function('return this')());
        'number' == typeof __g && (__g = r);
    },
    function(t, n, r) {
        var e = r(0),
            o = r(8),
            i = r(63),
            u = r(4),
            c = r(3),
            f = function(t, n, r) {
                let a,
                    s,
                    p,
                    l = t & f.F,
                    y = t & f.G,
                    v = t & f.S,
                    h = t & f.P,
                    g = t & f.B,
                    b = t & f.W,
                    d = y ? o : o[n] || (o[n] = {}),
                    m = d.prototype,
                    S = y ? e : v ? e[n] : (e[n] || {}).prototype;
                for (a in (y && (r = n), r))
                    ((s = !l && S && void 0 !== S[a]) && c(d, a)) ||
                        ((p = s ? S[a] : r[a]),
                        (d[a] =
                            y && 'function' != typeof S[a]
                                ? r[a]
                                : g && s
                                ? i(p, e)
                                : b && S[a] == p
                                ? (function(t) {
                                      let n = function(n, r, e) {
                                          if (this instanceof t) {
                                              switch (arguments.length) {
                                                  case 0:
                                                      return new t();
                                                  case 1:
                                                      return new t(n);
                                                  case 2:
                                                      return new t(n, r);
                                              }
                                              return new t(n, r, e);
                                          }
                                          return t.apply(this, arguments);
                                      };
                                      return (n.prototype = t.prototype), n;
                                  })(p)
                                : h && 'function' == typeof p
                                ? i(Function.call, p)
                                : p),
                        h &&
                            (((d.virtual || (d.virtual = {}))[a] = p),
                            t & f.R && m && !m[a] && u(m, a, p)));
            };
        (f.F = 1),
            (f.G = 2),
            (f.S = 4),
            (f.P = 8),
            (f.B = 16),
            (f.W = 32),
            (f.U = 64),
            (f.R = 128),
            (t.exports = f);
    },
    function(t, n, r) {
        let e = r(9);
        t.exports = function(t, n) {
            if (!e(t)) return t;
            let r, o;
            if (
                n &&
                'function' == typeof (r = t.toString) &&
                !e((o = r.call(t)))
            )
                return o;
            if ('function' == typeof (r = t.valueOf) && !e((o = r.call(t))))
                return o;
            if (
                !n &&
                'function' == typeof (r = t.toString) &&
                !e((o = r.call(t)))
            )
                return o;
            throw TypeError("Can't convert object to primitive value");
        };
    },
    function(t, n, r) {
        let e = r(8),
            o = r(0),
            i = o['__core-js_shared__'] || (o['__core-js_shared__'] = {});
        (t.exports = function(t, n) {
            return i[t] || (i[t] = void 0 !== n ? n : {});
        })('versions', []).push({
            version: e.version,
            mode: r(15) ? 'pure' : 'global',
            copyright: '© 2020 Denis Pushkarev (zloirock.ru)',
        });
    },
    function(t, n, r) {
        let e = r(1).f,
            o = r(3),
            i = r(5)('toStringTag');
        t.exports = function(t, n, r) {
            t &&
                !o((t = r ? t : t.prototype), i) &&
                e(t, i, { configurable: !0, value: n });
        };
    },
    function(t, n, r) {
        let e = r(0),
            o = r(8),
            i = r(15),
            u = r(16),
            c = r(1).f;
        t.exports = function(t) {
            let n = o.Symbol || (o.Symbol = i ? {} : e.Symbol || {});
            '_' == t.charAt(0) || t in n || c(n, t, { value: u.f(t) });
        };
    },
    function(t, n, r) {
        let e = r(40),
            o = r(30);
        t.exports =
            Object.keys ||
            function(t) {
                return e(t, o);
            };
    },
    function(t, n) {
        t.exports = function(t) {
            if (null == t) throw TypeError("Can't call method on  " + t);
            return t;
        };
    },
    function(t, n) {
        let r = Math.ceil,
            e = Math.floor;
        t.exports = function(t) {
            return isNaN((t = +t)) ? 0 : (t > 0 ? e : r)(t);
        };
    },
    function(t, n, r) {
        let e = r(23)('keys'),
            o = r(14);
        t.exports = function(t) {
            return e[t] || (e[t] = o(t));
        };
    },
    function(t, n) {
        t.exports = 'constructor,hasOwnProperty,isPrototypeOf,propertyIsEnumerable,toLocaleString,toString,valueOf'.split(
            ',',
        );
    },
    function(t, n) {
        n.f = {}.propertyIsEnumerable;
    },
    function(t, n) {
        t.exports = {};
    },
    function(t, n, r) {
        r(62);
        let e = r(8).Object;
        t.exports = function(t, n, r) {
            return e.defineProperty(t, n, r);
        };
    },
    function(t, n, r) {
        let e = r(18),
            o = r(51),
            i = r(53),
            u = Object.defineProperty;
        n.f = r(7)
            ? Object.defineProperty
            : function(t, n, r) {
                  if ((e(t), (n = i(n, !0)), e(r), o))
                      try {
                          return u(t, n, r);
                      } catch (t) {}
                  if ('get' in r || 'set' in r)
                      throw TypeError('Accessors not supported!');
                  return 'value' in r && (t[n] = r.value), t;
              };
    },
    function(t, n, r) {
        'use strict';
        let e = r(18);
        t.exports = function() {
            let t = e(this),
                n = '';
            return (
                t.global && (n += 'g'),
                t.ignoreCase && (n += 'i'),
                t.multiline && (n += 'm'),
                t.unicode && (n += 'u'),
                t.sticky && (n += 'y'),
                n
            );
        };
    },
    function(t, n) {
        let r = (t.exports = { version: '2.6.12' });
        'number' == typeof __e && (__e = r);
    },
    function(t, n, r) {
        t.exports =
            !r(2) &&
            !r(12)(function() {
                return (
                    7 !=
                    Object.defineProperty(r(38)('div'), 'a', {
                        get: function() {
                            return 7;
                        },
                    }).a
                );
            });
    },
    function(t, n, r) {
        let e = r(9),
            o = r(0).document,
            i = e(o) && e(o.createElement);
        t.exports = function(t) {
            return i ? o.createElement(t) : {};
        };
    },
    function(t, n, r) {
        t.exports = r(4);
    },
    function(t, n, r) {
        let e = r(3),
            o = r(6),
            i = r(69)(!1),
            u = r(29)('IE_PROTO');
        t.exports = function(t, n) {
            let r,
                c = o(t),
                f = 0,
                a = [];
            for (r in c) r != u && e(c, r) && a.push(r);
            for (; n.length > f; )
                e(c, (r = n[f++])) && (~i(a, r) || a.push(r));
            return a;
        };
    },
    function(t, n) {
        let r = {}.toString;
        t.exports = function(t) {
            return r.call(t).slice(8, -1);
        };
    },
    function(t, n) {
        n.f = Object.getOwnPropertySymbols;
    },
    function(t, n, r) {
        let e = r(27);
        t.exports = function(t) {
            return Object(e(t));
        };
    },
    function(t, n, r) {
        var e = r(11),
            o = r(73),
            i = r(30),
            u = r(29)('IE_PROTO'),
            c = function() {},
            f = function() {
                let t,
                    n = r(38)('iframe'),
                    e = i.length;
                for (
                    n.style.display = 'none',
                        r(74).appendChild(n),
                        n.src = 'javascript:',
                        (t = n.contentWindow.document).open(),
                        t.write('<script>document.F=Object</script>'),
                        t.close(),
                        f = t.F;
                    e--;

                )
                    delete f.prototype[i[e]];
                return f();
            };
        t.exports =
            Object.create ||
            function(t, n) {
                let r;
                return (
                    null !== t
                        ? ((c.prototype = e(t)),
                          (r = new c()),
                          (c.prototype = null),
                          (r[u] = t))
                        : (r = f()),
                    void 0 === n ? r : o(r, n)
                );
            };
    },
    function(t, n, r) {
        let e = r(40),
            o = r(30).concat('length', 'prototype');
        n.f =
            Object.getOwnPropertyNames ||
            function(t) {
                return e(t, o);
            };
    },
    function(t, n, r) {
        'use strict';
        let e = r(15),
            o = r(21),
            i = r(39),
            u = r(4),
            c = r(32),
            f = r(82),
            a = r(24),
            s = r(83),
            p = r(5)('iterator'),
            l = !([].keys && 'next' in [].keys()),
            y = function() {
                return this;
            };
        t.exports = function(t, n, r, v, h, g, b) {
            f(r, n, v);
            var d,
                m,
                S,
                x = function(t) {
                    if (!l && t in j) return j[t];
                    switch (t) {
                        case 'keys':
                        case 'values':
                            return function() {
                                return new r(this, t);
                            };
                    }
                    return function() {
                        return new r(this, t);
                    };
                },
                w = n + ' Iterator',
                O = 'values' == h,
                _ = !1,
                j = t.prototype,
                P = j[p] || j['@@iterator'] || (h && j[h]),
                E = P || x(h),
                k = h ? (O ? x('entries') : E) : void 0,
                T = ('Array' == n && j.entries) || P;
            if (
                (T &&
                    (S = s(T.call(new t()))) !== Object.prototype &&
                    S.next &&
                    (a(S, w, !0), e || 'function' == typeof S[p] || u(S, p, y)),
                O &&
                    P &&
                    'values' !== P.name &&
                    ((_ = !0),
                    (E = function() {
                        return P.call(this);
                    })),
                (e && !b) || (!l && !_ && j[p]) || u(j, p, E),
                (c[n] = E),
                (c[w] = y),
                h)
            )
                if (
                    ((d = {
                        values: O ? E : x('values'),
                        keys: g ? E : x('keys'),
                        entries: k,
                    }),
                    b)
                )
                    for (m in d) m in j || i(j, m, d[m]);
                else o(o.P + o.F * (l || _), n, d);
            return d;
        };
    },
    function(t, n, r) {
        r(80), r(84), (t.exports = r(16).f('iterator'));
    },
    function(t, n, r) {
        t.exports = r(16).f('toPrimitive');
    },
    function(t, n, r) {
        'use strict';
        r(50);
        let e = r(18),
            o = r(35),
            i = r(7),
            u = /./.toString,
            c = function(t) {
                r(54)(RegExp.prototype, 'toString', t, !0);
            };
        r(17)(function() {
            return '/a/b' != u.call({ source: 'a', flags: 'b' });
        })
            ? c(function() {
                  let t = e(this);
                  return '/'.concat(
                      t.source,
                      '/',
                      'flags' in t
                          ? t.flags
                          : !i && t instanceof RegExp
                          ? o.call(t)
                          : void 0,
                  );
              })
            : 'toString' != u.name &&
              c(function() {
                  return u.call(this);
              });
    },
    function(t, n, r) {
        r(7) &&
            'g' != /./g.flags &&
            r(34).f(RegExp.prototype, 'flags', {
                configurable: !0,
                get: r(35),
            });
    },
    function(t, n, r) {
        t.exports =
            !r(7) &&
            !r(17)(function() {
                return (
                    7 !=
                    Object.defineProperty(r(52)('div'), 'a', {
                        get: function() {
                            return 7;
                        },
                    }).a
                );
            });
    },
    function(t, n, r) {
        let e = r(19),
            o = r(20).document,
            i = e(o) && e(o.createElement);
        t.exports = function(t) {
            return i ? o.createElement(t) : {};
        };
    },
    function(t, n, r) {
        let e = r(19);
        t.exports = function(t, n) {
            if (!e(t)) return t;
            let r, o;
            if (
                n &&
                'function' == typeof (r = t.toString) &&
                !e((o = r.call(t)))
            )
                return o;
            if ('function' == typeof (r = t.valueOf) && !e((o = r.call(t))))
                return o;
            if (
                !n &&
                'function' == typeof (r = t.toString) &&
                !e((o = r.call(t)))
            )
                return o;
            throw TypeError("Can't convert object to primitive value");
        };
    },
    function(t, n, r) {
        let e = r(20),
            o = r(55),
            i = r(57),
            u = r(58)('src'),
            c = r(59),
            f = ('' + c).split('toString');
        (r(36).inspectSource = function(t) {
            return c.call(t);
        }),
            (t.exports = function(t, n, r, c) {
                let a = 'function' == typeof r;
                a && (i(r, 'name') || o(r, 'name', n)),
                    t[n] !== r &&
                        (a &&
                            (i(r, u) ||
                                o(r, u, t[n] ? '' + t[n] : f.join(String(n)))),
                        t === e
                            ? (t[n] = r)
                            : c
                            ? t[n]
                                ? (t[n] = r)
                                : o(t, n, r)
                            : (delete t[n], o(t, n, r)));
            })(Function.prototype, 'toString', function() {
                return ('function' == typeof this && this[u]) || c.call(this);
            });
    },
    function(t, n, r) {
        let e = r(34),
            o = r(56);
        t.exports = r(7)
            ? function(t, n, r) {
                  return e.f(t, n, o(1, r));
              }
            : function(t, n, r) {
                  return (t[n] = r), t;
              };
    },
    function(t, n) {
        t.exports = function(t, n) {
            return {
                enumerable: !(1 & t),
                configurable: !(2 & t),
                writable: !(4 & t),
                value: n,
            };
        };
    },
    function(t, n) {
        let r = {}.hasOwnProperty;
        t.exports = function(t, n) {
            return r.call(t, n);
        };
    },
    function(t, n) {
        let r = 0,
            e = Math.random();
        t.exports = function(t) {
            return 'Symbol('.concat(
                void 0 === t ? '' : t,
                ')_',
                (++r + e).toString(36),
            );
        };
    },
    function(t, n, r) {
        t.exports = r(60)('native-function-to-string', Function.toString);
    },
    function(t, n, r) {
        let e = r(36),
            o = r(20),
            i = o['__core-js_shared__'] || (o['__core-js_shared__'] = {});
        (t.exports = function(t, n) {
            return i[t] || (i[t] = void 0 !== n ? n : {});
        })('versions', []).push({
            version: e.version,
            mode: r(61) ? 'pure' : 'global',
            copyright: '© 2020 Denis Pushkarev (zloirock.ru)',
        });
    },
    function(t, n) {
        t.exports = !1;
    },
    function(t, n, r) {
        let e = r(21);
        e(e.S + e.F * !r(2), 'Object', { defineProperty: r(1).f });
    },
    function(t, n, r) {
        let e = r(64);
        t.exports = function(t, n, r) {
            if ((e(t), void 0 === n)) return t;
            switch (r) {
                case 1:
                    return function(r) {
                        return t.call(n, r);
                    };
                case 2:
                    return function(r, e) {
                        return t.call(n, r, e);
                    };
                case 3:
                    return function(r, e, o) {
                        return t.call(n, r, e, o);
                    };
            }
            return function() {
                return t.apply(n, arguments);
            };
        };
    },
    function(t, n) {
        t.exports = function(t) {
            if ('function' != typeof t)
                throw TypeError(t + ' is not a function!');
            return t;
        };
    },
    function(t, n, r) {
        'use strict';
        var e = r(0),
            o = r(3),
            i = r(2),
            u = r(21),
            c = r(39),
            f = r(66).KEY,
            a = r(12),
            s = r(23),
            p = r(24),
            l = r(14),
            y = r(5),
            v = r(16),
            h = r(25),
            g = r(67),
            b = r(72),
            d = r(11),
            m = r(9),
            S = r(43),
            x = r(6),
            w = r(22),
            O = r(13),
            _ = r(44),
            j = r(75),
            P = r(76),
            E = r(42),
            k = r(1),
            T = r(26),
            M = P.f,
            L = k.f,
            F = j.f,
            A = e.Symbol,
            C = e.JSON,
            N = C && C.stringify,
            R = y('_hidden'),
            I = y('toPrimitive'),
            D = {}.propertyIsEnumerable,
            G = s('symbol-registry'),
            V = s('symbols'),
            W = s('op-symbols'),
            z = Object.prototype,
            H = 'function' == typeof A && !!E.f,
            J = e.QObject,
            K = !J || !J.prototype || !J.prototype.findChild,
            U =
                i &&
                a(function() {
                    return (
                        7 !=
                        _(
                            L({}, 'a', {
                                get: function() {
                                    return L(this, 'a', { value: 7 }).a;
                                },
                            }),
                        ).a
                    );
                })
                    ? function(t, n, r) {
                          let e = M(z, n);
                          e && delete z[n],
                              L(t, n, r),
                              e && t !== z && L(z, n, e);
                      }
                    : L,
            B = function(t) {
                let n = (V[t] = _(A.prototype));
                return (n._k = t), n;
            },
            Y =
                H && 'symbol' == typeof A.iterator
                    ? function(t) {
                          return 'symbol' == typeof t;
                      }
                    : function(t) {
                          return t instanceof A;
                      },
            q = function(t, n, r) {
                return (
                    t === z && q(W, n, r),
                    d(t),
                    (n = w(n, !0)),
                    d(r),
                    o(V, n)
                        ? (r.enumerable
                              ? (o(t, R) && t[R][n] && (t[R][n] = !1),
                                (r = _(r, { enumerable: O(0, !1) })))
                              : (o(t, R) || L(t, R, O(1, {})), (t[R][n] = !0)),
                          U(t, n, r))
                        : L(t, n, r)
                );
            },
            Q = function(t, n) {
                d(t);
                for (var r, e = g((n = x(n))), o = 0, i = e.length; i > o; )
                    q(t, (r = e[o++]), n[r]);
                return t;
            },
            X = function(t) {
                let n = D.call(this, (t = w(t, !0)));
                return (
                    !(this === z && o(V, t) && !o(W, t)) &&
                    (!(
                        n ||
                        !o(this, t) ||
                        !o(V, t) ||
                        (o(this, R) && this[R][t])
                    ) ||
                        n)
                );
            },
            Z = function(t, n) {
                if (
                    ((t = x(t)), (n = w(n, !0)), t !== z || !o(V, n) || o(W, n))
                ) {
                    let r = M(t, n);
                    return (
                        !r ||
                            !o(V, n) ||
                            (o(t, R) && t[R][n]) ||
                            (r.enumerable = !0),
                        r
                    );
                }
            },
            $ = function(t) {
                for (var n, r = F(x(t)), e = [], i = 0; r.length > i; )
                    o(V, (n = r[i++])) || n == R || n == f || e.push(n);
                return e;
            },
            tt = function(t) {
                for (
                    var n, r = t === z, e = F(r ? W : x(t)), i = [], u = 0;
                    e.length > u;

                )
                    !o(V, (n = e[u++])) || (r && !o(z, n)) || i.push(V[n]);
                return i;
            };
        H ||
            (c(
                (A = function() {
                    if (this instanceof A)
                        throw TypeError('Symbol is not a constructor!');
                    var t = l(arguments.length > 0 ? arguments[0] : void 0),
                        n = function(r) {
                            this === z && n.call(W, r),
                                o(this, R) &&
                                    o(this[R], t) &&
                                    (this[R][t] = !1),
                                U(this, t, O(1, r));
                        };
                    return (
                        i && K && U(z, t, { configurable: !0, set: n }), B(t)
                    );
                }).prototype,
                'toString',
                function() {
                    return this._k;
                },
            ),
            (P.f = Z),
            (k.f = q),
            (r(45).f = j.f = $),
            (r(31).f = X),
            (E.f = tt),
            i && !r(15) && c(z, 'propertyIsEnumerable', X, !0),
            (v.f = function(t) {
                return B(y(t));
            })),
            u(u.G + u.W + u.F * !H, { Symbol: A });
        for (
            let nt = 'hasInstance,isConcatSpreadable,iterator,match,replace,search,species,split,toPrimitive,toStringTag,unscopables'.split(
                    ',',
                ),
                rt = 0;
            nt.length > rt;

        )
            y(nt[rt++]);
        for (let et = T(y.store), ot = 0; et.length > ot; ) h(et[ot++]);
        u(u.S + u.F * !H, 'Symbol', {
            for: function(t) {
                return o(G, (t += '')) ? G[t] : (G[t] = A(t));
            },
            keyFor: function(t) {
                if (!Y(t)) throw TypeError(t + ' is not a symbol!');
                for (let n in G) if (G[n] === t) return n;
            },
            useSetter: function() {
                K = !0;
            },
            useSimple: function() {
                K = !1;
            },
        }),
            u(u.S + u.F * !H, 'Object', {
                create: function(t, n) {
                    return void 0 === n ? _(t) : Q(_(t), n);
                },
                defineProperty: q,
                defineProperties: Q,
                getOwnPropertyDescriptor: Z,
                getOwnPropertyNames: $,
                getOwnPropertySymbols: tt,
            });
        let it = a(function() {
            E.f(1);
        });
        u(u.S + u.F * it, 'Object', {
            getOwnPropertySymbols: function(t) {
                return E.f(S(t));
            },
        }),
            C &&
                u(
                    u.S +
                        u.F *
                            (!H ||
                                a(function() {
                                    let t = A();
                                    return (
                                        '[null]' != N([t]) ||
                                        '{}' != N({ a: t }) ||
                                        '{}' != N(Object(t))
                                    );
                                })),
                    'JSON',
                    {
                        stringify: function(t) {
                            for (
                                var n, r, e = [t], o = 1;
                                arguments.length > o;

                            )
                                e.push(arguments[o++]);
                            if (
                                ((r = n = e[1]),
                                (m(n) || void 0 !== t) && !Y(t))
                            )
                                return (
                                    b(n) ||
                                        (n = function(t, n) {
                                            if (
                                                ('function' == typeof r &&
                                                    (n = r.call(this, t, n)),
                                                !Y(n))
                                            )
                                                return n;
                                        }),
                                    (e[1] = n),
                                    N.apply(C, e)
                                );
                        },
                    },
                ),
            A.prototype[I] || r(4)(A.prototype, I, A.prototype.valueOf),
            p(A, 'Symbol'),
            p(Math, 'Math', !0),
            p(e.JSON, 'JSON', !0);
    },
    function(t, n, r) {
        var e = r(14)('meta'),
            o = r(9),
            i = r(3),
            u = r(1).f,
            c = 0,
            f =
                Object.isExtensible ||
                function() {
                    return !0;
                },
            a = !r(12)(function() {
                return f(Object.preventExtensions({}));
            }),
            s = function(t) {
                u(t, e, { value: { i: 'O' + ++c, w: {} } });
            },
            p = (t.exports = {
                KEY: e,
                NEED: !1,
                fastKey: function(t, n) {
                    if (!o(t))
                        return 'symbol' == typeof t
                            ? t
                            : ('string' == typeof t ? 'S' : 'P') + t;
                    if (!i(t, e)) {
                        if (!f(t)) return 'F';
                        if (!n) return 'E';
                        s(t);
                    }
                    return t[e].i;
                },
                getWeak: function(t, n) {
                    if (!i(t, e)) {
                        if (!f(t)) return !0;
                        if (!n) return !1;
                        s(t);
                    }
                    return t[e].w;
                },
                onFreeze: function(t) {
                    return a && p.NEED && f(t) && !i(t, e) && s(t), t;
                },
            });
    },
    function(t, n, r) {
        let e = r(26),
            o = r(42),
            i = r(31);
        t.exports = function(t) {
            let n = e(t),
                r = o.f;
            if (r)
                for (var u, c = r(t), f = i.f, a = 0; c.length > a; )
                    f.call(t, (u = c[a++])) && n.push(u);
            return n;
        };
    },
    function(t, n, r) {
        let e = r(41);
        t.exports = Object('z').propertyIsEnumerable(0)
            ? Object
            : function(t) {
                  return 'String' == e(t) ? t.split('') : Object(t);
              };
    },
    function(t, n, r) {
        let e = r(6),
            o = r(70),
            i = r(71);
        t.exports = function(t) {
            return function(n, r, u) {
                let c,
                    f = e(n),
                    a = o(f.length),
                    s = i(u, a);
                if (t && r != r) {
                    for (; a > s; ) if ((c = f[s++]) != c) return !0;
                } else
                    for (; a > s; s++)
                        if ((t || s in f) && f[s] === r) return t || s || 0;
                return !t && -1;
            };
        };
    },
    function(t, n, r) {
        let e = r(28),
            o = Math.min;
        t.exports = function(t) {
            return t > 0 ? o(e(t), 9007199254740991) : 0;
        };
    },
    function(t, n, r) {
        let e = r(28),
            o = Math.max,
            i = Math.min;
        t.exports = function(t, n) {
            return (t = e(t)) < 0 ? o(t + n, 0) : i(t, n);
        };
    },
    function(t, n, r) {
        let e = r(41);
        t.exports =
            Array.isArray ||
            function(t) {
                return 'Array' == e(t);
            };
    },
    function(t, n, r) {
        let e = r(1),
            o = r(11),
            i = r(26);
        t.exports = r(2)
            ? Object.defineProperties
            : function(t, n) {
                  o(t);
                  for (var r, u = i(n), c = u.length, f = 0; c > f; )
                      e.f(t, (r = u[f++]), n[r]);
                  return t;
              };
    },
    function(t, n, r) {
        let e = r(0).document;
        t.exports = e && e.documentElement;
    },
    function(t, n, r) {
        let e = r(6),
            o = r(45).f,
            i = {}.toString,
            u =
                'object' == typeof window &&
                window &&
                Object.getOwnPropertyNames
                    ? Object.getOwnPropertyNames(window)
                    : [];
        t.exports.f = function(t) {
            return u && '[object Window]' == i.call(t)
                ? (function(t) {
                      try {
                          return o(t);
                      } catch (t) {
                          return u.slice();
                      }
                  })(t)
                : o(e(t));
        };
    },
    function(t, n, r) {
        let e = r(31),
            o = r(13),
            i = r(6),
            u = r(22),
            c = r(3),
            f = r(37),
            a = Object.getOwnPropertyDescriptor;
        n.f = r(2)
            ? a
            : function(t, n) {
                  if (((t = i(t)), (n = u(n, !0)), f))
                      try {
                          return a(t, n);
                      } catch (t) {}
                  if (c(t, n)) return o(!e.f.call(t, n), t[n]);
              };
    },
    function(t, n) {},
    function(t, n, r) {
        r(25)('asyncIterator');
    },
    function(t, n, r) {
        r(25)('observable');
    },
    function(t, n, r) {
        'use strict';
        let e = r(81)(!0);
        r(46)(
            String,
            'String',
            function(t) {
                (this._t = String(t)), (this._i = 0);
            },
            function() {
                let t,
                    n = this._t,
                    r = this._i;
                return r >= n.length
                    ? { value: void 0, done: !0 }
                    : ((t = e(n, r)),
                      (this._i += t.length),
                      { value: t, done: !1 });
            },
        );
    },
    function(t, n, r) {
        let e = r(28),
            o = r(27);
        t.exports = function(t) {
            return function(n, r) {
                let i,
                    u,
                    c = String(o(n)),
                    f = e(r),
                    a = c.length;
                return f < 0 || f >= a
                    ? t
                        ? ''
                        : void 0
                    : (i = c.charCodeAt(f)) < 55296 ||
                      i > 56319 ||
                      f + 1 === a ||
                      (u = c.charCodeAt(f + 1)) < 56320 ||
                      u > 57343
                    ? t
                        ? c.charAt(f)
                        : i
                    : t
                    ? c.slice(f, f + 2)
                    : u - 56320 + ((i - 55296) << 10) + 65536;
            };
        };
    },
    function(t, n, r) {
        'use strict';
        let e = r(44),
            o = r(13),
            i = r(24),
            u = {};
        r(4)(u, r(5)('iterator'), function() {
            return this;
        }),
            (t.exports = function(t, n, r) {
                (t.prototype = e(u, { next: o(1, r) })), i(t, n + ' Iterator');
            });
    },
    function(t, n, r) {
        let e = r(3),
            o = r(43),
            i = r(29)('IE_PROTO'),
            u = Object.prototype;
        t.exports =
            Object.getPrototypeOf ||
            function(t) {
                return (
                    (t = o(t)),
                    e(t, i)
                        ? t[i]
                        : 'function' == typeof t.constructor &&
                          t instanceof t.constructor
                        ? t.constructor.prototype
                        : t instanceof Object
                        ? u
                        : null
                );
            };
    },
    function(t, n, r) {
        r(85);
        for (
            let e = r(0),
                o = r(4),
                i = r(32),
                u = r(5)('toStringTag'),
                c = 'CSSRuleList,CSSStyleDeclaration,CSSValueList,ClientRectList,DOMRectList,DOMStringList,DOMTokenList,DataTransferItemList,FileList,HTMLAllCollection,HTMLCollection,HTMLFormElement,HTMLSelectElement,MediaList,MimeTypeArray,NamedNodeMap,NodeList,PaintRequestList,Plugin,PluginArray,SVGLengthList,SVGNumberList,SVGPathSegList,SVGPointList,SVGStringList,SVGTransformList,SourceBufferList,StyleSheetList,TextTrackCueList,TextTrackList,TouchList'.split(
                    ',',
                ),
                f = 0;
            f < c.length;
            f++
        ) {
            let a = c[f],
                s = e[a],
                p = s && s.prototype;
            p && !p[u] && o(p, u, a), (i[a] = i.Array);
        }
    },
    function(t, n, r) {
        'use strict';
        let e = r(86),
            o = r(87),
            i = r(32),
            u = r(6);
        (t.exports = r(46)(
            Array,
            'Array',
            function(t, n) {
                (this._t = u(t)), (this._i = 0), (this._k = n);
            },
            function() {
                let t = this._t,
                    n = this._k,
                    r = this._i++;
                return !t || r >= t.length
                    ? ((this._t = void 0), o(1))
                    : o(0, 'keys' == n ? r : 'values' == n ? t[r] : [r, t[r]]);
            },
            'values',
        )),
            (i.Arguments = i.Array),
            e('keys'),
            e('values'),
            e('entries');
    },
    function(t, n) {
        t.exports = function() {};
    },
    function(t, n) {
        t.exports = function(t, n) {
            return { value: n, done: !!t };
        };
    },
    function(t, n, r) {
        'use strict';
        r.r(n);
        r(49);
        let e = r(33),
            o = r.n(e),
            i = r(10),
            u = r.n(i),
            c = r(47),
            f = r.n(c);
        function a(t) {
            return (a =
                'function' == typeof u.a && 'symbol' == typeof f.a
                    ? function(t) {
                          return typeof t;
                      }
                    : function(t) {
                          return t &&
                              'function' == typeof u.a &&
                              t.constructor === u.a &&
                              t !== u.a.prototype
                              ? 'symbol'
                              : typeof t;
                      })(t);
        }
        let s = r(48),
            p = r.n(s);
        function l(t) {
            let n = (function(t, n) {
                if ('object' !== a(t) || null === t) return t;
                let r = t[p.a];
                if (void 0 !== r) {
                    let e = r.call(t, n || 'default');
                    if ('object' !== a(e)) return e;
                    throw new TypeError(
                        '@@toPrimitive must return a primitive value.',
                    );
                }
                return ('string' === n ? String : Number)(t);
            })(t, 'string');
            return 'symbol' === a(n) ? n : String(n);
        }
        function y(t, n) {
            for (let r = 0; r < n.length; r++) {
                let e = n[r];
                (e.enumerable = e.enumerable || !1),
                    (e.configurable = !0),
                    'value' in e && (e.writable = !0),
                    o()(t, l(e.key), e);
            }
        }
        let v = (function() {
            function t(n) {
                !(function(t, n) {
                    if (!(t instanceof n))
                        throw new TypeError(
                            'Cannot call a class as a function',
                        );
                })(this, t);
                let r = n.language || 'zh-CN';
                (this.klook_test_host =
                    n.klook_test_host || 'https://agent.fws.klooktest.io'),
                    (this.languagePath = 'en' === r ? '' : '/'.concat(r)),
                    (this.env = n.env),
                    (this.host =
                        'production' !== this.env
                            ? this.klook_test_host
                            : 'https://klook.klktech.cn');
            }
            let n, r, e;
            return (
                (n = t),
                (r = [
                    {
                        key: 'auth',
                        value: function(t) {
                            let n = t.state,
                                r = t.redirect_url,
                                e = new URL(
                                    ''
                                        .concat(this.host)
                                        .concat(this.languagePath),
                                );
                            e.pathname += '/auth/middleware';
                            let o = new URLSearchParams({
                                state: n,
                                auth_redirect_url: r,
                            });
                            (e.search = o.toString()),
                                (window.location.href = e.toString());
                        },
                    },
                ]) && y(n.prototype, r),
                e && y(n, e),
                o()(n, 'prototype', { writable: !1 }),
                t
            );
        })();
        window.AMP_OAUTH_SDK = v;
    },
]);
