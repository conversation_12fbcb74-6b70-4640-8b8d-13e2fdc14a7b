#!/bin/bash

# abort on errors
. ~/.nvm/nvm.sh

#NODE_VERSION="v12.22.12"
NODE_VERSION="v16.20.0"
nvm install ${NODE_VERSION}


function_copy(){
    rsync -r --exclude='node_modules' ./ $target_dir
    if [ "$?" = "1" ]; then
        echo "cp error and exit"
        exit 1
    fi
}


function_checkout_branch(){
    git checkout -- .
    git fetch
    git checkout $branch
    if [ "$?" = "1" ]; then
        echo "checkout error and exit"
        exit 1
    fi
}

function_build(){
    nvm use ${NODE_VERSION}
    npm ci
    npm rebuild node-sass
    npm run build:testing
    function_copy
    time_now=`date "+%Y-%m-%d %H:%M:%S"`
    commit_hash=`git log --pretty=oneline | head -n 1`
    echo "[$time_now] [build] branch=> $branch, hash => $commit_hash success " >> /tmp/.klook_agent_log
}

function_check_cairo() {
  unamestr=$(uname)
  if [[ "$unamestr" == 'Linux' ]]; then
    yum install cairo-devel libjpeg-turbo-devel giflib-devel -y
  elif [[ "$unamestr" == '<PERSON>' ]]; then
    brew install pkg-config cairo libpng jpeg giflib
  fi
}

function_check_supervisor() {
  echo "check supervisor conf "
  cat >/etc/supervisor/conf.d/${namespace}_${app}.conf <<EOF
[program:${namespace}_${app}]
command=npm run start:test
environment=PATH=${HOME}/.nvm/versions/node/${NODE_VERSION}/bin:%(ENV_PATH)s
directory=${target_dir}
autorestart=false
autostart=true
stopasgroup=true
killasgroup=true
stdout_logfile=/opt/logs/stdout_${namespace}_${app}.log
stderr_logfile=/opt/logs/stderr_${namespace}_${app}.log
EOF
  supervisorctl update
}


function_deploy(){
   function_check_cairo
   function_start_web
}

function_start_web() {
  nvm install ${NODE_VERSION}
  nvm use ${NODE_VERSION}
  npm install --production
  supervisorctl restart ${namespace}_${app}
}


function_change_node_version(){

    if [ $? -gt 0 ]; then
        . ~/.nvm/nvm.sh
    fi

    if [ "$NODE_ENV" = "test" ]; then
        node_env=$NODE_ENV
    fi

    echo "当前nvm版本"
    nvm --version

    nvm install ${NODE_VERSION}
    nvm use ${NODE_VERSION}

    echo "当前NODE_ENV"
    echo $node_env

    echo "当前node版本"
    node -v

    echo "当前npm版本"
    npm -v
}

cmd=$1 # build deploy
target_dir=$2 # test env dir
branch=$3 # branch name
node_env='test'

# 服务名
app=node
# 命名空间
namespace=klook-agent-web


if [ -z "$branch" ]; then
    echo "\033[31mthe branch or tag or commit is null that default origin/master.\033[0m"
    branch='origin/master'
fi

function_change_node_version

if [ "$cmd" = "build" ]; then
    function_checkout_branch
    function_build
elif [ "$cmd" = "deploy" ]; then
    function_check_supervisor
    function_deploy
fi

echo "------ Last Commit: -------"
git status | head -1
git show -s --date=relative | head -3
