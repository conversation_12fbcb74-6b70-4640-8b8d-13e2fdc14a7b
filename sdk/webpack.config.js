const path = require('path');

// npm run amp-sdk -- --amp_sdk_version 1.0.3
//
const getSdkVersion = function() {
    const argv = process.argv || '';
    const sdkFlagIndex = argv.indexOf('--amp_sdk_version');

    const version = sdkFlagIndex === -1 ? 0 : argv[sdkFlagIndex + 1];

    return version;
};
const SDK_VERSION = getSdkVersion();

module.exports = {
    mode: 'production',
    entry: '/sdk/amp_oauth2_sdk.js',
    output: {
        path: path.resolve(__dirname, '../public/sdk'),
        filename: SDK_VERSION
            ? `klook-amp-oauth2-sdk.${SDK_VERSION}.js`
            : `klook-amp-oauth2-sdk.[hash].js`,
    },
    module: {
        rules: [
            {
                test: /\.js$/,
                exclude: /node_modules/,
                loader: 'babel-loader',
                options: {
                    presets: ['@babel/preset-env'],
                    plugins: ['@babel/plugin-transform-runtime'],
                },
            },
        ],
    },
};
