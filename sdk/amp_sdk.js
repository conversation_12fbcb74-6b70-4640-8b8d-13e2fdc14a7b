//AMP SDK文档 https://klook.slab.com/posts/amp%E5%B9%B3%E5%8F%B0sdk%E6%96%87%E6%A1%A3-76qkyghz

const getEnvNum = url => {
    if (url.includes('agent')) {
        return url;
    } else {
        return url.replace('/t', '/agent');
    }
};
// 这里的env是APP_ENV 只有三个值： development｜production｜testing
const envMap = {
    development: window.location.origin,
    production: window.location.origin,
    testing: getEnvNum(window.location.origin),
};

const transLanguage = {
    'zh-HK': 'zh-TW',
    'en-US': '',
};

const AMP_JS_SDK = class {
    constructor(options) {
        const lang = transLanguage[options.language] || options.language || '';
        const languagePath = lang === 'en' || !lang ? '' : `/${lang}`;
        this.project = options.project || 'Vertical';
        this.path = `${envMap[options.env || 'production']}${languagePath}`;
    }

    init(info) {
        console.log(info);
    }

    login(redirectUrl) {
        window.location.href = `${
            this.path
        }/signin?continue=${encodeURIComponent(redirectUrl)}`;
    }

    payResult(order_no) {
        window.location.href = `${this.path}/pay/result?orderid=${order_no}`;
    }

    pay(info) {
        const {
            orderNo,
            settlementType,
            entrance,
            upgrade,
            redirectURL,
        } = info;
        window.location.href = `${this.path}/pay/middleware/vertical?order_no=${orderNo}&booking_type=${this.project}&settlement_type=${settlementType}&entrance=${entrance}&upgrade=${upgrade}&redirect_url=${redirectURL}`;
    }
};

function AMPSDK(options) {
    const amp = new AMP_JS_SDK(options);
    return amp;
}

export default AMPSDK;
