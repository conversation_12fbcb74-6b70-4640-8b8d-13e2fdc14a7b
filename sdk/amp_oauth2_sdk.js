// eslint-disable-next-line no-unused-vars
class AMP_OAUTH_SDK {
    constructor(options) {
        const lang = options.language || 'zh-CN';
        this.klook_test_host =
            options.klook_test_host || 'https://agent.fws.klooktest.io';
        this.languagePath = lang === 'en' ? '' : `/${lang}`;
        this.env = options.env;
        this.host =
            this.env !== 'production'
                ? this.klook_test_host
                : 'https://klook.klktech.cn';
    }
    auth({ state, redirect_url }) {
        const host = new URL(`${this.host}${this.languagePath}`);
        host.pathname += '/auth/middleware';
        const params = new URLSearchParams({
            state: state,
            auth_redirect_url: redirect_url,
        });
        host.search = params.toString();
        window.location.href = host.toString();
    }
}

window.AMP_OAUTH_SDK = AMP_OAUTH_SDK;
