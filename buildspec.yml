version: 0.2

env:
  variables:
    # 定义镜像名字
    AWS_DEFAULT_REGION: "ap-southeast-1"
    DOCKER_IMAGE: "klook-agent-web/node"

    #新增nginx镜像名字
    NGINX_DOCKER_IMAGE: "klook-agent-web/nginx"

  # @Orvice 在 codebuild 里面做下面两行的配置

  # parameter-store:
  #   S3_BUCKET: "klook-staticfile/dist_web/"

phases:
  install:
    runtime-versions:
      docker: 18
  pre_build:
    commands:
      - echo Logging in to Amazon ECR...
      - $(aws ecr get-login --no-include-email --region $AWS_DEFAULT_REGION)
      - export BUILD_TIME=`date +%FT%T%z`
  build:
    commands:
      - export AWS_ACCOUNT_ID=$(echo $CODEBUILD_BUILD_ARN | grep -oP '\d{12}')
      - echo $CODEBUILD_BUILD_ARN $AWS_ACCOUNT_ID
      - echo $CODEBUILD_WEBHOOK_TRIGGER $BUILD_TIME
      - npm install
      - npm run build
      - aws s3 cp ./dist/ s3://$S3_BUCKET/klook-agent-web/dist --recursive
      - ./build_docker_image.sh
  post_build:
    commands:
      - echo Build completed on `date`
