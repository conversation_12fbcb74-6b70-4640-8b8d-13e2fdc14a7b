     # pdf凭证
     location /upload_voucher/ {
        proxy_http_version 1.1;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header Host 'www.klook.com';
        proxy_pass http://fileagentserv.klook-email.svc.cluster.local:8080;
     }


     #  凭证图片[https://klook.klktech.com/qrcode/xxx.png]
     location ^~ /qrcode/  {
         proxy_http_version 1.1;
         proxy_set_header X-Real-IP $remote_addr;
         proxy_set_header Host 'www.klook.com';
         proxy_pass http://fileagentserv.klook-email.svc.cluster.local:8080;
     }

    # 新凭证(exp 凭证)
    location ~ ^(/|/en-US/|/zh-CN/|/zh-HK/|/zh-TW/|/ko/|/th/|/ms-MY/)(voucher-new|web3\/voucher-for-pdf)/ {
       proxy_http_version 1.1;
       proxy_set_header X-Real-IP $remote_addr;
       proxy_set_header Host 'vouchernode.klook-voucher-web.svc';
       proxy_pass http://vouchernode.klook-voucher-web.svc.cluster.local:8080;
    }

    # 凭证(PTP 凭证)
     location ~ ^(/|/en-US/|/zh-CN/|/zh-HK/|/zh-TW/|/ko/|/th/|/ms-MY/)(ptp\/dynamic_voucher)/ {
        proxy_http_version 1.1;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header Host 'transportnuxt.klook-web.svc';
        proxy_pass http://transportnuxt.klook-web.svc.cluster.local:8080;
     }

     # 老凭证
        location ~ ^(/|/en-US/|/zh-CN/|/zh-HK/|/zh-TW/|/ko/|/th/|/ms-MY/)(voucher|web3)/ {
           proxy_http_version 1.1;
           proxy_set_header X-Real-IP $remote_addr;
           proxy_set_header Host 'newwebnode.klook-new-web.svc';
           proxy_pass http://newwebnode.klook-new-web.svc.cluster.local:8080;
        }


    #dist_web走node
    location ~ /s_v[a-zA-Z0-9]+/dist_web/ {
       proxy_set_header Host $host;
       include cross_origin.params;
       proxy_http_version 1.1;
       proxy_pass http://node.klook-agent-web.svc.cluster.local:8080;
    }
    location /static/ {
       proxy_set_header Host $host;
       expires 365d;
       include cross_origin.params;
       proxy_http_version 1.1;
       proxy_pass http://node.klook-agent-web.svc.cluster.local:8080;
    }
    location ^~ /s/dist_web/ {
       proxy_set_header Host $host;
       expires 365d;
       include cross_origin.params;
       proxy_http_version 1.1;
       proxy_pass http://node.klook-agent-web.svc.cluster.local:8080;
    }



    location ~ ^/v1/(search) {
        include cross_origin.params;
        proxy_http_version 1.1;
        proxy_pass $upstream_internal_api;
    }

