server {

    #Todos 报 no "ssl_certificate" is defined for the "listen ... ssl"，先注释，目前的理解是在内网访问只需要80就好
    # listen      ************:443 ssl; #*************
    #Todos 报 bind() to ************:80 failed (99: Cannot assign requested address)，先注释，（外面elb如何只想启动的nginx？）
    # listen      ************:80;

    listen 8080;

    server_name  klook.klktech.com
                 bania.travel
                 jtrasia.klktech.com
                 klktech-fat2.fat.klooktest.cn
                 klktech-fat1.fat.klooktest.cn
                 klook.klktech.cn
                 agent.fws.klooktest.io
                 klook.stage.klktech.com
                 klook.stage.klktech.cn
                 klktech-klook-*********.ap-southeast-1.elb.amazonaws.com
                 internal-klktech-klook-internal-*********.ap-southeast-1.elb.amazonaws.com;
                 
    include gzip_params;

    # 一些上游地址
    resolver ************* ***********  valid=10s;
    set $upstream_agent_backend http://agentwebserv.klook-order.svc.cluster.local:8080;
    set $upstream_hotel_api_backend http://hotelapiserv.klook-hotel.svc.cluster.local:8080;
    set $upstream_toc_backend http://usrcommsrv.klook-appapi.svc.cluster.local:8080;
    set $upstream_user_backend http://userserv.klook-user.svc.cluster.local:8080;
    set $upstream_web_backend http://websrv.klook-appapi.svc.cluster.local:8080;
    set $upstream_order_backend http://appserv.klook-order.svc.cluster.local:8080;
    set $upstream_internal_api http://internal-api.klook.io;
    # set $upstream_elb_ns http://elb-ns.klook.io;
    set $upstream_ptp_backend http://np2pbffserv.klook-np2pbff.svc.cluster.local:8080;
    set $upstream_user_profile_backend http://userprofileserv.klook-martech.svc.cluster.local:8080;


    include block.conf;

    # 另外一个文件
    include klook-jv-agent-flickket.conf;

    proxy_intercept_errors on;

    # robots.txt for search engine

    location /robots.txt {
        alias /var/www/robots_klktech.txt;
    }

    location /google7a280e78d74b3849.html {
        alias /var/www/google7a280e78d74b3849.html;
    }

    location ~ ^/(favicon.ico|apple-touch-icon-precomposed.png) {
        log_not_found off;
        access_log off;
        return 204;
    }

    # hotel qiankun 子应用
    location /klook-hotel-subweb/ {
        proxy_pass http://nginx.klook-hotel-subweb.svc.cluster.local:8080;
        proxy_http_version 1.1;
    }

    location /Agent/assets {
        # TODO: 需要放到合适的地方
        alias /var/www/assets;
    }

    location ~ /v\d+/agentwebserv/ {
        proxy_pass $upstream_agent_backend;
        proxy_http_version 1.1;
        proxy_set_header Host 'agentwebserv.klook-order';
    }


    location ~ /v\d+/hotelapiserv/ {
        proxy_pass $upstream_hotel_api_backend;
        proxy_http_version 1.1;
        proxy_set_header Host 'hotelapiserv.klook-hotel';
    }


    location ~ /v\d+/usrcsrv/ {
        proxy_pass $upstream_toc_backend;
        proxy_http_version 1.1;
        proxy_set_header Host 'usrcommsrv.klook-appapi';
    }

    location ~ /v\d+/userserv/ {
        proxy_pass $upstream_user_backend;
        proxy_http_version 1.1;
        proxy_set_header Host 'userserv.klook-user';
    }

    location ~ /v1/websrv/ {
        proxy_pass $upstream_web_backend;
        proxy_http_version 1.1;
        proxy_set_header Host 'websrv.klook-appapi';
     }

    location ~ /v1/order/ {
        proxy_pass $upstream_order_backend;
        proxy_http_version 1.1;
        proxy_set_header Host 'appserv.klook-order';
     }

     location ~ /v1/fileagentserv/ {
        proxy_http_version 1.1;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header Host 'www.klook.com';
        proxy_pass http://fileagentserv.klook-email.svc.cluster.local:8080;
     }

     # PTP 动态凭证
     location ~ /v\d+/np2pbffserv/ {
         proxy_pass $upstream_ptp_backend;
         proxy_http_version 1.1;
         proxy_set_header Host 'np2pbffserv.klook-np2pbff';
     }

     location ~ /v\d+/userprofileserv/ {
         proxy_pass $upstream_user_profile_backend;
         proxy_http_version 1.1;
         proxy_set_header Host 'userprofileserv.klook-martech';
     }


    location / {
        proxy_set_header X-Klook-Request-Source “agent”;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-Proto https;
        proxy_pass http://node.klook-agent-web.svc.cluster.local:8080;
        proxy_http_version 1.1;
       add_header X-Frame-Options sameorigin always;
    }

    #!!! IMPORTANT !!! We need to hide the password file from prying eyes
    # This will deny access to any hidden file (beginning with a .period)
    location ~ /\. { deny  all; }
}
