error_log  /var/log/nginx/error.log warn;
pid        /var/run/nginx.pid;

events {
    worker_connections  2048;
    multi_accept on;
    use epoll;
}


http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;
    server_names_hash_bucket_size 128;# http://nginx.org/en/docs/http/server_names.html

    log_format  main_ext  '$remote_addr - $remote_user [$time_local] "$request" '
                      '$status $body_bytes_sent "$http_referer" '
                      '"$http_user_agent" "$http_x_forwarded_for" '
                      '"$host" sn="$server_name" '
                      'rt=$request_time '
                      'ua="$upstream_addr" us="$upstream_status" '
                      'ut="$upstream_response_time" ul="$upstream_response_length" '
                      'cs="$upstream_cache_status" uct="$upstream_connect_time"' ;

    access_log  /var/log/nginx/access.log  main_ext;

    sendfile        on;
    #tcp_nopush     on;

    keepalive_timeout  65;

    #allow underscore in header
    underscores_in_headers on;

    include /etc/nginx/conf.d/*.conf;
    server_tokens off;
}
