 gzip on;
    #gzip_min_length 1k;
    gzip_buffers 4 16k;
    #gzip_http_version 1.0;
    gzip_comp_level 2;
    gzip_types text/plain application/javascript application/x-javascript text/css application/xml text/javascript application/x-httpd-php image/jpeg image/gif image/png application/json image/svg+xml;
    gzip_vary on;
    gzip_disable "MSIE [1-6]\.";
    gzip_proxied any;
######## Max upload file size
    client_max_body_size 20m;
    #if set to on, it will discard _pt header
    underscores_in_headers on;
    ignore_invalid_headers off;
