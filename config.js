'use strict';

/**
 * 拥有多语言文件的语言
 */
const supportLocales = {
    en: { iso: 'en-US' },
    'zh-CN': { iso: 'zh-CN' },
    'zh-TW': { iso: 'zh-TW' },
    ko: { iso: 'ko-KR' },
};

const backendSupportLocales = ['en', 'zh-CN', 'zh-TW'];

/**
 * 平台所显示的所有多语言列表，部分语言没有对应的多语言文件则使用en.json
 */
const locales = {
    en: { iso: 'en-US' },
    'zh-CN': { iso: 'zh-CN' },
    'zh-TW': { iso: 'zh-TW' },
    ko: { iso: 'ko-KR' },
    'en-AU': { iso: 'en-AU' },
    'en-CA': { iso: 'en-CA' },
    'en-HK': { iso: 'en-HK' },
    'en-IN': { iso: 'en-IN' },
    'en-MY': { iso: 'en-MY' },
    'en-NZ': { iso: 'en-NZ' },
    'en-PH': { iso: 'en-PH' },
    'en-SG': { iso: 'en-SG' },
    'en-BS': { iso: 'en-BS' },
    'en-GB': { iso: 'en-GB' },
    ja: { iso: 'ja-JP' },
    th: { iso: 'th-TH' },
    id: { iso: 'id-ID' },
    fr: { iso: 'fr-FR' },
    es: { iso: 'es-ES' },
    de: { iso: 'de-DE' },
    it: { iso: 'it-IT' },
    ru: { iso: 'ru-RU' },
    vi: { iso: 'vi-VN' },
};

const defaultLocale = 'en';

/**
 * 支持的货币类型，key表示Country Code
 * AMP增加 结算货币为INR,NZD,THB,VND,AUD,EUR,GBP,IDR（和C端汇率保持一致）
 */

const CNCurrencies = {
    CN: {
        currency: 'CNY',
        currencySymbol: '¥',
        currencyName: 'global.currency.CNY',
    },
};

const AllCurrencies = {
    CN: {
        currency: 'CNY',
        currencySymbol: '¥',
        currencyName: 'global.currency.CNY',
    },
    HK: {
        currency: 'HKD',
        currencySymbol: 'HK$',
        currencyName: 'global.currency.HKD',
    },
    SG: {
        currency: 'SGD',
        currencySymbol: 'S$',
        currencyName: 'global.currency.SGD',
    },
    TW: {
        currency: 'TWD',
        currencySymbol: 'NT$',
        currencyName: 'global.currency.TWD',
    },
    US: {
        currency: 'USD',
        currencySymbol: 'US$',
        currencyName: 'global.currency.USD',
    },
    JP: {
        currency: 'JPY',
        currencySymbol: '¥',
        currencyName: 'global.currency.JPY',
    },
    KR: {
        currency: 'KRW',
        currencySymbol: '₩',
        currencyName: 'global.currency.KRW',
    },
    MY: {
        currency: 'MYR',
        currencySymbol: 'RM',
        currencyName: 'global.currency.MYR',
    },
    ID: {
        currency: 'IDR',
        currencySymbol: 'Rp',
        currencyName: 'global.currency.IDR',
    },
    TH: {
        currency: 'THB',
        currencySymbol: '฿',
        currencyName: 'global.currency.THB',
    },
    VN: {
        currency: 'VND',
        currencySymbol: '₫',
        currencyName: 'global.currency.VND',
    },

    IN: {
        currency: 'INR',
        currencySymbol: '₹',
        currencyName: 'global.currency.INR',
    },
    NZ: {
        currency: 'NZD',
        currencySymbol: 'NZ$',
        currencyName: 'global.currency.NZD',
    },
    AU: {
        currency: 'AUD',
        currencySymbol: 'AUD',
        currencyName: 'global.currency.AUD',
    },
    GB: {
        currency: 'GBP',
        currencySymbol: '£',
        currencyName: 'global.currency.GBP',
    },
    EU: {
        currency: 'EUR',
        currencySymbol: '€',
        currencyName: 'global.currency.EUR',
    },
};

const SERVER_API_HOST_URL = 'https://klktech394-fat.klooktest.com';
// const SERVER_API_HOST_URL = 'https://agent.fws.klook.io';

const jvMap = {
    '1': 'klook',
    '2': 'jtr',
    '4': 'bania',
    '6': 'klookCN',
};

module.exports = {
    backendSupportLocales,
    supportLocales,
    locales,
    jvMap,
    defaultLocale,
    AllCurrencies,
    CNCurrencies,
    SERVER_API_HOST_URL,
};
