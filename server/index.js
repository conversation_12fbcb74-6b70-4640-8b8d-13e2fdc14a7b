/* eslint-disable import/extensions */
const Koa = require('koa');
const logger = require('koa-logger');
const onerror = require('koa-onerror');
const debug = require('debug')('klook:app');
const { notifyError, isProduction } = require('./utils.js');
require('../init.js');

const app = new Koa();

// Request remote address. Supports X-Forwarded-For when app.proxy is true.
app.proxy = true;

const router = require('./middleware/router.js');

onerror(app);

app.use(require('./middleware/logquery.js')());

app.use(require('./middleware/getRequestId.js'));

app.use(require('./middleware/getUuid.js'));

app.use(require('./middleware/logger.js'));

app.use(logger());

if (!isProduction) {
    app.use(require('./middleware/api.js'));
}

app.use(require('./middleware/static.js'));

app.use(require('./middleware/info.js'));

app.use(router.routes());

app.on('error', (err, ctx) => {
    err.originalUrl = ctx && ctx.originalUrl;
    debug(err);
    if (isProduction) {
        try {
            notifyError(ctx, err);
        } catch (e) {
            debug(e);
        }
    }
});

/**
 * https://nodejs.org/dist/latest-v8.x/docs/api/process.html#process_event_rejectionhandled
 */
process.on('unhandledRejection', (reason, p) => {
    debug('Unhandled Rejection at:', p, 'reason:', reason);
});

if (!module.parent) {
    const port = isProduction ? 8080 : 3006;
    app.listen(port);
    console.log('Server is listening at', port);
} else {
    module.exports = app;
}
