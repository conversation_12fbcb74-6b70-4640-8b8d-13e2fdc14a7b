const send = require('koa-send');
const path = require('path');

const distPath = path.join(__dirname, '../../dist');

/**
 * 静态资源处理，引用：koa-static (https://github.com/koajs/static/blob/master/index.js)
 * @param {*} ctx
 * @param {*} next
 */
module.exports = async (ctx, next) => {
    let done = false;

    if (
        ctx.method === 'HEAD' ||
        (ctx.method === 'GET' && !/\.map$/.test(ctx.path))
    ) {
        try {
            done = await send(ctx, ctx.path, { root: distPath });
        } catch (err) {
            if (err.status !== 404) {
                throw err;
            }
        }
    }

    if (!done) {
        await next();
    }
};
