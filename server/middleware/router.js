/* eslint-disable import/extensions */
const fs = require('fs');
const path = require('path');
const Router = require('koa-router');
const template = require('lodash/template');
const { getKMSClient } = require('@klook/kms-client');
const { getSite } = require('@klook/env-utils');
const {
    APP_ENV,
    LOGQUERY_URL,
    LOGQUERY_URL_V3,
    URL_BASE,
} = require('../config.js');
const { locales, jvMap } = require('../../config.js');

const {
    getCommonHeader,
    rpRequest,
    NODE_ENV,
    isProduction,
    notifyError,
} = require('../utils.js');

let templateCompiled = template(
    fs.readFileSync(path.resolve(__dirname, '../../dist/index.html'), 'utf-8'),
);

const acceptLanguage = lang =>
    ((locales[lang] || {}).iso || 'en-US').replace('-', '_');

// get current site
const market = getSite();

const resetLoginStatus = ctx => {
    ctx.cookies.set('_pt', '', { maxAge: -1, httpOnly: true });
    ctx.cookies.set('encrypted_token', '', {
        maxAge: -1,
        httpOnly: false,
        secure: ctx.secure,
    });
};

const router = new Router();
router.get(
    [
        '/pay/middleware',
        '/signin',
        '/forgetpwd',
        '/signup',
        '/404',
        '/sam/voucher',
    ],
    (ctx, next) => {
        ctx.state.noNeedAuthorize = true;
        return next();
    },
);
/**
 *  健康检查
 */

router.get('/node/pong', ctx => {
    ctx.status = 200;
    return;
});

/**
 * sub account T&C 页面
 */

const conditionsDSTemplate = template(
    fs.readFileSync(
        path.resolve(__dirname, '../../public/conditions-bania.html'),
        'utf-8',
    ),
);
router.get('/conditions/', ctx => {
    ctx.body = conditionsDSTemplate();
});

/**
 * 重置密码
 */
router.get('/reset/password', async (ctx, next) => {
    let { lang, token } = ctx.query;
    //  后端接口返回的langPath格式是ja-JP 但是前端路由的却是ja，所以需要做转换
    if (lang) {
        lang = String(lang).replace('_', '-');
        for (let localesItem in locales) {
            if (lang === locales[localesItem].iso) {
                lang = localesItem;
                break;
            }
        }
        if (!Object.keys(locales).includes(lang)) {
            // 非法lang
            lang = '';
        }
    }
    const langPath = lang && lang !== 'en' ? `/${lang}` : '';

    if (langPath) {
        ctx.status = 302;
        ctx.redirect(`${langPath}/reset/password?token=${token}`);
        return;
    }

    const result = await rpRequest(ctx, {
        uri: `${ctx.state.baseUrl}/v1/agentwebserv/findpassword/token/check?token=${token}`,
        headers: getCommonHeader(ctx),
    });

    if (result && (result.result || {}).is_valid) {
        return next();
    }

    ctx.status = 302;
    ctx.redirect(`${langPath}/signin`);
});

const getUerInfoUrl = (isPrd, baseUrl, userToken) =>
    `${
        isPrd ? 'http://agentcoreserv.klook-order:8080' : baseUrl
    }/v2/agentcoreserv/agent/login/info?token=${userToken}`;

/**
 *  联合登录
 */
router.get('/auth/signin', async ctx => {
    let { client_id, redirect_url } = ctx.query;
    const { lang, langPath, userToken, baseUrl, isPrd } = ctx.state;

    const url_params = `client_id=${client_id}&redirect_url=${redirect_url}`;
    const redirect_login_url = `${langPath}/signin?${url_params}`;

    const redirectToLogin = () => {
        ctx.cookies.set('_pt', '', { maxAge: -1, httpOnly: true });
        ctx.status = 302;
        ctx.redirect(redirect_login_url);
    };

    const headers = Object.assign(
        { 'Accept-Language': acceptLanguage(lang) },
        getCommonHeader(ctx),
    );
    if (!userToken) {
        redirectToLogin();
    } else {
        let userInfoResp = await rpRequest(ctx, {
            uri: getUerInfoUrl(isPrd, baseUrl, userToken),
            headers,
        });
        if (userInfoResp.success) {
            let userCodeResp = await rpRequest(ctx, {
                uri: `${baseUrl}/v1/agentwebserv/auth/code`,
                qs: {
                    client_id,
                    redirect_url,
                },
                headers,
            });

            if (userCodeResp.success && userCodeResp.result) {
                ctx.status = 302;
                redirect_url += !redirect_url.includes('?') ? '?' : '';
                ctx.redirect(
                    `${redirect_url}&code=${userCodeResp.result.code}`,
                );
            } else {
                redirectToLogin();
            }
        } else {
            redirectToLogin();
        }
    }
});

/**
 * 这里的路由范围是否太广了
 */
router.get('/*', async (ctx, next) => {
    ctx.set('Strict-Transport-Security', 'max-age=31536000; includeSubDomains');
    const {
        noNeedAuthorize,
        lang,
        langPath,
        userToken,
        baseUrl,
        isPrd,
    } = ctx.state;
    if (!noNeedAuthorize && !userToken) {
        ctx.status = 302;
        ctx.cookies.set('cookie1', '', { maxAge: -1, httpOnly: false });

        // 如果auth页面 请求的页面失去登陆态，则把请求的路径作为参数保留下来，登陆成功后直接跳转回原来的页面
        const currentPathName = (
            new URL(ctx.request.url, ctx.request.origin) || {}
        ).pathname;
        if (
            ['/auth/middleware', '/auth/middleware/'].includes(currentPathName)
        ) {
            const continueUrl = new URL(
                ctx.request.url,
                ctx.request.origin,
            ).toString();
            const params = new URLSearchParams({ continue: continueUrl });
            const redirectUrl = new URL(
                `${langPath}/signin`,
                ctx.request.origin,
            );
            redirectUrl.search = params.toString();
            ctx.redirect(redirectUrl.toString());
            return;
        }
        ctx.redirect(`${langPath}/signin`);
        return;
    }

    const request = uri =>
        rpRequest(ctx, {
            uri,
            headers: Object.assign(
                { 'Accept-Language': acceptLanguage(lang) },
                getCommonHeader(ctx),
            ),
        }).then(res => (res && res.success ? res.result : null));

    // 这里取数据、填充
    let ranges =
        (await request(
            `${
                isPrd ? 'http://prodbasesrv.klook-product:8080' : baseUrl
            }/v1/prodbasesrv/ranges`,
        )) || {};

    // URL_BASE 配置在kv中的
    let urlBase = URL_BASE;
    const host = ctx.header['x-klook-host'] || ctx.host;

    if (host.includes('jtrasia')) {
        urlBase = 'jtrasia';
    }
    console.log(urlBase, 'urlBase');

    let agentInfo =
        (await request(
            `${baseUrl}/v1/agentwebserv/jv/info${
                isPrd ? `?url_base=${urlBase}` : ''
            }`,
        )) || {};
    console.log(agentInfo, 'agentInfo');
    let userInfo = null;
    if (userToken) {
        userInfo = (await request(
            getUerInfoUrl(isPrd, baseUrl, userToken),
        )) || {
            account_type: '',
            agent_category: 'Global',
            agent_type: 0,
            company_name: '测试公司名称test2',
            currency: 'HKD',
            entity: '',
            family_name: 'local_testlastthree',
            first_name: 'local_testfirstwo',
            gender: 'MR',
            id: *********,
            internal: false,
            language: 'zh_CN',
            parent_id: 0,
            permissions: {
                api_list: false,
                balance_lookup: true,
                batch_order: true,
                mashang: false,
            },
            register_name: '<EMAIL>',
            price_tier: 1038,
            joint_venture_id: 1,
            user_id: *********,
            agent_level: 0,
            send_confirm_email: 0,
            send_confirm_sms: 0,
            need_callback: 0,
        };
        /**
         * 取用户信息中，userInfo.id === 0 判断是否已经填写基本信息
         * !==0 --- 按原来进入
         * ===0 --- 跳转进入signup step2步骤，填写基本信息
         */
        if (
            userInfo &&
            userInfo.id === 0 &&
            ctx.originalUrl.indexOf('/signup/step2') === -1 &&
            !noNeedAuthorize
        ) {
            ctx.status = 302;
            ctx.cookies.set('cookie1', '', { maxAge: -1, httpOnly: false });
            ctx.redirect(`${langPath}/signup/step2`);
        }

        if (userInfo) {
            delete userInfo.mobile;
            delete userInfo.avatar;
            delete userInfo.email;
            // 独立收银台token
            // https://klook.larksuite.com/wiki/wikusnu40yReyajPn6NcnljEHag

            // 本地开发不用加密 && encrypted_token 存在的时候不再次加密
            // 灰度情况：klook登陆->打开新的页面从klook登陆到klooks登陆后 直接更新url再次打开klook 此刻 _pt和 encrypted_token都存在
            // 所以登陆klook的时候不会更新encrypted_token，导致加密解密失败
            const env = NODE_ENV;
            if (env !== 'development') {
                const kmsClient = getKMSClient(market);
                const encryptedDataResp = await kmsClient.getEncryptedData(
                    userToken,
                );
                console.log(
                    'encryptedDataResp',
                    JSON.stringify(encryptedDataResp),
                );
                if (encryptedDataResp.success) {
                    const { encryptedData } = encryptedDataResp.result;
                    ctx.cookies.set('encrypted_token', encryptedData, {
                        maxAge: 1000 * 60 * 60 * 24,
                        httpOnly: false,
                        secure: ctx.secure,
                    });
                } else {
                    notifyError(ctx, encryptedDataResp);
                }
            }
        } else {
            resetLoginStatus(ctx);
        }
    } else {
        resetLoginStatus(ctx);
    }

    if (!isProduction) {
        templateCompiled = template(
            fs.readFileSync(
                path.resolve(__dirname, '../../dist/index.html'),
                'utf-8',
            ),
        );
    }

    const jvId = (agentInfo.id || '1').toString();

    const jvName = jvMap[jvId] || 'klook';

    ctx.body = templateCompiled({
        env: `"${APP_ENV}"`,
        node_env: `"${NODE_ENV}"`,
        ranges: JSON.stringify((ranges && ranges.ranges) || []),
        userInfo: JSON.stringify(userInfo || {}),
        agentInfo: JSON.stringify(agentInfo),
        lang: JSON.stringify(lang),
        langPath: JSON.stringify(langPath),
        jvName: `"${jvName}"`,
        argsEnv: `"${process.env.KL_ARGS_ENV}"`,
        market: `"${market}"`,
        logqueryURL: `"${LOGQUERY_URL}"`,
        logqueryURLV3: `"${LOGQUERY_URL_V3}"`,
    });
    return next();
});

module.exports = router;
