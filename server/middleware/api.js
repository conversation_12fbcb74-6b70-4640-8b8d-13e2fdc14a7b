const request = require('request');
const { SERVER_API_HOST_URL } = require('../../config');

const urlReg = /^\/(v1|v2|v3|rest|query|agent|web2|Web2|assets|Agent)\//;

module.exports = async (ctx, next) => {
    if (urlReg.test(ctx.url) || /.ajax$/.test(ctx.path)) {
        const options = {
            uri: ctx.url,
            baseUrl: SERVER_API_HOST_URL,
            gzip: true,
            json: true,
        };

        if (
            ctx.url.startsWith('/v1/agentwebserv/batchorder/download') ||
            ctx.url.startsWith('/v1/agentwebserv/agent/bookings/export') ||
            ctx.url.startsWith('/v1/agentwebserv/agentbatch/bookings/export')
        ) {
            options.encoding = null;
        }
        await new Promise(resolve => {
            ctx.req.pipe(
                request(options, (error, response, body) => {
                    if (error) {
                        throw error;
                    }
                    ctx.set({ 'set-cookie': response.headers['set-cookie'] });
                    ctx.body = error || body;
                    ctx.status = response.statusCode || 200;
                    resolve();
                }),
            );
        });
    } else {
        await next();
    }
};
