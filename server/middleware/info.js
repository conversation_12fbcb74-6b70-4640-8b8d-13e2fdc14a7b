const { locales, defaultLocale } = require('../../config');
const { isProduction, NODE_ENV } = require('../utils');

// eslint-disable-next-line no-useless-escape
const langReg = new RegExp(`^\/(${Object.keys(locales).join('|')})\/`);

const getBaseUrl = ctx => {
    const baseUrlMap = {
        development: ctx.origin,
        test: ctx.origin.replace('http:', 'https:'),
        production: 'http://agentwebserv.klook-order:8080',
    };
    return baseUrlMap[NODE_ENV];
};
module.exports = async (ctx, next) => {
    const langMatches = langReg.exec(ctx.path);
    if (langMatches) {
        ctx.state.lang = langMatches[1];
    } else {
        ctx.state.lang = defaultLocale || 'en';
    }

    ctx.state.langPath =
        ctx.state.lang === defaultLocale ? '' : `/${ctx.state.lang}`;

    ctx.path = ctx.path.slice(ctx.state.langPath.length);

    // ctx.path = '' 然后 ctx.path is null, 原因是koa内部使用 parseurl 来获取 pathname
    if (!ctx.path || ctx.path === '/') {
        ctx.set(
            'Strict-Transport-Security',
            'max-age=31536000; includeSubDomains',
        );
        ctx.status = 301;
        ctx.redirect(`${ctx.state.langPath}/index`);
        return;
    }

    ctx.state.userToken = ctx.query.token || ctx.cookies.get('_pt');
    ctx.state.baseUrl = getBaseUrl(ctx);
    ctx.state.isPrd = isProduction;

    await next();
};
