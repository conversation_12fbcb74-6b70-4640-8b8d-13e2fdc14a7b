const uuid = require('uuid/v4');

module.exports = async (ctx, next) => {
    let agent_device_id = ctx.cookies.get('agent_device_id');
    if (!agent_device_id) {
        agent_device_id = uuid();

        ctx.cookies.set('agent_device_id', agent_device_id, {
            maxAge: 2 * 365 * 24 * 3600 * 1000, // 2年
            httpOnly: false,
            secure: ctx.secure,
        });
    }
    await next();
};
