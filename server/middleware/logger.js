function getMethodByStatus(statusCode) {
    switch (true) {
        case statusCode >= 500:
            return 'error';
        case statusCode >= 400:
            return 'warn';
        case statusCode >= 300:
            return 'info';
        case statusCode >= 200:
            return 'info';
        default:
            return 'info';
    }
}
function getMeta(metas) {
    return Object.assign({}, ...metas);
}

function reqPrint(req, res) {
    let format = `<- ${req.requestId} ${req.method || 'UNKNOW'} %url`;
    let method = 'info';
    const addition = {
        send_bytes: 0,
    };
    print(req, res, 'req', format, method, addition);
}

function resPrint(req, res) {
    let method = getMethodByStatus(res.statusCode);
    let format = `<- ${req.requestId} ${req.method || 'UNKNOW'} %url ${
        res.statusCode
    } %elapsedTime`;
    const addition = {
        http_status: res.statusCode,
        req_status: res.statusCode,
        method: req.method,
        _logger_name_: 'access_log:frontend:node:page',
    };
    print(req, res, 'res', format, method, addition);
}

function print(req, res, type, format, method, addition) {
    const duration = Date.now() - req.startTime;
    let url = req.oldPath || req.url || '';

    format = format
        .replace(/%url/g, url)
        .replace(/%elapsedTime/g, `${duration}ms`);

    const logquery = req.logquery;
    const level = method[0].toUpperCase();
    const date = new Date().toISOString();

    if (logquery) {
        let host = req.headers['x-klook-host'] || req.headers.host || '';
        host = host ? `https://${host}` : '';
        url = `${host}${url}`;

        const meta = getMeta(req.route ? req.route.meta : []) || [];
        const message = JSON.stringify({
            ...addition,
            action: 0,
            _page_: meta.trackedPageName || 'agent_index',
            type,
            url,
            level: 'I',
            method: req.method,
            _duration_: duration,
            request_id: req.requestId,
            send_bytes: res.length || (req.socket || {}).bytesWritten || 0,
            received_bytes: req.length || (req.socket || {}).bytesRead || 0,
            business: 'klook-agent-web',
            description: format,
        });
        logquery.service({
            isMasked: true,
            level: level,
            funcName: 'logger.amp.print',
            message: message,
            tag: 'AMP',
        });
    } else {
        console[method](`${date}${format}`);
    }
}

module.exports = async (ctx, next) => {
    ctx.req.startTime = Date.now();

    reqPrint(ctx.req, ctx.res);

    ctx.res.on('finish', () => {
        resPrint(ctx.req, ctx.res);
    });

    await next();
};
