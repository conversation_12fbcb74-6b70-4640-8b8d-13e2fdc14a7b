const { getCurrentConfig } = require('@klook/kv');

const parseData = k => {
    try {
        return JSON.parse(k);
    } catch (_e) {
        return null;
    }
};

let APP_ENV = 'development';
let logqueryConf = {
    app: true,
    agent: false,
    console: false,
    client: {
        url: 'nats://nlb-nats.dev.klook.io:6222',
        subject: 'log.go.frontend.klook-agent-web.node',
        serviceName: 'klook-agent-web/node',
        dev: true,
    },
};
let LOGQUERY_URL = 'https://log.fws.klooktest.io/v2/frontlogsrv/log/web';
let LOGQUERY_URL_V3 = 'https://log.fws.klooktest.io/v3/frontlogsrv/log/web';
let URL_BASE = '';
let AWS_KMS_KEY_ID = '';

try {
    const kvConfig = getCurrentConfig() || {};
    APP_ENV = kvConfig.APP_ENV || APP_ENV;
    logqueryConf = parseData(kvConfig['klook.logquery']) || logqueryConf;
    LOGQUERY_URL = kvConfig.LOGQUERY_URL || LOGQUERY_URL;
    LOGQUERY_URL_V3 = kvConfig.LOGQUERY_URL_V3 || LOGQUERY_URL_V3;
    URL_BASE = kvConfig.URL_BASE || '';
    AWS_KMS_KEY_ID = kvConfig.AWS_KMS_KEY_ID || '';
    // 注入变量
    process.env.AUTH_TOKEN = kvConfig['AUTH_TOKEN.pw'];
    process.env.SERVER_NAME = 'klook-agent-web/node';
    process.env.AWS_KMS_KEY_ID = AWS_KMS_KEY_ID;
    console.log('agent-web-process-env: ', JSON.stringify(process.env || {}));
} catch (error) {
    console.log(error, 'error');
}

module.exports = {
    APP_ENV,
    LOGQUERY_URL,
    LOGQUERY_URL_V3,
    logqueryConf,
    URL_BASE,
};
