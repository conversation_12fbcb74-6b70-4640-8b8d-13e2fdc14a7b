// 去标加密的key
const MOBILE_KEYS = [
    'mobile',
    'phoneNumber',
    'booking_agent_mobile',
    'phone_number',
];
const EMAIL_KEYS = ['booking_agent_email', 'email'];
const FIRST_NAME_KEYS = [
    'first_name',
    'family_name',
    'firstName',
    'lastName',
    'last_name',
];

// 写在代码备份 获取jv信息的base_url
// const ARGS_APP_TO_JV_MAP = {
//     prod: 'klook',
//     stage: 'klookstgae',
//     cnaliyunstage: 'klookstgaecn',
//     dev: '',
//     awsdev: '',
//     fws: 'agent.fws',
//     fat: '',
//     aliyunprod: 'klookcn',
//     aliyunfat2: 'klktech-fat12.fat',  // fat2
//     aliyunfat: '	klktech-fat1.fat', // fat1
//     seera: 'agent',
//     seeradev: 'klktech.dev',
//     seerastage: 'klktech.stage',
// };

module.exports = {
    MOBILE_KEYS,
    EMAIL_KEYS,
    FIRST_NAME_KEYS,
    // ARGS_APP_TO_JV_MAP,
};
