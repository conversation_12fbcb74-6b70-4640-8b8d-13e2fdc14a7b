/* eslint-disable import/extensions */
const rp = require('request-promise-native');
const debug = require('debug')('klook:utils');
const { MaskUtil } = require('@klook/klk-kcrypto');
const { MOBILE_KEYS, EMAIL_KEYS, FIRST_NAME_KEYS } = require('./constant.js');

const NODE_ENV = process.env.NODE_ENV || 'production';

// NODE_ENV
// 产线/类产线 ：production ： 包括 fws fat1 fat2 stage ptest prod 等等需要走构建的环境
// gcp测试环境，是test

// isProduction： 类产线 & 产线
const isProduction = process.env.NODE_ENV === 'production';
const isDev = NODE_ENV === 'development';

const isObject = val =>
    Object.prototype.toString.call(val) === '[object Object]';

const deepResolveParams = obj => {
    const resolveParam = obj;
    Object.keys(resolveParam).forEach(key => {
        const value = resolveParam[key];

        if (isObject(value)) {
            deepResolveParams(value);
            return;
        }
        if (!value) {
            // do nothing
        } else if (MOBILE_KEYS.includes(key)) {
            resolveParam[key] = MaskUtil.mobile(value);
        } else if (EMAIL_KEYS.includes(key)) {
            resolveParam[key] = MaskUtil.email(value);
        } else if (FIRST_NAME_KEYS.includes(key)) {
            resolveParam[key] = MaskUtil.first_name(value);
        }
    });
};
const resolveParams = params => {
    if (!params) return params;

    const resolveParam = JSON.parse(JSON.stringify(params));
    deepResolveParams(resolveParam);

    return resolveParam;
};
const batchResolveParams = function(params) {
    const resolveParam = Object.assign({}, params);
    if (resolveParam.data) {
        resolveParam.data = resolveParams(resolveParam.data);
    }

    if (resolveParam.params) {
        resolveParam.params = resolveParams(resolveParam.params);
    }
    return resolveParam;
};

/**
 * logquery接入 https://klook.slab.com/posts/amp-%E6%8E%A5%E5%85%A5-klk-logquery-nepfzfq5
 * @param {*} config  level/tag/funcName
 * @param {*} ctx context
 * @param {*} messages error message
 */
const addLogquery = (config, ctx, messages) => {
    const attachments = {
        timestamp: Date.now(),
        level: config.level || 'E',
        message: JSON.stringify(messages),
        tag: config.tag,
        funcName: config.funcName || '',
        isMasked: true,
    };

    if (ctx.req.logquery) {
        ctx.req.logquery.service(attachments);
    } else {
        console.log(JSON.stringify(attachments));
    }
};

/**
 * 错误通知，延用老的逻辑，十分钟内发送slack的次数不能超过十次
 */
let tenMinSendNum = 0,
    tid = null,
    lastSendTime = 0,
    errorQueue = [];
const notifyError = (ctx, error) => {
    if (Date.now() - lastSendTime > 600000) {
        tenMinSendNum = 0;
    }
    let config = {
        level: 'E',
        tag: 'AMP.Node.Request.Error',
    };

    if (tenMinSendNum <= 10) {
        if (errorQueue.length > 10) {
            return;
        }
        errorQueue.push(batchResolveParams(error));
        clearTimeout(tid);

        tid = setTimeout(
            () => {
                addLogquery(config, ctx, errorQueue);
                errorQueue = [];
                lastSendTime = Date.now();
                tenMinSendNum++;
            },
            errorQueue.length === 10 ? 1000 : 30000,
        );
    } else if (tenMinSendNum === 11) {
        addLogquery(config, ctx, {
            message:
                '当前进程十分钟之内发送错误超过10次, 暂时停止发送错误, 下个十分钟区间自动开启错误通知',
        });
        tenMinSendNum = 12;
    }
};

const rpOptions = {
    method: 'GET',
    encoding: null,
    gzip: true,
    time: true,
    timeout: 10000,
    json: true,
    resolveWithFullResponse: true,
};

const rpRequest = (ctx, options) => {
    const _option = Object.assign({ time: true }, rpOptions, options);
    return rp(_option)
        .then(response => {
            debug(response.body);
            // prd才发送  性能日志上报
            if (isProduction) {
                let config = {
                    level: 'I',
                    funcName: 'axios.plugin.responseSuccess',
                    tag: 'axios.plugin.responseSuccess',
                };
                const message = formatToOptimus(
                    batchResolveParams(response),
                    ctx.req,
                    {},
                    false,
                );
                addLogquery(config, ctx, message);
            }
            return response.body;
        })
        .catch(reason => {
            // The server responded with a status codes other than 2xx.
            // 考虑超时的情况(connect timeout or read timeout) err.code === 'ETIMEDOUT' or err.connect === true
            const requestUrl = `${reason.options.baseUrl || ''}${
                reason.options.uri
            }`;
            let message = '';
            if (reason.statusCode) {
                message = `[request error] ${requestUrl} [${
                    reason.statusCode
                }]\n[Headers]${JSON.stringify(reason.options.headers)}`;
            } else if (
                ['ESOCKETTIMEDOUT', 'ETIMEDOUT'].includes(reason.error.code)
            ) {
                message = `[request error] ${requestUrl} ${
                    reason.error.connect === true ? 'connect' : 'read'
                } timeout of ${reason.options.timeout}ms exceeded`;
            } else {
                message = `[request error] ${requestUrl} ${reason.error}`;
            }
            if (message) {
                debug(message);
                if (isProduction) {
                    // 性能日志上报
                    const config = {
                        level: 'E',
                        file: __filename,
                        funcName: 'axios.plugin.responseError',
                        tag: 'axios.plugin.responseError',
                    };
                    const messages = formatToOptimus(
                        batchResolveParams(reason),
                        ctx.req,
                        {},
                        false,
                    );
                    addLogquery(config, ctx, messages);
                }
                // 非dev下错误日志上报
                if (!isDev) {
                    notifyError(ctx, { message, name: 'Request Error' });
                }
            }
        });
};

const getCommonHeader = appCtx => {
    const commonHeaders = {};
    if (appCtx.header) {
        commonHeaders['X-Klook-Host'] = appCtx.header['x-klook-host'];
        commonHeaders['X-Klook-Entry'] = appCtx.header['X-Klook-Entry'];
        commonHeaders['User-Agent'] = appCtx.header['user-agent'];
        commonHeaders['Referer'] = appCtx.header['referer'] || '';
        commonHeaders['Cookie'] = appCtx.header['cookie'] || '';
        commonHeaders['X-DeviceID'] = appCtx.header['x-deviceid'] || '';
        commonHeaders['X-Request-From'] =
            appCtx.header['X-Request-From'] || 'node';
        commonHeaders['Request-ID'] = appCtx.header['request-id'] || '';
    }
    if (appCtx.cookies) {
        commonHeaders['_pt'] = appCtx.cookies.get('device_id_new') || '';
    }
    return commonHeaders;
};

const formatToOptimus = (responseObj, req, other = {}, stringify = true) => {
    const { request, statusCode, socket, headers } = responseObj;
    const logData = {
        // type,
        url: request ? request['href'] : '',
        level: 'I',
        action: 0,
        req_status: String(statusCode),
        _page_: 'agent_index',
        method:
            request && request.method
                ? String(request.method).toLowerCase()
                : '',
        _duration_:
            request && request.startTime ? Date.now() - request.startTime : 0,
        request_id: headers && headers['x-klook-request-id'],
        send_bytes: (socket || {}).bytesWritten || 0,
        received_bytes: (socket || {}).bytesRead || 0,
        pageUrl: req.oldPath || req.url || '',
        _logger_name_: 'access_log:frontend:node:api',
        business: 'klook-agent-web/axios',
        headers: req.headers,
        ...other,
    };
    return stringify ? JSON.stringify(logData) : logData;
};

module.exports = {
    getCommonHeader,
    notifyError,
    rpRequest,
    isProduction,
    NODE_ENV,
    batchResolveParams,
    isDev,
};
