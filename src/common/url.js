export default {
    //signup
    agent_signup_email_check: '/v1/agentwebserv/register/check',
    upload_agent_license: '/v1/agentwebserv/agent/license/upload', //  /v1/fileagentserv/ 还没迁移k8s
    upload_agent_license_business_card:
        '/v1/agentwebserv/agent/business_card/upload', //  /v1/fileagentserv/ 还没迁移k8s
    register_account: '/v3/agentwebserv/register/account',
    register_info: '/v1/agentwebserv/register/info',

    //add activity to agent_collection
    add_activity_to_collection: '/v1/agentwebserv/wishes/add',
    remove_activity_from_collection: '/v1/agentwebserv/wishes/remove',

    add_shopping_cart: '/v2/agentwebserv/shoppingcart/add',

    // home接口拆分
    // agent_home: '/v1/agentwebserv/home',
    // agent_home_collected_activities:
    //     '/v1/agentwebserv/agent/collected_activities',
    agent_home_booked_activities: '/v1/agentwebserv/agent/booked_activities',
    agent_home_booking_summary: '/v1/agentwebserv/agent/booking_summary',

    agent_marketing_view: type => `/v1/agentwebserv/marketing/${type}/view`,
    agent_collection: '/v1/agentwebserv/collection',
    agent_account_info: '/v1/agentwebserv/account/info',
    agent_account_reset_password: '/v1/agentwebserv/account/password',
    agent_account_bookings: '/v2/agentwebserv/account/bookings',
    agent_account_batch_order_bookings: '/v1/agentwebserv/batchorder/search',
    agent_token_reset: '/v1/userserv/password/token/reset',

    unitorder_search: '/v1/agentwebserv/unitorder/search',
    // unitorder_search: '/v1/agentwebserv/batchorder/search',
    unitorder_download: '/v1/agentwebserv/unitorder/download',
    export_agent_order_voucher_batch_record:
        '/v1/agentwebserv/agentunit/bookings/export',

    agent_account_bookings_update: order_id =>
        `/v1/agentwebserv/bookings/${order_id}/update`,
    agent_account_batch_order_bookings_update: order_id =>
        `/v1/agentwebserv/batch_bookings/${order_id}/update`,
    get_activity_detail_url_by_id: activity_id =>
        `/v2/agentwebserv/activity/${activity_id}/detail`,
    get_activity_detail_url_by_id2: () => `/v3/agentwebserv/activity/detail`,
    get_activity_schedules_url_by_activity_id: activity_id =>
        `/v1/usrcsrv/activity/${activity_id}/schedules`,
    get_packages_schedules: `/v1/agentwebserv/packages/schedules`,
    get_arrangements_by_arrangement_id: arrangement_id =>
        `/v1/agentwebserv/arrangements/${arrangement_id}/units`,
    get_package_price_by_activity_id_and_date: (id, date) =>
        `/v1/agentwebserv/activity/${id}/schedule/${date}/package_price`,
    agent_account_order_detail: order_no =>
        `/v1/agentwebserv/bookings/${order_no}/detail`,

    agent_account_balance: '/v1/agentwebserv/account/balance',
    agent_balance_export: '/v1/agentwebserv/agent/balance/export',
    upload_logo: '/v1/agentwebserv/agent/voucher_logo_upload',
    upload_bill_voucher: '/v1/agentwebserv/bill/upload',
    agent_account_topups: '/v1/agentwebserv/account/balance/topups',
    agent_account_voucher_detail: ' /v1/agentwebserv/account/voucher_detail',
    agent_sub_account: '/v1/agentwebserv/account/sub_accounts',
    agent_balance_detail: '/v1/agentwebserv/account/balance/detail',

    agent_sub_account_update: sub_account_id =>
        `/v1/agentwebserv/account/sub_accounts/${sub_account_id}/update`,

    agent_logout: '/v1/agentwebserv/logout/account',
    agent_login: '/v2/agentwebserv/login/account',
    agent_send_otp: '/v1/agentwebserv/agent/login/otp/send',
    agent_validate_otp: '/v1/agentwebserv/agent/login/otp/validate',
    agent_bill_save: '/v1/agentwebserv/bill/save',
    country_city: '/v1/websrv/countries/cities',
    agent_account_countries: '/v1/agentwebserv/countries',
    agent_account_emails: '/v1/agentwebserv/account/emails',
    find_pwd: '/v2/agentwebserv/findpassword/email/send',
    export_agent_order_record: '/v1/agentwebserv/agent/bookings/export',
    export_agent_order_batch_record:
        '/v1/agentwebserv/agentbatch/bookings/export',
    //api_list
    api_list: '/v1/agentwebserv/account/api_list',

    //batch_order
    search_activity: '/v1/agentwebserv/batchorder/package/list',
    get_price_list_by_time_slot_id: '/v1/agentwebserv/batchorder/price/list',
    submit_batch_order: '/v2/agentwebserv/batchorder/create',

    //city.vue
    search_common: '/v1/agentwebserv/search/suggest',
    search_hint: '/v1/agentwebserv/search/hit',
    city_activity_search: '/v1/agentwebserv/search',
    agent_shoppingcart_info_api: '/v2/agentwebserv/shoppingcarts',
    delete_shopping_cart_item: '/v1/order/shoppingcart/delete',
    get_city_activities_by_search: '/v1/agentwebserv/search',

    search: {
        search_common: '/v1/agentwebserv/product/complete_search',
        search_by_city: '/v1/agentwebserv/product/city_search',
    },

    get_city_activities_by_city_id: city_id =>
        `/v1/agentwebserv/city/${city_id}/activities`,

    get_city_activities_by_city_id_and_tag_id: (city_id, tag_id) =>
        `/v1/agentwebserv/city/${city_id}/tags/${tag_id}/activities`,
    get_city_activities_by_city_id_and_template_id: (city_id, template_id) =>
        `/v1/agentwebserv/city/${city_id}/templates/${template_id}/activities`,

    billrecord_hide_status: '/v1/agentwebserv/billrecord/hide/status',
    get_city_template_tags_by_city_id: city_id =>
        `/v1/agentwebserv/city/${city_id}?templateIDs=1,2,3,4,5`,

    get_promotion_activites: '/v1/agentwebserv/marketing/promotion/activities',
    update_promotion_activites: ids =>
        `/v1/agentwebserv/marketing/${ids}/promotion/activities`,

    // booking
    booking_settlement: '/v1/agentwebserv/booking/settlement',
    batch_booking_settlement: '/v2/agentwebserv/settlement/info/batch',

    generate_order: '/v3/agentwebserv/generate/order',
    batch_generate_order: '/v2/agentwebserv/generate/batchorder',
    check_batch_order_status: batch_order_id =>
        `/v1/agentwebserv/batchorder/${batch_order_id}/detail`,
    order_detail: '/v1/agentwebserv/order/detail',

    //tamll
    tmall: {
        get_order_list: '/v1/agentwebserv/mashang/order/list',
        send_code: ids => `/v1/agentwebserv/mashang/code/send/${ids}`,
        cancel_after_vertication: '/v1/agentwebserv/mashang/code/consume',
        update_note: id => `/v1/agentwebserv/mashang/code/note/${id}`,
    },
    packages_schedules: '/v1/agentwebserv/packages/schedules',
    arrangements_units: arrangement_id =>
        `/v1/agentwebserv/arrangements/${arrangement_id}/units`,

    // 门票
    ticket: {
        activity: {
            package_skus: ids => `/v1/agentwebserv/${ids}/skus/basic`,
            sku_schedules: (package_id, sku_id) =>
                `/v1/agentwebserv/${package_id}/${sku_id}/schedules`,
        },
        pay: {
            package_aidSku_detail: '/v3/agentwebserv/package/detail',
            package_sku_detail: package_id =>
                `/v1/agentwebserv/${package_id}/detail`,
            booking_settlement: '/v2/agentwebserv/booking/settlement',
            booking_order: '/v3/agentwebserv/booking/order',
            order_info: order_no => `/v1/agentwebserv/booking/${order_no}/info`,
            supplier_timeslots:
                '/v1/agentwebserv/booking/package/supplier_timeslots',
        },
    },
    hotel: {
        list: '/v1/hotelapiserv/hotelapi/amp/order/list',
        suggest: '/v2/hotelapiserv/hotelapi/suggest',
        updateBookRefNo: '/v1/hotelapiserv/hotelapi/amp/order/info/update',
    },
    // 后端日志上报
    action_collect: '/v1/agentwebserv/agent/action/collect',
    ticket_alterinfo: {
        get: ticket_id => `/v1/agentwebserv/ticket/${ticket_id}/alterinfo`,
        update: '/v1/agentwebserv/ticket/alter/submit',
        getHistory: ticket_id =>
            `/v1/agentwebserv/ticket/${ticket_id}/alterhistory`,
    },
    hotel_booking_export: '/v1/agentwebserv/agenthotel/bookings/export',

    user_term_list: '/v1/agentwebserv/user_term/display/list',
    user_term_authorize: '/v1/agentwebserv/user_term/authorize',

    // zannadu auth sdk
    check_auth_status: '/v1/agentwebserv/znd/authorized/status',
    action_authorize: '/v1/agentwebserv/oauth2/authorize',
    sam_ticket_voucher: '/v1/agentwebserv/sam/ticket/voucher',
};
