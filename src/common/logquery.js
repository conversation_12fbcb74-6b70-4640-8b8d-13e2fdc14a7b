import Logquery from '@klook/logquery';
import { batchResolveParams } from './kcrypto';

/**
 * https://klook.larksuite.com/docs/docusjzk230oA91XguAQrIA5Che
 */

const defaultOptions = {
    url: window.KLK_LOGQUERY_URL,
    headers: { 'X-Platform': 'desktop' },
    queue: {
        interval: 5000,
        size: 15,
    },
};
const logquery = new Logquery(defaultOptions);

export default class Logger {
    /**
     * @param {*} error
     */
    constructor() {}
    handlerError(error, customInfo) {
        error = Object.assign(customInfo, error);
        logquery.service({
            isMasked: true,
            timestamp: Date.now(),
            level: customInfo.level || 'E',
            message: JSON.stringify({
                errorMessage: batchResolveParams(error),
                more: {
                    url: window.location.href,
                    name: 'AMP.Error',
                    userAgent: window.navigator.userAgent,
                },
            }),
            tag: 'AMP/Error',
        });
    }
}
