import Report from '@klook/client-report';
import once from 'lodash/once';

// 使用 app_env
const klkReport = new Report({
    url: window.KLK_LOGQUERY_URL_V3,
    platform: 'desktop',
    debug: window.KLK_ENV === 'production' ? false : true,
});

// /**
//  * 追踪页面渲染成功
//  * @param routerName
//  */
async function trackPageRenderSuccess(routerName) {
    klkReport.reportPerformance({
        pageName: routerName,
    });
}

// 保留页面的性能追踪
const trackPageRenderSuccessOnce = once(routerName => {
    return trackPageRenderSuccess(routerName);
});

// // 追踪接口请求成功
// function trackRequestSuccess(response, params) {
//     params.param = response && response.config && response.config.params;
//     klkReport.reportRequestSuccess(
//         {
//             pageName: params.page,
//         },
//         params,
//         response,
//     );
// }

// // 追踪接口请求失败
// function trackRequestError(error, response, params) {
//     params.param = response && response.config && response.config.params;
//     klkReport.reportRequestError(
//         {
//             pageName: params.page,
//         },
//         params,
//         response,
//         error,
//     );
// }

// export { trackRequestSuccess, trackRequestError, trackPageRenderSuccessOnce };
export { trackPageRenderSuccessOnce };
