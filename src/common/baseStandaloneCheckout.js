// docs：https://klook.slab.com/posts/web-%E7%8B%AC%E7%AB%8B%E6%94%B6%E9%93%B6%E5%8F%B0%E5%AF%B9%E6%8E%A5%E6%8A%80%E6%9C%AF%E6%96%B9%E6%A1%88-jq42zzwk
import Cookies from 'js-cookie';
import i18n from '@/i18n';
import { SERVER_API_HOST_URL } from '~/config';

// 容器区分，每个容器域名不一样
const devEnv = ['dev'];
const testEnv = ['awsdev', 'fat', 'ptest'];
const seeraEnv = ['seera', 'seeraprod', 'seeradev', 'seerastage'];
const aliyunfatEnv = ['aliyunfat', 'aliyunfat2'];
const fwsEnv = ['fws'];

const getEnvNum = (url, curt, target) =>
    url.includes('uat')
        ? url.replace(`${curt}-`, '')
        : url.replace(curt, target);

const currentHost =
    (new URL(window.location.href) || {}).host || 'klook.klktech.com';

const prodHost = {
    'klook.klktech.com': 'https://www.klook.com',
    'klook.klktech.cn': 'https://www.klook.cn',
    'klook-verify.klktech.cn': 'https://www-verify.klook.cn',
    'klook.stage.klktech.com': 'https://www.stage.klook.io',
    'klook.stage.klktech.cn': 'https://www.stage.klook.cn',
};

const apiHostMap = env => {
    const host = window.location.host
    // dev
    if (devEnv.includes(env)) {
        return getEnvNum(SERVER_API_HOST_URL);
    }

    // 新测试环境
    if(host.endsWith('.klooktest.io')||host.endsWith('.klooktest.cn')||host.endsWith('.klooktest.com')){
        const other = host.replace(/^([a-zA-Z]+)(\d+-(fat|uat|dev|fws).klooktest|.stage.klook|.klook)(.com|.io|.cn)/, "$2$4");
        return `https://www${other}`
    }

    // 测试环境
    if (testEnv.includes(env)) {
        return getEnvNum(window.location.origin, 'agent', 't');
    }
    // seera
    if (seeraEnv.includes(env)) {
        return getEnvNum(window.location.origin, 'klktech', 'www');
    }

    if (aliyunfatEnv.includes(env)) {
        return getEnvNum(window.location.origin, 'klktech', 'www');
    }

    if (fwsEnv.includes(env)) {
        return getEnvNum(window.location.origin, 'agent', 'www');
    }
    // prod
    return prodHost[currentHost] || 'https://www.klook.com';
};

// CN站只跳转CN收银台
// 虽然暂时没有en-bs，提前做一个映射以防未来新增这个语言
const lang = klook.isCNSite
    ? 'zh-CN'
    : window.KLK_LANG === 'en'
    ? 'en-US'
    : window.KLK_LANG === 'en-BS'
    ? ''
    : window.KLK_LANG;

// const envMap = {
//     development: getEnvNum(SERVER_API_HOST_URL),
//     testing: getEnvNum(window.location.origin),
//     production: 'https://www.klook.com',
// };

const getCheckoutOptions = async ({
    orderNo,
    settlementType,
    entrance,
    upgrade,
    middleRedirectUrl,
}) => {
    return {
        cookie: Cookies.get('encrypted_token'),
        checkoutConfig: {
            env: {
                apiHost: apiHostMap(window.KLK_ARGS_ENV || 'prod'),
            },
            urlConfig: {
                langPath: `${lang}/`, // 收银台多语言路径，'zh-CN/', en-BS 时传入 ''
                orderNo: orderNo, // B 端订单结算获取到的订单号
                settlementType: settlementType || '0', // 0: 从购物车进入 1: 直接从活动详情进入
                // 1: 订单结算页进入；2: 订单列表页进入；3: 订单详情页进入（如果 B 端没有待支付，entrance 都传 1 就行）
                entrance: entrance || '1',
                upgrade: upgrade || '', // 订单结算页购买的 klook flex 保险的数量
            },
            uiConfig: {
                themeColor: '#4c87e6', // 收银台主题风格颜色
                headerVisible: true, // 是否隐藏收银台顶部导航，默认隐藏
                headerStyle: {
                    // 顶部导航自定义 css style
                    background: '#fff',
                    padding: '0 30px',
                    'box-shadow': 'inset 0 -1px 0 0 #eeeeee',
                },
                headerLogoUrl: window.KLK_JOINT_VENTRUES.logo_url, // B 端收银台定制顶部导航 logo 链接
                headerLogoLink: '', //B 端收银台定制顶部导航点击 logo 对应的链接，默认 ''，代表不可点击
                headerHistoryBackArrowVisible: false, //  B 端收银台定制顶部导航是否显示返回上一页箭头
                footer: '', // B 端收银台定制底部 footer，默认 ''，优先级比 customFooterConfigs 要高
                footerConfigs: [], // 收银台底部 footer 简单配置项，about us 等, 适用于 mweb
                dataSecurityConfigs: [
                    //  提交区域 data security 显示内容，服务条款等
                    // @todo @hugo cn待提供
                    {
                        content: i18n.t('t_and_c'),
                        link: `${window.location.protocol}//${window.location.host}/conditions.html`,
                    },
                ],
                orderInfoVisible: true, // 是否隐藏订单信息，默认不可见
                travelerInfoVisible: true, // 是否隐藏预定人信息，默认不可见
            },
            tokenConfig: {
                grabPayIdToken: '',
            },
            trackConfig: {},
            extraUrlConfig: {
                //  B 端各业务方提供的中间页的链接
                middleRedirectUrl: middleRedirectUrl || '',
            },
        },
    };
};

export { getCheckoutOptions };
