import axios from 'axios';
import { captchaInterceptor } from '@klook/captcha';
import Cookies from 'js-cookie';
import qs from 'qs';
import Base58 from 'base-58';
import sha1 from 'js-sha1';
import md5 from './md5';
// import Monitor from './monitor';
import Logger from './logquery';
// import { trackRequestSuccess, trackRequestError } from './optimus';
import router from '../router';
import { DRPAgentMap, NOT_REPORT_PATH } from './const_data';
import { getDRPAgent } from './util';
import { batchResolveParams } from './kcrypto';
/**
 * 提取一些公共的、比较基础的方法到这里
 * 1、ajax封装，使用axios，兼容之前的ajaxGet\ajaxPost\ajaxPostJSON调用
 * 2、浮点运算加、乘： accAdd accMul
 * 3、md5计算
 * 4、字符串处理：千分位函数formatPriceThousands
 * 5、登录状态判断、邮箱、密码有效性检测
 */
captchaInterceptor(axios);
axios.defaults.headers.common = {
    'Accept-Language': (window.KLK_LANG === 'en'
        ? 'en-US'
        : window.KLK_LANG || 'en-US'
    ).replace('-', '_'),
    version: '4.0',
    'x-platform': 'desktop',
};

const logger = new Logger();
window.addEventListener('error', err =>
    logger.handlerError({ stack: err.error.stack }, { type: 'js' }),
);

const getParams = (type, url, headers, data) => {
    return {
        type,
        url,
        headers,
        data,
        startTime: Date.now(),
        page: router.history.current.name,
    };
};

const handleError = (error, fn, params) => {
    let errMessage = '';
    // error.request
    if (error.response) {
        errMessage = `client request:${error.config.url}:response status ${error.response.status}`;
    } else if (error.request) {
        errMessage = `client request:${error.config.url}:request ${error.request}`;
    } else {
        errMessage = `Axios Error: ${error.message}`;
    }
    typeof fn === 'function' &&
        fn({
            success: false,
            error: {
                code: 990001,
                message:
                    'There were some network issue. Please try again later.',
            },
        });
    errMessage = errMessage
        ? errMessage
        : 'There were some network issue. Please try again later.';
    console.log(errMessage);
    // 加上errMessage，有些上报的日志看不出来什么问题
    const filterParams = batchResolveParams(params);
    logger.handlerError(error, {
        type: 'ajax',
        errMessage,
        reqUrl: (error.config || {}).url,
        ...filterParams,
    });
    // trackRequestError(error, error.response, params);
};

const cbWrapper = (fn, response, param) => {
    console.log('param', param);
    const result = response.data || {};
    const errorCode = result.error ? `${result.error.code}` : '0';

    if (
        errorCode === '400' ||
        errorCode === '4001' ||
        errorCode === '0995S0010'
    ) {
        document.cookie = 'cookie1=0;max-age=-1;path=/';
        document.cookie = '_pt=0;max-age=-1;path=/';
        if (!/\/signin$/.test(location.pathname)) {
            window.location.replace(
                `${window.KLK_LANG_PATH ||
                    ''}/signin?continue=${encodeURIComponent(location.href)}`,
            );
        } else {
            typeof fn == 'function' && fn(result, response);
        }
    } else {
        typeof fn == 'function' && fn(result, response);
    }
};

const ajaxGet = (url, data, fn, headers) => {
    fn = typeof data === 'function' ? data : fn;
    // params 的作为handleError/cbWrapper 上报参数
    const params = getParams('GET', url, headers, data);
    return axios
        .get(url, {
            headers: Object.assign({}, headers),
            params: Object.assign({ _: Date.now() }, data),
        })
        .then(response => {
            cbWrapper(fn, response, params);
        })
        .catch(error => {
            handleError(error, fn, params);
        });
};

const ajaxPostJSON = (url, data, fn, headers, config = {}) => {
    console.log('url', url);
    fn = typeof data === 'function' ? data : fn;
    const params = getParams('POST', url, headers, data);
    return axios
        .post(`${url}?_=${Date.now()}`, data, {
            headers: Object.assign({}, headers),
            ...config,
        })
        .then(response => {
            cbWrapper(fn, response, params);
        })
        .catch(error => {
            let newParams;
            // 针对这个接口特殊处理
            if (NOT_REPORT_PATH.includes(url)) {
                newParams = {};
            } else {
                newParams = params;
            }
            handleError(error, fn, newParams);
        });
};

const ajaxPost = (url, data, fn, headers, config = {}) =>
    ajaxPostJSON(url, qs.stringify(data), fn, headers, config);

const accMul = (arg1, arg2) => {
    let m = 0,
        s1 = arg1.toString(),
        s2 = arg2.toString();
    try {
        m += s1.split('.')[1].length;
    } catch (e) {
        // empty block
    }
    try {
        m += s2.split('.')[1].length;
    } catch (e) {
        // empty block
    }
    return (
        (Number(s1.replace('.', '')) * Number(s2.replace('.', ''))) /
        Math.pow(10, m)
    );
};

const accAdd = (arg1, arg2) => {
    let r1, r2, m;
    try {
        r1 = arg1.toString().split('.')[1].length;
    } catch (e) {
        r1 = 0;
    }
    try {
        r2 = arg2.toString().split('.')[1].length;
    } catch (e) {
        r2 = 0;
    }
    m = Math.pow(10, Math.max(r1, r2));
    return (accMul(arg1, m) + accMul(arg2, m)) / m;
};

const formatPriceThousands = price => {
    price = (price || '0').toString();
    let tmp;
    if (price.indexOf('.') < 0) {
        tmp = price.replace(/(?=(?!(\b))(\d{3})+$)/g, ',');
    } else {
        price = price.split('.');
        tmp =
            price[0].toString().replace(/(?=(?!(\b))(\d{3})+$)/g, ',') +
            '.' +
            price[1];
    }
    return tmp;
};

const checkPhoneNumber = phoneNumber => {
    return /^\d{6,20}$/.test(phoneNumber);
};

const checkEmail = email => {
    return /^[a-zA-Z0-9_-]+(\.([a-zA-Z0-9_-])+)*@[a-zA-Z0-9_-]+[.][a-zA-Z0-9_-]+([.][a-zA-Z0-9_-]+)*$/.test(
        email,
    );
};

const checkPwd = pwd => {
    return (
        pwd &&
        pwd.length >= 8 &&
        pwd.length <= 20 &&
        /[a-zA-Z]/g.test(pwd) &&
        /[0-9]/g.test(pwd)
    );
};

/**
 * https://github.com/Kount/kount-ris-java-sdk/blob/master/kount-ris-sdk/src/main/java/com/kount/ris/util/Khash.java
 * @param {*} kount hash cardNumber
 */
const getCardNumberHash = cardNumber => {
    if (typeof cardNumber !== 'string') {
        return cardNumber;
    }

    const acceptableCharacters = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ';

    const loopMax = 28;
    const hexChunk = 7;
    const aLength = 36;

    const sha1Digest = sha1(
        `${cardNumber}.4077th hawkeye trapper radar section-8`,
    );

    let hashed = '';
    for (let i = 0; i < loopMax; i += 2) {
        hashed +=
            acceptableCharacters[
                parseInt(sha1Digest.substring(i, hexChunk + i), 16) % aLength
            ];
    }

    const cardToken = `${cardNumber.substring(0, 6)}${hashed}`;

    const bytes = [];
    for (let i = 0; i < cardToken.length; i++) {
        bytes.push(cardToken[i].charCodeAt(0));
    }

    return Base58.encode(bytes);
};

/**
 * 兼容老代码, 样式同样使用老的，样式优化的时候，这个应该去掉
 */
// todo need delete
const globalLoading = {
    show(msg) {
        if (!this._el) {
            const shadowEl = document.createElement('div'),
                containerEl = document.createElement('div'),
                contentEl = document.createElement('div'),
                loadingEl = document.createElement('div'),
                textEl = document.createElement('p');

            shadowEl.style.position = 'fixed';
            shadowEl.style.left = '0px';
            shadowEl.style.right = '0px';
            shadowEl.style.top = '0px';
            shadowEl.style.bottom = '0px';
            shadowEl.style.background = 'rgba(0, 0, 0, 0.3)';
            shadowEl.style.zIndex = 111;

            containerEl.style.width = '310px';
            containerEl.style.height = '140px';
            containerEl.style.background = '#fff';
            containerEl.style.margin = '100px auto 0px';
            containerEl.style.borderRadius = '5px';
            containerEl.style.textAlign = 'center';
            containerEl.style.padding = '30px 0';

            loadingEl.className = 'uil-ring-css';
            loadingEl.style.transform = 'scale(0.8)';

            textEl.style.marginTop = '20px';
            textEl.innerText = msg;

            loadingEl.appendChild(document.createElement('div'));
            contentEl.appendChild(loadingEl);
            contentEl.appendChild(textEl);
            containerEl.appendChild(contentEl);
            shadowEl.appendChild(containerEl);

            document.body.appendChild(shadowEl);
            this._el = shadowEl;
            this._textEl = textEl;
        } else {
            this.hide();
        }
    },
    hide() {
        this._el && document.body.removeChild(this._el);
        this._textEl = null;
        this._el = null;
    },
};

const isLoggedIn = () => !!Cookies.get('cookie1');

/**
 * 功能待确定
 * @param {*} url
 */
const escSeo = url => {
    const matched = /(.*)(\.jpg|\.jpeg|\.png)$/i.exec(url);
    if (matched) {
        return matched[1].replace(/[.'"/&%]/g, '') + matched[2];
    }
    return typeof url === 'string' ? url.replace(/[.'"/&%]/g, '') : url;
};

// 模糊搜索正则
const escapeRegexpString = value =>
    value.replace(/[|\\{}()[\]^$+*?.]/g, '\\$&');

function loadScript(url, callback) {
    let head =
        document.getElementsByTagName('head')[0] || document.documentElement;
    let script = document.createElement('script');
    /*
      if ( s.scriptCharset ) {
          script.charset = s.scriptCharset;
      }
      */
    script.src = url;

    // Handle Script loading
    let loadFlag = false;

    // Attach handlers for all browsers
    script.onload = script.onreadystatechange = function() {
        if (
            !loadFlag &&
            (!this.readyState ||
                this.readyState === 'loaded' ||
                this.readyState === 'complete')
        ) {
            loadFlag = true;

            if (typeof callback === 'function') {
                callback();
            }

            // Handle memory leak in IE
            script.onload = script.onreadystatechange = null;
            if (head && script.parentNode) {
                head.removeChild(script);
            }
        }
    };

    // Use insertBefore instead of appendChild  to circumvent an IE6 bug.
    // This arises when a base node is used (#2709 and #4378).
    head.insertBefore(script, head.firstChild);
}

const escape = str => str && String(str).replace(/[.'"/\\&%]/g, '');

const getImageUrl = (width = 1920, height = 720, hasWater = false) =>
    `https://res.klook.com/images/fl_lossy.progressive,q_65/c_fill,w_${width},h_${height}${
        hasWater ? '/w_80,x_15,y_15,g_south_west,l_klook_water' : ''
    }/activities/`;

const isDRPAgent = !!getDRPAgent(DRPAgentMap);
const DRPLandingPage = DRPAgentMap[getDRPAgent(DRPAgentMap)];

const isCNSite = window.KLK_MARKET == 'cn';
const showHistoryList = isCNSite ? true : false;

export {
    ajaxGet,
    ajaxPost,
    ajaxPostJSON,
    accMul,
    accAdd,
    formatPriceThousands,
    md5,
    checkEmail,
    getCardNumberHash,
    globalLoading,
    checkPhoneNumber,
    checkPwd,
    isLoggedIn,
    escSeo,
    logger,
    escapeRegexpString,
    loadScript,
    escape,
    getImageUrl,
    isDRPAgent,
    DRPLandingPage,
    showHistoryList,
    isCNSite,
};
