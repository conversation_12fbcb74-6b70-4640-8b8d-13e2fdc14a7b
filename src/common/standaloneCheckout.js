import jssdk from '@klook/standalone-checkout-helper';
import { getCheckoutOptions } from './baseStandaloneCheckout';

const middleRedirectUrl = `${window.location.protocol}//${window.location.host}${window.KLK_LANG_PATH}/pay/middleware`;

let payData = {
    settlementType: '0',
    entrance: '1',
    upgrade: '',
    middleRedirectUrl,
};
// 打开收银台页面
const openOrderCheckout = async orderNo => {
    Object.assign(payData, { orderNo });
    jssdk.openOrderCheckout(await getCheckoutOptions(payData));
};

// B端重新登录后自动跳转至 2C 页面
const redirectOrderCheckout = async (orderNo, redirectUrl) => {
    const options = Object.assign(
        { redirectUrl },
        await getCheckoutOptions(
            Object.assign(payData, {
                orderNo,
            }),
        ),
    );
    jssdk.redirectUrl(options);
};

export { openOrderCheckout, redirectOrderCheckout };
