import jssdk from '@klook/standalone-checkout-helper';
import { getCheckoutOptions } from './baseStandaloneCheckout';
// 打开收银台页面
const openOrderCheckoutVertical = async payData => {
    jssdk.openOrderCheckout(await getCheckoutOptions(payData));
};

const redirectOrderCheckout = async (
    orderNo,
    settlementType,
    entrance,
    redirectUrl,
    upgrade,
    middleRedirectUrl,
) => {
    const options = Object.assign(
        { redirectUrl },
        await getCheckoutOptions(
            orderNo,
            settlementType,
            (entrance = '1'),
            upgrade,
            middleRedirectUrl,
        ),
    );
    jssdk.redirectUrl(options);
};
export { openOrderCheckoutVertical, redirectOrderCheckout };
