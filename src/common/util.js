import urlObj from '@/common/url';
import uniqBy from 'lodash/unionBy';

export const formatDateRFC3339 = date => {
    return date ? date.toISOString() : '';
};

export const openVouch = url => {
    if (!url) return false;
    klook.ajaxPostJSON(urlObj.action_collect, {
        action: 'view_voucher',
        action_value: url,
    });
    window.open(url);
};
export const processActivityInfo = function(acitivties = []) {
    if (acitivties && acitivties.length) {
        const currencySymbol = this.getCurrencySymbolByCurrencyCode(
            acitivties[0].currency,
        );
        const item = acitivties.map(a => {
            a.currencySymbol = currencySymbol;
            a.id = a.vertical_id;
            a.title = a.vertical_name;
            a.is_instance = a.instance;
            a.image_url = a.banner_url;
            a.is_promotion = a.promotion_info && a.promotion_info.is_promotion;
            a.sell_price = a.price_info && a.price_info.from_sell_price;
            a.market_price = a.price_info && a.price_info.from_market_price;
            a.is_vertical = a.vertical_type !== 100;
            return a;
        });
        return item;
    }
    return [];
};

export const expansionFn = (arr = [], res = []) => {
    arr.forEach(item => {
        if (item.children && item.children.length) {
            expansionFn(item.children, res);
        } else {
            res.push(item);
        }
    });
    return uniqBy(res, 'id');
};

// TODO 这需要后端处理，但是后端说结构体复杂，不好处理。后续有需求可以和产品沟通这一部分在后端处理。
// 现在处理方式：分页处理的卡片加上block，其他的不分页是过滤。因为有些不仅仅是活动卡片，比如最近购买和轮播图上面，所以用了前端屏蔽处理。
export const filterBlockData = (list = []) => {
    return list.filter(item => !item.is_blocked);
};

// 判断是否为分销Agent
const isPRDAgentID = id => {
    const isMaster =
        window.KLK_USER_INFO &&
        window.KLK_USER_INFO.parent_id === 0 &&
        window.KLK_USER_INFO.id === id;
    const isSub = window.KLK_USER_INFO && window.KLK_USER_INFO.parent_id === id;
    return isMaster || isSub;
};

// 获取分销Agent的ID
export const getDRPAgent = obj => {
    return Object.keys(obj).find(key => isPRDAgentID(+key));
};

export const openPage = config => {
    const { url, parentNode } = config;
    if (!url) {
        return;
    }
    const target = config.target || '_blank';
    let eleA = document.createElement('a');
    eleA.target = target;
    eleA.style.display = 'none';
    eleA.href = url;
    eleA.onclick = event => {
        event.stopPropagation();
        if (eleA) {
            eleA.onclick = null;
            parentNode && parentNode.removeChild(eleA);
            eleA = null;
        }
    };
    parentNode && parentNode.appendChild(eleA);
    eleA.click();
};
