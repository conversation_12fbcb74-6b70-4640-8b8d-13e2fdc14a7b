import debounce from 'lodash/debounce';
import urlObj from './url';

function isEmpty(val) {
    return (
        typeof val == 'undefined' ||
        (typeof val == 'string' && val.trim().length === 0)
    );
}

function lettersOnly(value) {
    return /^[a-zA-ZàáâäãåąčćęèéêëėįìíîïłńòóôöõøùúûüųūÿýżźñçčšžÀÁÂÄÃÅĄĆČĖĘÈÉÊËÌÍÎÏĮŁŃÒÓÔÖÕØÙÚÛÜŲŪŸÝŻŹÑßÇŒÆČŠŽ∂ð ,.'-]+$/i.test(
        value,
    );
}

function validateLettersOnly(rule, value, callback) {
    const msg = this.$t('global_validate_lettersonly');
    if (lettersOnly(value)) {
        callback();
    } else {
        callback(new Error(msg));
    }
}

function validateNotEmpty(rule, value, callback) {
    const msg = this.$t('cannot_be_empty');
    if (isEmpty(value)) {
        callback(new Error(msg));
    } else {
        callback();
    }
}
function validateEmail(rule, value, callback) {
    const msg = this.$t('global_validate_plzEnterValidEmail');
    if (!klook.checkEmail(value)) {
        callback(new Error(msg));
    } else {
        callback();
    }
}
let asyncValidateAgentSignupEmail = debounce(function(rule, value, callback) {
    const msg = this.$t('email_already_exists');
    klook.ajaxGet(
        urlObj.agent_signup_email_check,
        {
            email: value,
            user_channel: window.KLK_JOINT_VENTRUES.id,
        },
        resp => {
            if (resp.success) {
                if (resp.result) {
                    callback();
                } else {
                    callback(new Error(msg));
                }
            } else {
                if (resp.error) {
                    callback(resp.error.message);
                }
            }
        },
    );
}, 200);

function validateCountryCode(rule, value, callback) {
    const msg = this.$t('cannot_be_empty');
    if (isEmpty(this.signupTravelAgentDetailsData.country_code)) {
        callback(new Error(msg));
    } else {
        callback();
    }
}
function validatePhoneNumber(rule, value, callback) {
    const msg = this.$t('cannot_be_empty');
    if (isEmpty(value)) {
        callback(new Error(msg));
    } else if (!klook.checkPhoneNumber(value)) {
        callback(new Error(this.$t('phone_number_validate')));
    } else {
        callback();
    }
}

function validateEmployeesNumber(rule, value, callback) {
    const validateNumMsg = this.$t('please_input_integer_bigger_than_zero');
    const msg = this.$t('cannot_be_empty');
    if (isEmpty(value)) {
        callback(new Error(msg));
    } else if (!(+value > 0)) {
        callback(new Error(validateNumMsg));
    } else {
        callback();
    }
}

function validateTopDestinations(rule, value, callback) {
    const msg = this.$t('cannot_be_empty');
    if (value.length === 0) {
        callback(new Error(msg));
    } else {
        callback();
    }
}

function validatePassword(rule, value, callback) {
    const msg = this.$t('global_password_validate');
    if (!klook.checkPwd(value)) {
        callback(new Error(msg));
    } else {
        callback();
    }
}

const validateConfirmPwd = function(rule, value, callback) {
    const msg = this.$t('global_password_not_match');
    if (
        this.signupAdministratorRegistrationData.password !==
        this.signupAdministratorRegistrationData.repeatPwd
    ) {
        callback(new Error(msg));
    } else {
        callback();
    }
};

const validateBusinessLicenseImg = function(rule, value, callback) {
    const msg = this.$t('cannot_be_empty');
    if (isEmpty(this.signupCompanyDetailsData.business_license)) {
        callback(new Error(msg));
    } else {
        callback();
    }
};

const validateBusinessCard = function(rule, value, callback) {
    const msg = this.$t('cannot_be_empty');
    if (isEmpty(this.signupCompanyDetailsData.business_card)) {
        callback(new Error(msg));
    } else {
        callback();
    }
};

const validateSelectedCooperationChannels = function(rule, value, callback) {
    const msg = this.$t('cannot_be_empty');
    if (!Array.isArray(value) || value.length === 0) {
        callback(new Error(msg));
    } else {
        callback();
    }
};

const validateAgreeUsageTerm = function(rule, value, callback) {
    const msg = this.$t('cannot_be_empty');
    if (!value) {
        callback(new Error(msg));
    } else {
        callback();
    }
};

const formRules = function(custom_validation_obj) {
    let const_validation = {
        first_name: [
            {
                required: 'true',
                validator: validateLettersOnly.bind(this),
                trigger: 'blur',
            },
        ],
        last_name: [
            {
                required: 'true',
                validator: validateLettersOnly.bind(this),
                trigger: 'blur',
            },
        ],
        email_account: [
            {
                required: 'true',
                validator: validateEmail.bind(this),
                trigger: 'blur',
            },
            {
                validator: asyncValidateAgentSignupEmail.bind(this),
                trigger: 'blur',
            },
        ],
        country_code: [
            {
                required: 'true',
                validator: validateCountryCode.bind(this),
                trigger: 'change',
            },
        ],
        phone_number: [
            {
                required: 'true',
                validator: validatePhoneNumber.bind(this),
                trigger: 'blur',
            },
        ],
        password: [
            {
                required: 'true',
                validator: validatePassword.bind(this),
                trigger: 'blur',
            },
        ],
        repeatPwd: [
            {
                required: 'true',
                validator: validateConfirmPwd.bind(this),
                trigger: 'blur',
            },
        ],
        company_name: [
            {
                required: 'true',
                validator: validateNotEmpty.bind(this),
                trigger: 'blur',
            },
        ],
        duties: [
            {
                required: 'true',
                validator: validateNotEmpty.bind(this),
                trigger: 'blur',
            },
        ],
        website_url: [
            {
                required: 'true',
                validator: validateNotEmpty.bind(this),
                trigger: 'blur',
            },
        ],
        address: [
            {
                required: 'true',
                validator: validateNotEmpty.bind(this),
                trigger: 'blur',
            },
        ],
        business_license: [
            {
                required: 'true',
                validator: validateBusinessLicenseImg.bind(this),
                trigger: 'blur,change',
            },
        ],
        // agency_license 不需要必填
        // agency_license: [
        //     {
        //         required: 'true',
        //         validator: validateAgencyLicenseImg.bind(this),
        //         trigger: 'blur,change'
        //     }
        // ],
        business_card: [
            {
                required: 'true',
                validator: validateBusinessCard.bind(this),
                trigger: 'blur,change',
            },
        ],
        // some company detail information
        company_phone_number: [
            {
                required: 'true',
                validator: validatePhoneNumber.bind(this),
                trigger: 'blur',
            },
        ],
        company_email_address: [
            {
                required: 'true',
                validator: validateEmail.bind(this),
                trigger: 'blur',
            },
        ],
        num_of_employees: [
            {
                required: 'true',
                validator: validateEmployeesNumber.bind(this),
                trigger: 'blur',
            },
        ],
        establishment_date: [
            {
                required: 'true',
                validator: validateNotEmpty.bind(this),
                trigger: 'blur',
            },
        ],
        // company_selling & top_destinations change to required
        company_selling: [
            {
                required: 'true',
                validator: validateNotEmpty.bind(this),
                trigger: 'blur',
            },
        ],
        top_destinations: [
            {
                required: 'true',
                validator: validateTopDestinations.bind(this),
                trigger: 'blur',
            },
        ],
        cooperation_type: [
            {
                required: 'true',
                validator: validateSelectedCooperationChannels.bind(this),
                trigger: 'blur',
            },
        ],
        agreeUsageTerm: [
            {
                required: 'true',
                validator: validateAgreeUsageTerm.bind(this),
                trigger: 'blur,change',
            },
        ],
    };

    return Object.assign({}, const_validation, custom_validation_obj || {});
};

export default formRules;
