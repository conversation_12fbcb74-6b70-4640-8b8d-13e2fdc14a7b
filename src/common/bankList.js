/**
 * 充值银行信息
 */

import i18n from '@/i18n';

const HKD = [
    {
        name: i18n.t('citi_bank_hongkong'),
        bankCode: '006',
        swiftCode: 'CITIHKHXXXX',
        accountName: 'Klook Travel Technology Limited',
        accountNumber: '391 ********',
    },
    // {
    //     name: 'Paypal',
    //     accountName: 'Klook Travel Technology Ltd',
    //     accountNumber: '<EMAIL>',
    // },
];
const HKD_BANIA = [
    {
        name: i18n.t('citi_bank_hongkong'),
        bankCode: '006',
        swiftCode: 'CITIHKHXXXX',
        accountName: 'FLICKKET LIMITED',
        accountNumber: '391 ********',
    },
];

const CNY = [
    {
        name: i18n.t('alipay'), // alipay 账号信息接口返回
        accountName: '',
        accountNumber: '',
    },
    // {
    //     id: 'wechat',
    //     name: i18n.t('wechat_pay'),
    //     accountName: '深圳市趣游玩旅行社有限公司',
    // },
];

const JPY = [
    {
        name: i18n.t('citi_bank_hongkong'),
        bankCode: '006',
        swiftCode: 'CITIHKHXXXX',
        accountName: 'Klook Travel Technology Limited',
        accountNumber: '391 ********',
    },
];
const JPY_BANIA = [
    {
        name: i18n.t('citi_bank_hongkong'),
        bankCode: '006',
        swiftCode: 'CITIHKHXXXX',
        accountName: 'FLICKKET LIMITED',
        accountNumber: '391 ********',
    },
];

const TWD = [
    {
        name: i18n.t('ctbc'),
        bankCode: '822',
        swiftCode: 'CTCBTWTP271',
        accountName: i18n.t('taiwan_acount_name'),
        accountNumber: '************',
    },
];

const SGD = [
    {
        name: i18n.t('citi_bank_hongkong'),
        bankCode: '006',
        swiftCode: 'CITIHKHXXXX',
        accountName: 'Klook Travel Technology Limited',
        accountNumber: '**********',
    },
    // {
    //     name: 'Paypal',
    //     accountName: 'Klook Travel Technology Ltd',
    //     accountNumber: '<EMAIL>',
    // },
    {
        name: i18n.t('singapore_dbs_bank'),
        bankCode: '7214',
        swiftCode: 'CITISGSGXXX',
        accountName: 'Klook Travel Technology Pte Ltd',
        accountNumber: '**********',
    },
];

const SDG_BANIA = [
    {
        name: i18n.t('citi_bank_hongkong'),
        bankCode: '006',
        swiftCode: 'CITIHKHXXXX',
        accountName: 'FLICKKET LIMITED',
        accountNumber: '391 ********',
    },
];

const USD = [
    {
        name: i18n.t('citi_bank_hongkong'),
        bankCode: '006',
        swiftCode: 'CITIHKHXXXX',
        accountName: 'Klook Travel Technology Limited',
        accountNumber: '391 ********',
    },
    // {
    //     name: 'Paypal',
    //     accountName: 'Klook Travel Technology Ltd',
    //     accountNumber: '<EMAIL>',
    // },
    {
        name: i18n.t('singapore_dbs_bank'),
        bankName: '',
        bankCode: '7214',
        swiftCode: 'CITISGSGXXX',
        accountName: 'Klook Travel Technology Pte Ltd',
        accountNumber: '*********',
    },
];

const USD_BANIA = [
    {
        name: i18n.t('citi_bank_hongkong'),
        bankCode: '6',
        swiftCode: 'CITIHKHXXXX',
        accountName: 'FLICKKET LIMITED',
        accountNumber: '391 ********',
    },
];

const KRW = [
    // {
    //     name: i18n.t('citi_bank'),
    //     bankCode: '027',
    //     swiftCode: 'CITIKSX',
    //     accountName: 'KLOOK TRAVEL TECHNOLOGY LLC',
    //     accountNumber: '**********',
    // },
];
const KRW_KLOOK = [
    {
        name: 'JPMorgan Chase Bank N.A., Seoul Branch',
        swiftCode: 'CHASKRSX',
        accountName: 'Klook Travel Technology Limited',
        accountNumber: '**********',
    },
];

// MYR 在klook和jtr充值信息不一致
const MYR_KLOOK = [
    {
        name: i18n.t('citi_bank_berhad'),
        swiftCode: 'CITIMYKL',
        accountName: 'KLOOK TECHNOLOGY SDN BHD',
        accountNumber: '**********',
    },
];

const MYR_JTR = [
    {
        name: i18n.t('banking_corporation'),
        swiftCode: 'HSBCHKHHHKH',
        accountName: 'JTRweb Limited',
        accountNumber: '078-551660-838',
        bankAddress: '1 Queen’s Road Central, Hong Kong',
    },
];

// 新增货币
const NZD = [
    {
        name: 'Citibank, N.A. (Sydney Branch)',
        bankCode: '242-000',
        swiftCode: 'CITIAU2X',
        accountName: 'KLOOK TRAVEL TECH (AUS) PTY LTD',
        accountNumber: '*********',
    },
];

const THB = [
    {
        name: 'Citibank N.A.Bangkok Branch',
        bankCode: '017',
        swiftCode: 'CITITHBX',
        accountName: 'Klook Travel Technology Limited',
        accountNumber: '0-552867-009',
    },
];

const AUD = [
    {
        name: 'Citibank, N.A. (Sydney Branch)',
        bankCode: '242-000',
        swiftCode: 'CITIAU2X',
        accountName: 'KLOOK TRAVEL TECHNOLOGY AUS PTY LTD',
        accountNumber: '*********',
    },
];

const INR = [
    // 印度当地不允许代收代付，所以移除该充值方式
    // {
    //     name: 'Citibank N.A., Mumbai Branch',
    //     bankCode: '',
    //     swiftCode: 'CITI0100000',
    //     accountName: 'KLOOK TRAVEL TECHNOLOGY IND PVT LTD',
    //     accountNumber: '*********',
    // },
];

const VND = [
    // {
    //     name: 'Citibank N.A. - Hanoi Branch',
    //     bankCode: '',
    //     swiftCode: 'CITIVNVX',
    //     accountName: 'Klook Travel Vietnam Co Ltd',
    //     accountNumber: '**********',
    // },
];

const EUR = [
    {
        name: 'JPMorgan Chase Bank, N.A. Amsterdam',
        bankCode: '',
        swiftCode: 'CHASNL2X',
        accountName: 'KLOOK TRAVEL TECHNOLOGY LIMITED EUR',
        accountNumber: '*********',
        iban: 'NL44CHAS0*********',
    },
];

const GBP = [
    {
        name: 'JPMorgan Chase Bank, N.A. London',
        bankCode: '60-92-42',
        swiftCode: 'CHASGB2L',
        accountName: 'Klook Travel Technology Limited',
        accountNumber: '********',
        iban: 'GB21CHAS609242********',
    },
];

const IDR = [
    // {
    //     name: 'Citibank, N.A. Indonesia ',
    //     bankCode: '031-0305',
    //     swiftCode: 'CITIIDJX',
    //     accountName: 'KLOOK TRAVEL TECHNOLOGY PTE LTD',
    //     accountNumber: '0-550890-003',
    // },
];

const CURRENCY_PAYMENT_MAP = {
    klook: {
        MYR: MYR_KLOOK,
        USD,
        TWD,
        SGD,
        KRW: KRW_KLOOK,
        JPY,
        HKD,
        CNY,
        INR,
        NZD,
        THB,
        VND,
        AUD,
        EUR,
        GBP,
        IDR,
    },
    jtr: {
        MYR: MYR_JTR,
        USD,
        TWD,
        SGD,
        KRW,
        JPY,
        HKD,
        CNY,
        INR,
        NZD,
        THB,
        VND,
        AUD,
        EUR,
        GBP,
        IDR,
    },
    bania: {
        HKD: HKD_BANIA,
        JPY: JPY_BANIA,
        SGD: SDG_BANIA,
        USD: USD_BANIA,
        MYR: MYR_KLOOK, // 由klook MYR代收
        KRW, // 由klook KRW代收
        TWD, // 由klook TWD代收
        INR,
        NZD,
        THB,
        VND,
        AUD,
        EUR,
        GBP,
        IDR,
    },
    // same as klook
    klookCN: {
        MYR: MYR_KLOOK,
        USD,
        TWD,
        SGD,
        KRW: KRW_KLOOK,
        JPY,
        HKD,
        CNY,
        INR,
        NZD,
        THB,
        VND,
        AUD,
        EUR,
        GBP,
        IDR,
    },
};

export default currency => {
    return (CURRENCY_PAYMENT_MAP[window.KLK_JV_NAME] || {})[currency] || {};
};
