/**
 * 日志上报到kibana.klook.com；
 */

const defaultOptions = {
    reportDelay: 60 * 1000,
    enableTracker: /klktech\.com$/.test(location.hostname) ? true : false,
    maxStackDepth: 8,
    url: 'https://log.klook.com/wlog/agent-web-err/log'
};

const stringHashCode = str => {
    let hash = 0;

    if (str.length == 0) return hash;

    for (let i = 0; i < str.length; i++) {
        let c = str.charCodeAt(i);
        hash = ((hash << 5) - hash) + c;
        hash = hash & hash;
    }
    return hash;
};


const subStack = (stackStr, maxDepth) => {
    if (typeof stackStr !== 'string') {
        return null;
    }
    return stackStr.split('\n').slice(0, maxDepth);
};

const getErrorUniqueKey = errorInfo => {
    if (!errorInfo) {
        return errorInfo;
    }
    return stringHashCode(Object.values(errorInfo).join());
};

const isBotReg = /(Sogou web spider)|(bingbot)|(Googlebot)|(<PERSON><PERSON>pider)|(AdsBot)|(TweetmemeBot)|(Slackbot)|(James BOT)|(Applebot)|(Facebot)|(YandexMobileBot)|(AhrefsBot)|(contxbot)|(Livechat OpenGraph Robot)|(Mail.RU_Bot)|(archive.org_bot)|(MojeekBot)|(Discordbot)|(startmebot)/i;

export default class Monitor {
    constructor(options) {
        this._options = Object.assign({}, defaultOptions, options);
        this._lastSend = [];
    }
    _proxyPost(data) {
        if (isBotReg.test(navigator.userAgent)) {
            return ;
        }
        
        const xhr = new XMLHttpRequest();
        xhr.open('POST', this._options.url);

        xhr.send(JSON.stringify(data));
    }
    /**
     * 参考：https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Error
     * @param {*} error Error对象
     */
    handler(error, customInfo) {
        console.error(error);
        if (!error || !this._options.enableTracker) {
            return ;
        }


        const { reportDelay, maxStackDepth } = this._options;

        const errorInfo = Object.assign({
            name: error.name,
            message: error.message,
            type: error.type || 'default',
            stack: subStack(error.stack, maxStackDepth),
            href: location.href,
            referer: document.referrer
        }, customInfo);

        if (typeof errorInfo.message === 'string' && ~errorInfo.message.indexOf('Script error')) {
            return ;
        }

        const _key = getErrorUniqueKey(errorInfo), t = Date.now();

        const _lastSend = this._lastSend[_key] || 0;

        if (t - _lastSend > reportDelay) {
            typeof this._proxyPost === 'function' && this._proxyPost(errorInfo);
            this._lastSend[_key] = t;
        }
    }
}

