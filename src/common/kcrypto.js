import { MaskUtil } from '@klook/klk-kcrypto';

const MOBILE_KEYS = [
    'mobile',
    'phoneNumber',
    'booking_agent_mobile',
    'phone_number',
];
const EMAIL_KEYS = ['booking_agent_email', 'email'];
const FIRST_NAME_KEYS = ['first_name', 'family_name', 'firstName'];

const isObject = val =>
    Object.prototype.toString.call(val) === '[object Object]';

const deepResolveParams = obj => {
    const resolveParam = obj;
    Object.keys(resolveParam).forEach(key => {
        const value = resolveParam[key];

        if (isObject(value)) {
            deepResolveParams(value);
            return;
        }
        if (!value) {
            // do nothing
        } else if (MOBILE_KEYS.includes(key)) {
            resolveParam[key] = MaskUtil.mobile(value);
        } else if (EMAIL_KEYS.includes(key)) {
            resolveParam[key] = MaskUtil.email(value);
        } else if (FIRST_NAME_KEYS.includes(key)) {
            resolveParam[key] = MaskUtil.first_name(value);
        }
    });
};
const resolveParams = params => {
    if (!params) return params;

    const resolveParam = JSON.parse(JSON.stringify(params));
    deepResolveParams(resolveParam);

    return resolveParam;
};

export const batchResolveParams = function(params) {
    const resolveParam = Object.assign({}, params);
    if (resolveParam.data) {
        resolveParam.data = resolveParams(resolveParam.data);
    }

    if (resolveParam.params) {
        resolveParam.params = resolveParams(resolveParam.params);
    }
    return resolveParam;
};
