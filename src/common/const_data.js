import { format } from 'date-fns';
import i18n from '@/i18n';
import { backendSupportLocales } from '../../config';

export const TEMPLATE_VERTICAL_MAP = {
    1: 'Attractions & Shows',
    2: 'Tours & Sightseeing',
    3: 'Activities & Experiences',
    4: 'F&B',
    5: 'Transport & Travel Service',
    7: 'WiFi',
    8: 'Sim Card',
    9: 'YSim',
    12: 'China Rail',
    13: 'Airport Transfer',
    14: 'Hotel',
    16: 'Hotel API', // 和小程序保持一致
};

// 获取sso header跳转的url
export const getURL = type => {
    // 处理和C端langPath code不同(AMP默认是en-US，c端的默认是en-BS)
    const transformLangPath = lang => {
        const langPathMap = {
            en: '/en-US',
            'en-BS': '',
        };
        if (langPathMap.hasOwnProperty(lang)) {
            return langPathMap[lang];
        }
        return `/${lang}`;
    };

    let langPath = transformLangPath(window.KLK_LANG);
    if (process.env.NODE_ENV === 'production') {
        return `https://klktech.partner.klook.com${langPath}/${type}/`;
    } else {
        return `https://t114.test.klook.io${langPath}/${type}/?util=partner&partner_id=hybrid-amp&amp_auth=1`;
    }
};

export const AGENT_TYPE = ['Main Account', 'Sub Account']; // agent_type 0 主账号  1 子账号

// 后端有几个接口只支持三种语言
export const acceptLanguage = backendSupportLocales.includes(window.KLK_LANG)
    ? {}
    : { 'Accept-Language': 'en_US' };

export const statusTypeList = {
    0: { icon: 'icon_feedback_pending', color: '#FC7F0A' },
    1: { icon: 'icon_feedback_warning', color: '#E64340' },
    4: { icon: 'icon_feedback_success', color: '#16AA77' },
    8: { icon: 'refund', color: '#FF5722' },
};

export const getRuleDate = date => {
    return date ? format(date, 'DD/MM/YYYY HH:mm:ss') : '';
};

export const booking_status_list = function() {
    return [
        {
            value: -1,
            label: i18n.t('all'),
        },
        {
            value: 0,
            label: i18n.t('not_confirm'),
        },
        {
            value: 1,
            label: i18n.t('is_canceled'),
        },
        {
            value: 4,
            label: i18n.t('is_confirmed'),
        },
        {
            value: 8,
            label: i18n.t('is_refunded'),
        },
    ];
};

export const payment_types_list = function() {
    return [
        {
            value: '',
            label: i18n.t('all'),
        },
        {
            value: 'paypal',
            label: i18n.t('paypal'),
        },
        {
            value: 'alipay',
            label: i18n.t('alipay'),
        },
        {
            value: 'weixinpay',
            label: i18n.t('wechat_pay'),
        },
        {
            value: 'balance',
            label: i18n.t('credit_balance'),
        },
        {
            value: 'creditcard',
            label: i18n.t('credit_card'),
        },
    ];
};

export const voucher_types_list = function() {
    return [
        {
            value: 0,
            label: i18n.t('all'),
        },
        {
            value: 1,
            label: i18n.t('pending_payment'),
        },
        {
            value: 2,
            label: i18n.t('paid'),
        },
        {
            value: 3,
            label: i18n.t('is_confirmed'),
        },
        {
            value: 4,
            label: i18n.t('is_refunded'),
        },
    ];
};

export const modifyOtherInfoStatusList = function() {
    return {
        //0（处理中），1（成功），2（失败）
        0: i18n.t('modifying'),
        1: i18n.t('modify_success'),
        2: i18n.t('modify_fail'),
    };
};

// #111209 AMP 分销商品限制功能优化 https://ones.klook.io/project/#/team/BtSACWh2/task/9u3KdA53kWJKPyHZ
export const DRPAgentMap = {
    8897: '/activity/412',
    8455: '/activity/412',
    12410: '/activity/412', // Trazy Agent
};

export const NOT_REPORT_PATH = ['/v2/agentwebserv/booking/order'];

/**
 * 赞那度host
 * 产线：https://klookzhotel.klktech.cn/hotelchannel
 * 测试：https://klk-test.zanadu.com.cn/hotelchannel
 */
export const ZND_HOST = ['klookzhotel.klktech.cn', 'klk-test.zanadu.com.cn'];

export const hostWhiteList = [
    // 产线
    'klook.klktech.com',
    'klook.klktech.cn',
    'www.klook.com',
    'www.klook.cn',
    'jtrasia.klktech.com',
    'www.almosafer-activities.com',
    'agent.almosafer-activities.com',
    'klook.stage.klktech.com',
    'klook.stage.klktech.cn',
    'www.stage.klook.io',
    'www.stage.klook.cn',
];
