class FileInput {
    constructor() {
        const id = 'klk--input__fileupload'
        const $el = document.querySelector(`#${id}`)

        this.id = id
        this.$el = null

        if ($el) {
            return $el
        }
        this._createElement()
        return this
    }

    _createElement() {
        const { id } = this
        let input = document.createElement('input')
        input.type = 'file'
        input.style.display = 'none'
        input.id = id
        this.$el = input
        document.body.appendChild(input)
        return this
    }

    click() {
        setTimeout(() => {
            this.$el.click()
        }, 0);
        return this
    }

    onchange(callback) {
        this.$el.onchange = async (...args) => {
            await callback.apply(null, args)
            this.reset()
        }
        return this
    }

    reset() {
        if (this.$el) {
            // hack: trigger change event when selects the same file
            this.$el.value = ''
        }
    }

    destroy() {
        this.$el = null
        this.id = null
    }

}

export default new FileInput()