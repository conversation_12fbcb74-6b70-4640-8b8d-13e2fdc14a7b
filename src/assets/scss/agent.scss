@import 'markdown.scss';

html {
    -ms-text-size-adjust: 100%;
    -webkit-text-size-adjust: 100%;
}

body {
    margin: 0;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Droid Sans', 'Helvetica Neue', 'Helvetica', 'Arial', sans-serif;
    font-size: 14px;
    color: #000;
    line-height: 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

body.lang_zh-CN {
    font-family: 'Helvetica Neue', 'Helvetica', 'Arial', 'PingFang SC', '微软雅黑', 'Microsoft YaHei', '华文细黑', 'STHeiti', sans-serif;
}

body.lang_zh-TW {
    font-family: 'Helvetica Neue', 'Helvetica', 'Arial', 'PingFang TC', '微软雅黑', 'Microsoft YaHei', '华文细黑', 'STHeiti', sans-serif;
}

body.lang_zh-HK {
    font-family: 'Helvetica Neue', 'Helvetica', 'Arial', 'PingFang HK', '微软雅黑', 'Microsoft YaHei', '华文细黑', 'STHeiti', sans-serif;
}

body.lang_ko {
    word-break: keep-all;
    font-family: 'Helvetica Neue', 'Helvetica', 'Arial', 'Apple SD Gothic Neo', '나눔 고딕', 'Nanum Gothic', '맑은 고딕', 'Malgun Gothic', 'dotum', sans-serif;
}

body,
h1,
h2,
h3,
h4,
h5,
h6,
p,
ul,
ol,
form {
    margin: 0;
}

h4,
h5,
h6 {
    font-size: 1em;
}

h1,
h2,
h3,
h4,
h5,
h6 {
    font-weight: normal;
}

ul,
ol {
    padding-left: 0;
    list-style-type: none;
}

article,
aside,
details,
figcaption,
figure,
footer,
header,
main,
nav,
section,
summary {
    display: block;
}

div,
section,
article,
ul,
li,
th,
td {
    box-sizing: border-box;
}

audio,
canvas,
progress,
video {
    display: inline-block;
}

audio:not([controls]) {
    display: none;
    height: 0;
}

progress {
    vertical-align: baseline;
}

template,
[hidden] {
    display: none;
}

a {
    background-color: transparent;
    -webkit-text-decoration-skip: objects;
    cursor: pointer;
    text-decoration: none;
    color: #000;
}

a:active,
a:hover,
a:focus {
    outline-width: 0;
}


.va-top {
    vertical-align: top;
}


.btn_group {
    margin-top: 30px;
    .klk-button {
        margin: 8px;
    }
}

//from app.vue css
a.router_link_active,
.text_clickable {
    color: var(--primary-color);
}


.agreement_text {
    text-decoration: underline;
    color: var(--primary-color);
}

.agent_icon,
.icon_related_text {
    vertical-align: middle;
    color: #888;
}

.js_agent_log_out {
    cursor: pointer;
}

.router_link {
    cursor: pointer;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
}

.nav_arrow_down {
    width: 10px;
    height: 10px;
    font-size: 12px;
}

body {
    background: #f5f5f5;
}

//end
.theme_color_text {
    color: var(--primary-color);
}


.flash-sale-icon {
    padding: 0 15px;
    height: 22px;
    font-size: 12px;
    color: #ffffff;
    line-height: 22px;
    text-align: center;
    background-image: url('../imgs/flash_sale.png');
    background-size: 100% 100%;
    box-sizing: border-box;
    -webkit-box-sizing: border-box;
    white-space: nowrap;
}

.ul_list {
    background: #fff;
    display: none;
    width: 140px;
    padding: 16px 0;
    z-index: 100;
    text-align: center;
    position: absolute;
    border-radius: 4px;
    box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.24), 0 0 4px 0 rgba(0, 0, 0, 0.12);
    li {
        width: 100%;
        height: 40px;
        line-height: 40px;
        cursor: pointer;
        &.active_li {
            background: #f5f5f5;
        }
        &.active_router_link {
            background: #f5f5f5;
        }
        &:hover {
            background: #f5f5f5;
        }
    }
    &::before {
        content: '';
        position: absolute;
        height: 11px;
        width: 11px;
        background: #fff;
        transform: rotate(45deg);
        top: -6px;
        border-top: 1px solid #ececec;
        border-left: 1px solid #ececec;
    }
}

//show case
.cases {
    display: flex;
    height: 118px;
    white-space: nowrap;

    .case {
        display: flex;
        flex-direction: column;
        width: 320px;
        height: 140px;
        margin-right: 12px;
        background-color: rgba(255, 87, 34, 0.08);
        padding: 20px 24px;
        box-sizing: border-box;
        border-radius: 8px;

        .case_top {
            margin-top: 2px;
            font-size: 16px;
            line-height: 19px;
            font-weight: 600;
            color: #333333;
        }
        .case_middle {
            margin-top: 8px;
            font-weight: 700;
        }
        .case_bottom {
            margin-top: 8px;
            font-size: 12px;
            line-height: 14px;
            color: #999;
        }
        &.left {
            background-image: url('../imgs/balance.png');
            background-size: cover;

            .case_middle {
                color: #ff5722;
                line-height: 26px;
                font-size: 22px;
            }

        }
        &.right {
            background-color: rgba(77,135,229,.08);
            background-image: url('../imgs/due.png');
            background-size: cover;
            border-right: none;
            .case_middle {
                color: var(--primary-color);
                line-height: 26px;
                font-size: 22px;
            }
        }
        &:last-child {
            border-right: none;
            background-color: rgba(77, 135, 229, 0.08);
        }
    }
}

//form input
.input_wrapper {
    width: 100%;
    margin-top: 16px;
    word-wrap: break-word;
    white-space: normal;

    .is_disabled {
        background-color: #fafafa;
        cursor: not-allowed;
        color: #333;
    }
    .message {
        position: absolute;
        font-size: 12px;
        height: 14px;
    }
    &.error {
        .klk-input-inner {
            border-color: #fd5252;
            &:hover,:active,:focus{
                border-color: #fd5252!important;
            }
        }
        .message {
            color: #fd5252;
        }
    }
    .input_label {
        font-size: 14px;
        color: #666;
    }
}
.flash-sale-title {
    margin-bottom: 24px;
    .count-down, h2 {
        display: inline-block;
        vertical-align: middle;
    }
    h2 {
        margin-right: 32px;
        &.main-title {
            margin-bottom: 0 !important;
        }
    }
}

.klk-pagination{
    padding: 30px 0;
}

.klk-markdown a {
    color: #000!important;
    text-decoration: none!important;
}
 .width-100 {
    width: 100px;
}

.width-200 {
    width: 200px;
}

.width-250 {
    width: 250px;
}

.width-520 {
    width: 520px;
}
