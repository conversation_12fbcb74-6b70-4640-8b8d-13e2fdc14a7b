@import '@klook/klook-ui/lib/styles/base.scss';
@import '@klook/klook-ui/lib/styles/components/icon.scss';
@import '@klook/klook-ui/lib/styles/components/button.scss';
@import '@klook/klook-ui/lib/styles/components/input.scss';
@import '@klook/klook-ui/lib/styles/components/badge.scss';
@import '@klook/klook-ui/lib/styles/components/message.scss';
@import '@klook/klook-ui/lib/styles/components/modal.scss';
@import '@klook/klook-ui/lib/styles/components/pagination.scss';
@import '@klook/klook-ui/lib/styles/components/switch.scss';
@import '@klook/klook-ui/lib/styles/components/checkbox.scss';
@import '@klook/klook-ui/lib/styles/components/radio.scss';
@import '@klook/klook-ui/lib/styles/components/select.scss';
@import '@klook/klook-ui/lib/styles/components/date-picker.scss';
@import '@klook/klook-ui/lib/styles/components/poptip.scss';
@import '@klook/klook-ui/lib/styles/components/form.scss';
@import '@klook/klook-ui/lib/styles/components/table.scss';
@import '@klook/klook-ui/lib/styles/components/carousel.scss';
@import '@klook/klook-ui/lib/styles/components/section-title.scss';
@import '@klook/klook-ui/lib/styles/components/markdown.scss';
@import '@klook/klook-ui/lib/styles/components/divider.scss';
@import '@klook/klook-ui/lib/styles/components/toast.scss';
@import '@klook/klook-ui/lib/styles/components/drawer.scss';

input:-webkit-autofill {
  -webkit-box-shadow: 0 0 0 1000px white inset;
  -webkit-text-fill-color: currentColor;
}

.klk-pagination-wrapper {
  margin: 30px 0;
  float: right;
}

.klk-markdown  {
  h1,h2,h3,h4{
    font-size: 16px;
    line-height: 1.38;
    margin-top: 10px;
    padding-bottom: 10px;
  }
}
.klk-form-item{
  &.is-required{
    .klk-form-item-label{
       &::after{
         content: '*';
         color: red!important;
       }
    }
  }
}
