<template>
    <klk-modal
        :scrollable="true"
        :open.sync="visible"
        class="reset-password"
        :title="$t('reset_password')"
        :ok-label="$t('submit')"
        :cancel-label="$t('cancel')"
        @on-cancel="$emit('update:visible', false)"
        @on-confirm="submitForm('form')"
    >
        <klk-form :model="form" :rules="rules" ref="form">
            <klk-form-item :label="$t('new_password')" prop="pwd">
                <klk-input type="password" v-model="form.pwd"> </klk-input>
            </klk-form-item>
            <klk-form-item :label="$t('confirm_password')" prop="repeatPwd">
                <klk-input type="password" v-model="form.repeatPwd">
                </klk-input>
            </klk-form-item>
        </klk-form>
    </klk-modal>
</template>

<script>
export default {
    props: {
        visible: {
            type: Boolean,
            default: false,
        },
    },
    data() {
        const validPwd = (rule, value, callback) => {
            if (!klook.checkPwd(value)) {
                callback(new Error(this.$t('global_password_validate')));
            }
            callback();
        };
        const validateRepeatPwd = (rule, value, callback) => {
            if (value !== '' && value !== this.form.pwd) {
                callback(new Error(this.$t('global_password_not_match')));
            } else {
                callback();
            }
        };
        return {
            rules: {
                pwd: [{ validator: validPwd, trigger: 'blur' }],
                repeatPwd: [{ validator: validateRepeatPwd, trigger: 'blur' }],
            },
            form: {
                pwd: '',
                repeatPwd: '',
            },
        };
    },

    methods: {
        submitForm(formName) {
            this.$refs[formName].validate(valid => {
                if (valid) {
                    this.$emit('update-sub-account', {
                        password: klook.md5(this.form.pwd),
                    });
                } else {
                    return false;
                }
            });
        },
    },
};
</script>

<style lang="scss">
.add-sub-account {
    .select_title {
        width: 85px;
    }
}
</style>
