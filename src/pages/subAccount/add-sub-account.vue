<template>
    <klk-modal
        :scrollable="true"
        :open.sync="visible"
        width="600"
        class="add-sub-account"
        :title="$t('add_sub_account')"
        :ok-label="$t('submit')"
        :cancel-label="$t('cancel')"
        @on-cancel="$emit('update:visible', false)"
        @on-confirm="submitForm('form')"
    >
        <klk-form :model="form" :rules="rules" inline ref="form">
            <klk-form-item :label="$t('title')" prop="title">
                <klk-select v-model="form.title" class="select_title">
                    <klk-option
                        v-for="item in ['MR', 'MRS', 'MISS']"
                        :key="item"
                        :value="item"
                        :label="item"
                    ></klk-option>
                </klk-select>
            </klk-form-item>

            <klk-form-item
                :label="$t('first_name')"
                prop="firstName"
                class="width-200"
            >
                <klk-input v-model="form.firstName"> </klk-input>
            </klk-form-item>
            <klk-form-item
                :label="$t('last_name')"
                prop="lastName"
                class="width-200"
                style="margin-right: 0"
            >
                <klk-input v-model="form.lastName"> </klk-input>
            </klk-form-item>
            <klk-form-item
                :label="$t('email_address')"
                prop="email"
                class="width-250"
            >
                <klk-input v-model="form.email"> </klk-input>
            </klk-form-item>
            <br />
            <klk-form-item
                :label="$t('password')"
                prop="loginPwd"
                class="width-250"
            >
                <klk-input type="password" v-model="form.loginPwd"> </klk-input>
            </klk-form-item>
            <klk-form-item
                :label="$t('confirm_password')"
                prop="confirmLoginPwd"
                class="width-250"
            >
                <klk-input type="password" v-model="form.confirmLoginPwd">
                </klk-input>
            </klk-form-item>
            <klk-form-item
                :label="$t('country_or_regional_code')"
                prop="countryCode"
                class="width-250"
            >
                <klk-select
                    filterable
                    v-model="form.countryCode"
                    class="width_250"
                    :placeholder="$t('global.select.palceholder')"
                >
                    <klk-option
                        v-for="(item, index) in countryCodeObj.main"
                        :key="index"
                        :value="item.country_code"
                        :label="item.country_name"
                    >
                    </klk-option>
                    <klk-option-group
                        v-if="!isCN"
                        :label="$t('other_country_or_regional')"
                    >
                        <klk-option
                            v-for="(item, index) in countryCodeObj.others || []"
                            :key="index"
                            :value="item.country_code"
                            :label="item.country_name"
                        >
                        </klk-option>
                    </klk-option-group>
                </klk-select>
            </klk-form-item>

            <klk-form-item
                :label="$t('phone_number')"
                prop="phoneNumber"
                class="width-250"
            >
                <klk-input v-model="form.phoneNumber"> </klk-input>
            </klk-form-item>
            <klk-form-item
                :label="$t('allow_balance_access')"
                class="width-250"
            >
                <klk-select
                    class="width_250"
                    v-model="form.whetherAllowViewBalance"
                >
                    <klk-option :value="1" :label="$t('yes')"></klk-option>
                    <klk-option :value="0" :label="$t('no')"></klk-option>
                </klk-select>
            </klk-form-item>

            <klk-form-item
                v-show="has_batch_order"
                label="是否允许进行大宗采购"
                class="width-250"
            >
                <klk-select
                    class="width_250"
                    v-model="form.whetherAllowBatchOrder"
                >
                    <klk-option :value="1" :label="$t('yes')"></klk-option>
                    <klk-option :value="0" :label="$t('no')"></klk-option>
                </klk-select>
            </klk-form-item>

            <klk-form-item prop="agree_usage_term">
                <klk-checkbox v-model="form.agree_usage_term">
                    <span
                        v-html="$t('confirm_read_t_c', ['/conditions/'])"
                    ></span>
                </klk-checkbox>
            </klk-form-item>
        </klk-form>
    </klk-modal>
</template>

<script>
import urlObj from '@/common/url';

export default {
    props: {
        visible: {
            type: Boolean,
            default: false,
        },
        countryCodeObj: {
            required: true,
            default: () => {
                return {
                    main: [],
                    others: [],
                };
            },
        },
    },
    data() {
        const lettersOnly = value => {
            return /^[a-zA-ZàáâäãåąčćęèéêëėįìíîïłńòóôöõøùúûüųūÿýżźñçčšžÀÁÂÄÃÅĄĆČĖĘÈÉÊËÌÍÎÏĮŁŃÒÓÔÖÕØÙÚÛÜŲŪŸÝŻŹÑßÇŒÆČŠŽ∂ð ,.'-]+$/i.test(
                value,
            );
        };
        const validateName = (rule, value, callback) => {
            if (!lettersOnly(value)) {
                callback(new Error(this.$t('please_input_english_characters')));
            }
            callback();
        };
        const validEmail = (rule, value, callback) => {
            if (!klook.checkEmail(value)) {
                callback(
                    new Error(this.$t('global_validate_plzEnterValidEmail')),
                );
            }
            callback();
        };
        const validLoginPwd = (rule, value, callback) => {
            if (!klook.checkPwd(value)) {
                callback(new Error(this.$t('global_password_validate')));
            }
            callback();
        };

        const validateConfirmLoginPwd = (rule, value, callback) => {
            if (value !== '' && value !== this.form.loginPwd) {
                callback(new Error(this.$t('global_password_not_match')));
            } else {
                callback();
            }
        };

        const validatePhoneNumber = (rule, value, callback) => {
            if (!klook.checkPhoneNumber(value)) {
                callback(new Error(this.$t('phone_number_validate')));
            }
            callback();
        };

        const validateAgreeUsageTerm = (rule, value, callback) => {
            if (!value) {
                callback(new Error('请阅读条款'));
            }
            callback();
        };

        const requiredRule = {
            required: true,
            message: this.$t('cannot_be_empty'),
            trigger: 'change',
        };
        return {
            rules: {
                title: [requiredRule],
                firstName: [{ validator: validateName, trigger: 'blur' }],
                lastName: [{ validator: validateName, trigger: 'blur' }],
                email: [{ validator: validEmail, trigger: 'blur' }],
                loginPwd: [{ validator: validLoginPwd, trigger: 'blur' }],
                confirmLoginPwd: [
                    { validator: validateConfirmLoginPwd, trigger: 'blur' },
                ],
                countryCode: [requiredRule],
                phoneNumber: [
                    { validator: validatePhoneNumber, trigger: 'blur' },
                ],
                agree_usage_term: [
                    { validator: validateAgreeUsageTerm, trigger: 'blur' },
                ],
            },
            form: {
                title: 'MR',
                firstName: '',
                lastName: '',
                email: '',
                loginPwd: '',
                confirmLoginPwd: '',
                countryCode: '',
                phoneNumber: '',
                whetherAllowViewBalance: 1,
                whetherAllowBatchOrder: 0,
                agree_usage_term: false,
            },
        };
    },
    computed: {
        has_batch_order() {
            return (window.KLK_USER_INFO.permissions || {}).batch_order; //有大宗采购
        },
        isCN() {
            return klook.isCNSite;
        },
    },
    methods: {
        loadCountryCode() {
            klook.ajaxGet(urlObj.agent_account_countries, resp => {
                if (resp.success) {
                    this.countryCodeObj = resp.result || {};
                }
            });
        },
        submitForm(formName) {
            this.$refs[formName].validate(valid => {
                if (valid) {
                    this.addSubAccount();
                } else {
                    return false;
                }
            });
        },
        resetAddAccountForm() {
            this.form.title = 'MR';
            this.form.firstName = '';
            this.form.lastName = '';
            this.form.email = '';
            this.form.loginPwd = '';
            this.form.confirmLoginPwd = '';
            this.form.phoneNumber = '';
            this.form.whetherAllowViewBalance = 1;
            this.form.agree_usage_term = 0;
        },

        addSubAccount() {
            klook.ajaxPostJSON(
                urlObj.agent_sub_account,
                {
                    title: this.form.title,
                    firstName: this.form.firstName,
                    lastName: this.form.lastName,
                    email: this.form.email,
                    password: klook.md5(this.form.loginPwd),
                    countryCode: this.form.countryCode,
                    phoneNumber: this.form.phoneNumber,
                    balanceLookup: !!this.form.whetherAllowViewBalance,
                    whetherAllowBatchOrder: !!this.form.whetherAllowBatchOrder,
                },
                resp => {
                    if (resp.success) {
                        this.$emit('load-sub-account-data');
                        this.resetAddAccountForm();
                        this.$message({
                            type: 'success',
                            message: this.$t('success'),
                        });
                    } else {
                        let errorMessage = '';
                        //无法建立子帐号 (email 已存在)
                        // ERR0011 string = `030011`
                        if (resp.error.code === '030011') {
                            errorMessage = this.$t('email_already_exists');
                        }
                        this.$message({
                            type: 'error',
                            message:
                                errorMessage ||
                                (resp.error && resp.error.message),
                        });
                    }
                },
            );
        },
    },
};
</script>

<style lang="scss">
.add-sub-account {
    padding-bottom: 20px;
    .select_title {
        width: 85px;
    }
}
</style>
