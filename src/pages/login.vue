<template>
    <div id="signin-container" class="signin-container">
        <div class="content">
            <section class="banner" :class="{ 'bania-banner': isBania }">
                <div class="content-layout">
                    <div class="signin-descr">
                        <div class="title">Agent<br />Marketplace</div>
                        <div class="subtitle">{{ $t('login.subtitle') }}</div>
                        <ul class="info">
                            <li>{{ $t('login.intro1') }}</li>
                            <li>{{ $t('login.intro2') }}</li>
                            <li>{{ $t('login.intro3') }}</li>
                            <li>{{ $t('login.intro4') }}</li>
                            <li>{{ $t('login.intro5') }}</li>
                        </ul>
                    </div>
                    <form
                        class="signin-box"
                        onsubmit="return false;"
                        v-loading="isLoggingIn"
                    >
                        <div class="login-title">
                            {{ $t('login.login.header') }}
                        </div>
                        <div :class="{ line: true, error: emailErrorTip }">
                            <klk-input
                                id="username"
                                name="username"
                                :placeholder="$t('login.email')"
                                v-model.trim="username"
                                @blur="validateField('email')"
                            >
                            </klk-input>
                        </div>
                        <div class="line-error">{{ emailErrorTip }}</div>
                        <div :class="{ line: true, error: passwordErrorTip }">
                            <klk-input
                                type="password"
                                :placeholder="$t('login.password')"
                                v-model.trim="password"
                                @blur="validateField('password')"
                            >
                            </klk-input>
                        </div>
                        <div class="line-error">{{ passwordErrorTip }}</div>
                        <div class="line">
                            <span class="remember_me">
                                <klk-checkbox v-model="rememberMe">
                                    {{ $t('login.rememberme') }}
                                </klk-checkbox>
                            </span>
                            <span class="forget_password">
                                <router-link :to="{ name: 'forgetpwd' }"
                                    >{{ $t('login.forget') }}
                                </router-link>
                            </span>
                        </div>
                        <div class="line">
                            <klk-button class="submit" @click="submit" block>
                                {{ $t('login.login') }}
                            </klk-button>
                        </div>
                        <div class="line">
                            <div class="signin_up">
                                <router-link :to="{ name: 'signup' }">
                                    {{ $t('login.signup') }}
                                </router-link>
                            </div>
                        </div>
                    </form>
                </div>
            </section>
            <section class="brand">
                <div class="content-layout">
                    <div class="brand_title">{{ $t('login.partner') }}</div>
                    <div class="brand_subtitle">
                        {{ $t('login.partner.intro') }}
                    </div>
                    <img
                        src="https://res.klook.com/image/upload/fl_lossy.progressive,q_80/c_fill/v1562643512/agent/partnerships_2x_w9ebsz.png"
                        class="brand_img"
                    />
                </div>
            </section>
            <klk-login-reject-dialog
                ref="rejectDialog"
            ></klk-login-reject-dialog>

            <otp-dialog
                ref="otpDialog"
                @otp-validate-success="handleLoginSuccess"
            />
        </div>
        <footer class="footer">
            <div class="company" v-html="footTip"></div>
        </footer>
    </div>
</template>

<script>
import Cookies from 'js-cookie';
import urlObj from '@/common/url';
import { FOOTER_COMPANY } from '~/jv_config';
import { hostWhiteList } from '@/common/const_data';
import KlkLoginRejectDialog from './tpl/login_reject_dialog.vue';
import OtpDialog from './tpl/otp-dialog.vue';

export default {
    name: 'LoginPage',
    data: function() {
        return {
            showClearPassword: false,
            isLoggingIn: false,
            username: '',
            password: '',
            rememberMe: false,
            emailErrorTip: '',
            passwordErrorTip: '',
        };
    },
    components: {
        KlkLoginRejectDialog,
        OtpDialog,
    },
    computed: {
        /**
         *  识别代理商类型的字段，当前是通过handlebars模版暴露为全局变量AGENT_URL_RELATED_DATA，因此在这里获取
         */
        userChannel() {
            return window.KLK_JOINT_VENTRUES ? window.KLK_JOINT_VENTRUES.id : 0;
        },
        footTip() {
            return FOOTER_COMPANY[window.KLK_JV_NAME];
        },
        isBania() {
            return window.KLK_JV_NAME === 'bania';
        },
    },
    methods: {
        switchPasswordInputType() {
            this.showClearPassword = !this.showClearPassword;
        },
        validateField(type) {
            let isValid = true;
            if (type === 'email') {
                isValid = klook.checkEmail(this.username);
                this.emailErrorTip = isValid
                    ? ''
                    : this.$t('login.email.error1');
            } else if (type === 'password') {
                isValid = this.password.length >= 8;
                this.passwordErrorTip = isValid
                    ? ''
                    : this.password.length > 0
                    ? this.$t('login.password.error2')
                    : this.$t('login.password.error1');
            }
            return isValid;
        },
        validHost(urlHost) {
            return (
                hostWhiteList.includes(urlHost) || urlHost.includes('klooktest')
            );
        },
        submit() {
            if (
                !this.validateField('email') ||
                !this.validateField('password') ||
                this.isLoggingIn
            ) {
                return;
            }
            this.emailErrorTip = this.passwordErrorTip = '';
            this.isLoggingIn = true;
            const params = {
                user_channel: this.userChannel,
                username: this.username,
                password: klook.md5(this.password),
                gateway: 'agent',
                refer_url: location.href,
                need_cookie: true,
                login_id: Cookies.get('agent_device_id') || '',
            };

            klook.ajaxPost(
                urlObj.agent_login,
                params,
                res => {
                    this.isLoggingIn = false;
                    if (res.success && res.result) {
                        this.handleLoginSuccess();
                    } else {
                        klook.logger.handlerError(
                            {
                                info: JSON.stringify(res),
                            },
                            { type: 'login error' },
                        );
                        // 610001 --- 未填写 agent 基本信息，重定向到 signup/step2
                        if (res.error && res.error.code === '610001') {
                            window.location = `${window.KLK_LANG_PATH}/signup/step2`;
                            return false;
                        }
                        if (
                            res.error &&
                            (res.error.code === '90100001' ||
                                res.error.code === '90100002')
                        ) {
                            this.$refs.rejectDialog &&
                                this.$refs.rejectDialog.show(
                                    res.error.code,
                                    res.result && res.result.create_date,
                                );
                        } else if (res.error && res.error.code === '90100004') {
                            this.$refs.otpDialog &&
                                this.$refs.otpDialog.show(
                                    res.error.message,
                                    this.username,
                                );
                        } else {
                            if (res.error && res.error.code !== '-9098') {
                                this.passwordErrorTip =
                                    (res.error && res.error.message) ||
                                    'Network error, please retry';
                            }
                        }
                        if (Cookies.get('cookie1')) {
                            Cookies.remove('cookie1');
                        }
                    }
                },
                {},
                { needCaptcha: true },
            );
        },
        logout() {
            klook.ajaxPost(urlObj.agent_logout, { accessType: 4 }, res => {
                if (res.success) {
                    document.cookie = 'cookie1=0;max-age=-1;path=/';
                    window.location = `${window.KLK_LANG_PATH}/signin`;
                }
            });
        },
        handleLoginSuccess() {
            if (this.rememberMe) {
                Cookies.set('remember_user_name', this.username, {
                    path: '/',
                });
            }
            Cookies.set('cookie1', '1', {
                path: '/',
                expires: 7,
            });
            let continue_url = `${decodeURIComponent(
                this.$route.query.continue ||
                    this.$route.params.continue ||
                    window.KLK_LANG_PATH + '/',
            )}`;

            // 联合登录跳转
            let { client_id, redirect_url } = this.$route.query || {};
            if (client_id !== undefined && redirect_url !== undefined) {
                window.location = `${window.KLK_LANG_PATH}/auth/signin?client_id=${client_id}&redirect_url=${redirect_url}`;
                return;
            }
            // test dev
            if (continue_url && window.KLK_ENV !== 'production') {
                window.location = continue_url;
                return;
            }

            try {
                const urlHost = new URL(continue_url).host;
                // 检查抽取的 host 是否在白名单中
                if (this.validHost(urlHost)) {
                    window.location = continue_url;
                } else {
                    window.location = `${window.KLK_LANG_PATH}/index`;
                }
            } catch (error) {
                try {
                    // window.KLK_LANG_PATH 本来不应该加到判断里面，sdk跳转写的有问题，但是暂时没发替换【对面开发休假中 TODO
                    // 构建URL对象
                    const urlObj = new URL(
                        `${window.KLK_LANG_PATH}${continue_url}`,
                        window.location.origin,
                    );
                    const currentPathName = urlObj.pathname;
                    const validPaths = [
                        `${window.KLK_LANG_PATH}/auth/middleware`,
                        `${window.KLK_LANG_PATH}/auth/middleware/`,
                    ];

                    // 判断当前路径是否在合法的路径列表中
                    if (
                        validPaths.includes(currentPathName) &&
                        this.validHost(urlObj.host)
                    ) {
                        window.location.href = urlObj.toString();
                    } else {
                        window.location.href = `${window.KLK_LANG_PATH}/index`;
                    }
                } catch (error) {
                    window.location.href = `${window.KLK_LANG_PATH}/index`;
                }
            }
        },
    },
    mounted() {
        const username = Cookies.get('remember_user_name');
        if (username) {
            this.username = username;
        }
        if (this.$route.query && this.$route.query.accessType === 4) {
            this.logout();
        }
    },
};
</script>
<style lang="scss" scoped>
header.agent_nav {
    border-bottom: none !important;
}

.signin-container {
    min-width: 1160px;

    .content {
        min-height: 100%;
    }

    .banner {
        background: linear-gradient(
                107deg,
                rgba(5, 63, 156, 0.8),
                var(--primary-color)
            ),
            url('https://res.klook.com/image/upload/fl_lossy.progressive,q_65/c_fill/v1533281112/agent/login_banner_bg.jpg')
                no-repeat;
        background-size: cover;
        height: 600px;
    }

    .bania-banner {
        background: no-repeat
            url('https://res.klook.com/image/upload/fl_lossy.progressive,q_65/c_fill/v1591864717/agent/banner_bania_bg.png');
        background-size: cover;

        .info {
            font-weight: bold;
        }
    }

    .content-layout {
        width: 1160px;
        margin: 0 auto;
        position: relative;
    }

    .signin-descr {
        color: #fff;
        padding-top: 96px;

        .title {
            font-size: 54px;
            font-weight: bold;
            line-height: 1.35;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        }

        .subtitle {
            font-size: 30px;
            line-height: 35px;
            margin: 20px 0;
        }

        .info {
            list-style: none;
            font-size: 16px;
            line-height: 18px;

            li {
                margin-bottom: 16px;
                font-style: italic;
                padding-left: 26px;
                position: relative;

                &:before {
                    content: '';
                    position: absolute;
                    top: 1px;
                    width: 16px;
                    height: 16px;
                    background: url('~@/assets/imgs/icon-check.png') no-repeat;
                    background-size: 100% 100%;
                    left: 0;
                }
            }
        }
    }

    .signin-box {
        width: 420px;
        background: #fff;
        position: absolute;
        right: 0;
        top: 105px;
        border-radius: 2px;
        padding: 24px 0 20px;

        .login-title {
            color: var(--primary-color);
            text-align: center;
            font-size: 24px;
            line-height: 28px;
            font-weight: bold;
            margin-bottom: 24px;
        }

        .line {
            padding: 0 40px;
            position: relative;
            overflow: hidden;
        }

        .line-error {
            padding: 10px 40px;
            color: #ff5722;
            font-size: 14px;
            line-height: 20px;
        }

        .switch-password-visibility {
            font-size: 24px;
            position: absolute;
            top: 12px;
            right: 52px;
            cursor: pointer;
            color: #888;
        }

        .remember_me {
            font-size: 14px;
            line-height: 26px;
            float: left;
        }

        .forget_password {
            font-size: 14px;
            line-height: 26px;
            float: right;
        }

        a,
        a:hover,
        a:active {
            color: var(--primary-color) !important;
        }

        a:hover {
            text-decoration: underline;
        }

        button.submit {
            //width: 100%;
            //height: 48px;
            /*border-radius: 2px;*/
            /*text-align: center;*/
            /*font-size: 18px;*/
            /*font-weight: bold;*/
            //background: var(--primary-color);
            //color: #fff;
            margin: 24px 0 32px;
            //border: none;
        }

        .signin_up {
            font-size: 16px;
            line-height: 18px;
            padding-top: 20px;
            border-top: 1px solid #e0e0e0;
            text-align: center;
        }

        .error input {
            border-color: #fd5252;
        }
    }

    .brand {
        background: #fff;
        padding: 64px 0;

        .brand_title {
            font-size: 32px;
            line-height: 37px;
            text-align: center;
            color: #333;
            margin-bottom: 24px;
        }

        .brand_subtitle {
            font-size: 16px;
            line-height: 18px;
            text-align: center;
            color: #999;
            margin-bottom: 24px;
        }

        .brand_img {
            width: 100%;
        }
    }

    .footer {
        padding: 12px 0;
        background: #292929;

        .company {
            text-align: center;
            color: #818181;
            font-size: 13px;
            line-height: 15px;
        }
    }
}
</style>
