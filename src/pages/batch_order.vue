<template lang="html">
    <div class="batch_order" v-loading="loadingData">
        <div class="header">
            <div class="title">{{ $t('bulk_buy') }}</div>
            <div class="desc">
                <span v-html="$t('bulk_but_subTitle', [contactEmail])"></span>
            </div>
        </div>
        <div class="search_area">
            <div :class="['input_wrapper', { error: !activityIdValidate }]">
                <div class="input_label">
                    {{ $t('activity_id') }}
                </div>
                <klk-input
                    class="activity_id_input"
                    v-model="activity_id"
                    @keyup.enter="loadData"
                    :placeholder="$t('input_activity_id')"
                >
                    <klk-button
                        slot="append"
                        class="search_btn"
                        @click="loadData"
                    >
                        {{ $t('find') }}
                    </klk-button>
                </klk-input>
                <span
                    style="padding-bottom:6px;"
                    class="err_msg"
                    v-show="!activityIdValidate"
                    >{{ $t('activity_id_error') }}</span
                >
            </div>
        </div>
        <template
            v-if="package_list_map && Object.keys(package_list_map).length > 0"
        >
            <div class="activity_area">
                <div class="activity_name_label"></div>
                <div class="activity_name">
                    {{ $t('activity_name') }}：{{ activity_name }}
                </div>
                <div class="activity_detail">
                    <div
                        class="package_detail"
                        :key="item.id"
                        v-for="item in package_list_map"
                    >
                        <div class="input_wrapper package_name_wrapper">
                            <klk-checkbox
                                class="check_package"
                                v-model="item.checked"
                            >
                                <span class="package_name">{{
                                    item.name
                                }}</span>
                            </klk-checkbox>
                        </div>
                        <div class="input_wrapper valid_date_wrapper">
                            <div class="input_label">
                                {{ $t('available_date') }}
                            </div>
                            <div class="ticket-item-date">
                                <div class="calender_input_wrapper">
                                    <klk-single-date-picker
                                        format="YYYY-MM-DD"
                                        class="ticket_valid_date"
                                        :isDayBlocked="
                                            date => isDayBlocked(item, date)
                                        "
                                        @input="handleDateChange(item)"
                                        v-model="item.package_valid_date"
                                    >
                                    </klk-single-date-picker>
                                </div>
                                <div>
                                    <klk-select
                                        class="width-250"
                                        v-show="item.selectedTimeSlots.length"
                                        v-model="item.selectedTimeSlot"
                                        @change="
                                            loadPriceList(
                                                item.selectedTimeSlot,
                                                item.id,
                                            )
                                        "
                                    >
                                        <klk-option
                                            v-for="slot in item.selectedTimeSlots"
                                            :key="slot.id"
                                            :value="slot.id"
                                            :label="slot.showTime"
                                        ></klk-option>
                                    </klk-select>
                                </div>
                            </div>
                        </div>
                        <div class="input_wrapper ticket_number_wrapper">
                            <div
                                class="input_label"
                                v-show="
                                    item.selectedTimeSlotRelatedPrices.length
                                "
                            >
                                <ul class="related_price_item_container">
                                    <li
                                        class="related_price_item"
                                        :key="index"
                                        v-for="(priceObj,
                                        index) in item.selectedTimeSlotRelatedPrices"
                                    >
                                        <div
                                            :class="[
                                                'input_wrapper',
                                                {
                                                    error: !checkBatchNumber(
                                                        priceObj.quantity,
                                                    ),
                                                },
                                            ]"
                                            style="margin-top:0"
                                        >
                                            <div class="input_label">
                                                {{ priceObj.name }}（{{
                                                    $t('quantity')
                                                }}）
                                            </div>
                                            <klk-input
                                                v-model="priceObj.quantity"
                                                :disabled="!item.checked"
                                                :placeholder="
                                                    ticket_number_placeholder
                                                "
                                            >
                                            </klk-input>
                                            <span
                                                class="err_msg"
                                                v-show="
                                                    !checkBatchNumber(
                                                        priceObj.quantity,
                                                    )
                                                "
                                                >{{
                                                    ticket_number_placeholder
                                                }}</span
                                            >
                                        </div>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <klk-button class="submit-button" @click="submitOrder">
                {{ $t('submit_order') }}
            </klk-button>
        </template>
        <template v-else>
            <klk-no-data></klk-no-data>
        </template>
    </div>
</template>

<script>
import urlObj from '@/common/url';
import { format, isBefore } from 'date-fns';

export default {
    name: 'BatchOrder',
    data() {
        return {
            loadingData: false,
            dialog_error_tip: '',
            activity_id: '',
            package_list: [], //主动添加了一个selectedTimeRelatedPrices变量去保存根据当前package时间load的priceList
            package_list_map: {}, //对package_list的改造，方便通过packge_id来获取package 数据
            activity_name: '',
            date_picker_loaded: false,
            ticket_number_placeholder: this.$t('agent_input_limit', [20, 1000]),
            submitData: {
                activity_id: '',
                sub_ticket_items: [],
            },
        };
    },

    methods: {
        handleDateChange(item) {
            item.checked = true;
            const date = format(item.package_valid_date, 'YYYY-MM-DD');
            const package_id = item.id;
            let timeSlots =
                this.package_list_map[package_id].time_slots[date] || [];
            item.selectedTimeSlots = timeSlots;
            if (timeSlots.length > 0) {
                item.selectedTimeSlot = timeSlots[0].id;
            }
        },
        isDayBlocked(item, date) {
            const dateStr = format(date, 'YYYY-MM-DD');
            if (!this.package_list_map[item.id].time_slots[dateStr]) {
                return true;
            }
            return (
                isBefore(dateStr, item.start_time) ||
                isBefore(item.end_time, dateStr)
            );
        },
        checkBatchNumber(quantity = '') {
            return (
                quantity.trim() === '' ||
                (+quantity && +quantity >= 20 && +quantity <= 1000)
            );
        },

        submitOrder() {
            if (!this.activity_id) {
                return;
            }
            let submit_data_valid = true;

            function processSubmitData() {
                function checkValidPackageOrder(package_item) {
                    return !(
                        package_item.selectedTimeSlots.length === 0 ||
                        !package_item.checked ||
                        package_item.selectedTimeSlotRelatedPrices.length === 0
                    );
                }

                this.submitData = {
                    activity_id: +this.activity_id,
                    sub_ticket_items: [],
                };
                Object.values(this.package_list_map).forEach(package_item => {
                    if (!checkValidPackageOrder(package_item)) return;
                    let sub_ticket_item = {
                        arrangement_id: package_item.selectedTimeSlot,
                        unit_prices: [],
                    };
                    package_item.selectedTimeSlotRelatedPrices.forEach(
                        unit_price => {
                            if (
                                unit_price.quantity &&
                                this.checkBatchNumber(unit_price.quantity)
                            ) {
                                sub_ticket_item.unit_prices.push({
                                    sku_id: unit_price.id,
                                    quantity: +unit_price.quantity,
                                });
                            }
                        },
                    );
                    if (sub_ticket_item.unit_prices.length > 0) {
                        this.submitData.sub_ticket_items.push(sub_ticket_item);
                    } else {
                        submit_data_valid = false; //打开了package slots 选择功能，但是一个正确的unit也没有
                    }
                });
            }

            function submitDataValid() {
                return submit_data_valid;
            }

            processSubmitData.call(this);
            if (!submitDataValid()) {
                this.$message(this.$t('activity.tips.please_select_unit'));
                return false;
            }

            klook.ajaxPostJSON(
                urlObj.submit_batch_order,
                this.submitData,
                resp => {
                    if (resp.success) {
                        this.$alert(
                            this.$t('bulk_buy_tips'),
                            this.$t('submit_success'),
                        ).then(() => {});
                    } else {
                        this.$alert(
                            resp.error.message || 'failed',
                            this.$t('submit_failed'),
                        ).then(() => {});
                    }
                },
            );
        },

        loadPriceList(time_slot_id, package_id) {
            console.log('load data');
            if (!time_slot_id || !package_id) return;

            klook.ajaxGet(
                urlObj.get_price_list_by_time_slot_id,
                {
                    time_slot_id: time_slot_id,
                },
                resp => {
                    if (resp.success) {
                        this.package_list_map[
                            package_id
                        ].selectedTimeSlotRelatedPrices = resp.result.prices;
                    }
                },
            );
        },
        loadData() {
            if (this.activity_id.trim() === '' || !this.activityIdValidate) {
                return;
            }
            this.loadingData = true;

            function generatePackageListMap(packages) {
                let package_list_map = {};
                packages.forEach(package_item => {
                    package_item.selectedTimeSlotRelatedPrices = [];
                    package_item.selectedTimeSlots = [];
                    package_item.checked = false;
                    package_item.package_valid_date = undefined;
                    package_list_map[package_item.id] = package_item;
                });
                return package_list_map;
            }

            klook.ajaxGet(
                urlObj.search_activity,
                {
                    activity_id: this.activity_id,
                },
                resp => {
                    this.loadingData = false;
                    if (resp.success) {
                        this.package_list_map = generatePackageListMap(
                            resp.result.package_list || [],
                        );
                        this.activity_name = resp.result.activity_name;
                    } else {
                        this.$message({
                            type: 'error',
                            message:
                                (resp.error && resp.error.message) || 'error',
                        });
                        this.package_list_map = {};
                    }
                },
            );
        },
    },
    computed: {
        contactEmail() {
            // 联系邮箱
            // China Agent展示***************
            // Global Agent展示***************
            let isChinaAgent =
                window.KLK_USER_INFO &&
                window.KLK_USER_INFO.agent_category === 'China';
            return isChinaAgent ? '<EMAIL>' : '<EMAIL>';
        },
        activityIdValidate() {
            const id = this.activity_id.trim();
            return id === '' || /^\d+$/.test(id);
        },
    },
};
</script>

<style lang="scss">
.batch_order {
    padding-bottom: 30px;

    .input_label {
        margin-bottom: 10px;
        color: #333333;
    }

    .alert_cancel_btn {
        background-color: #fff;
        margin-right: 16px;
    }

    .ticket-item-date {
        display: flex;
    }

    .email_contact {
        font-weight: bold;
    }

    .submit-button {
        width: 144px;
    }

    .search_btn {
        border-top-left-radius: 0;
        border-bottom-left-radius: 0;
    }

    .error {
        position: relative;

        .err_msg {
            position: absolute;
            left: 0;
            bottom: -30px;
            font-size: 12px;
            visibility: visible;
            color: red;
        }
    }

    .header {
        width: 100%;
        border-bottom: solid 1px #e0e0e0;

        .title {
            height: 33px;
            font-size: 24px;
            color: #333333;
        }

        .desc {
            padding-bottom: 24px;
            font-size: 14px;
            color: #757575;
        }
    }

    .search_area {
        padding: 8px 0 30px 0;
        border-bottom: solid 1px #e0e0e0;

        .input_wrapper {
            width: 400px;

            .klk-button {
                border-radius: 0 !important;
                border: none !important;

                &:hover,
                &:focus,
                &:active {
                    border-radius: 0 !important;
                }
            }
        }
    }

    .activity_area {
        margin-bottom: 30px;

        .package_detail {
            margin-bottom: 30px;
        }

        .activity_name_label {
            font-size: 14px;
            color: #999;
            margin-top: 30px;
            margin-bottom: 12px;
        }

        .activity_name {
            font-weight: 600;
            font-size: 18px;
            padding-bottom: 8px;
        }

        .package_name_wrapper {
            width: auto;
            margin-right: 12px;
            position: relative;
        }

        .package_name {
            font-weight: 600;
            color: #333333;
        }

        .valid_date_wrapper {
            width: 700px;
            margin-right: 20px;
            margin-top: 4px;
        }

        .related_price_item_container {
            width: 700px;
            font-size: 0;

            .related_price_item {
                display: inline-block;
                width: 250px;
                margin-right: 16px;
                margin-top: 10px;

                .err_msg {
                    bottom: -20px;
                }
            }
        }

        .ticket_number_wrapper {
            input {
                width: 250px;
                font-size: 14px;
            }

            margin-top: 0px;
            width: 250px;

            button {
                margin-left: -6px;
            }
        }

        .calender_input_wrapper {
            position: relative;
            display: inline-block;
            margin-right: 20px;

            input {
                width: 250px;
            }

            .klk-icon-calender {
                font-size: 24px;
                position: absolute;
                top: 14px;
                right: 8px;
            }
        }
    }
}
</style>
