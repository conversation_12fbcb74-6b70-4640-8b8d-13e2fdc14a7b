<template>
    <div class="reset-password-page">
        <div class="page-content">
            <div class="page-content-reset" v-if="step === 1">
                <div class="reset-title">{{ $t('reset.password') }}</div>
                <div class="reset-form">
                    <div class="reset-form-tip">
                        {{ $t('reset.password.error1') }}
                    </div>
                    <div class="input-container">
                        <label>{{ $t('reset.newpassword') }}</label>
                        <div class="input-container-content">
                            <klk-input
                                type="password"
                                v-model="newPassword"
                                :placeholder="$t('reset.placeholder.new')"
                            >
                            </klk-input>
                        </div>
                    </div>
                    <div class="input-container">
                        <label>{{ $t('reset.confrimpassword') }}</label>
                        <div class="input-container-content">
                            <klk-input
                                type="password"
                                v-model="confirmPassword"
                            >
                            </klk-input>
                        </div>
                    </div>
                    <div class="input-error-tip" v-show="errorTip">
                        {{ errorTip }}
                    </div>
                    <div
                        class="input-submit"
                        role="button"
                        @click="submit"
                        v-loading="isSubmitting"
                    >
                        {{ $t('reset.confirm') }}
                    </div>
                </div>
            </div>
            <div class="page-content-success" v-show="step === 2">
                <img src="@/assets/imgs/successful_circle.png" />
                <div class="success-title">Reset password successful</div>
                <div class="success-subtitle">
                    You have successfully reset your password and will be
                    redirected to home page in 3 seconds.
                </div>
                <router-link class="input-submit" :to="{ name: 'signin' }"
                    >Login Now</router-link
                >
            </div>
        </div>
    </div>
</template>

<script>
import urlObj from '@/common/url';

export default {
    name: 'ResetPasswordPage',
    data() {
        return {
            newPassword: '',
            confirmPassword: '',
            step: 1,
            seconds: 3,
            isSubmitting: false,
            errorTip: '',
        };
    },
    computed: {
        isValid() {
            return (
                this.newPassword === this.confirmPassword &&
                this.newPassword &&
                this.confirmPassword
            );
        },
    },
    methods: {
        submit() {
            if (!this.isValid) {
                this.errorTip = this.$t('global_password_not_match');
                return;
            }

            if (!klook.checkPwd(this.newPassword)) {
                this.errorTip = this.$t('global_password_validate');
                return;
            }

            if (this.isSubmitting) {
                return;
            }

            this.isSubmitting = true;
            klook.ajaxPost(
                urlObj.agent_token_reset,
                {
                    token: this.$route.query.token,
                    newPassword: klook.md5(this.newPassword),
                },
                res => {
                    this.isSubmitting = false;
                    // "重置密码状态：0 正常，1 token已使用，2 token过期，3 token不存在,4 旧密码错误，5 弱密码，6 密码更新失败
                    if (res.success && res.result) {
                        if (res.result.status === 0) {
                            this.step = 2;
                            return;
                        }
                        switch (res.result.status) {
                            case 0:
                                this.step = 2;
                                return;
                            case 2:
                                this.errorTip = this.$t(
                                    'global_email_link_expired_header',
                                );
                                return;
                            default:
                                this.errorTip = `[${res.result.status}] Reset password failed!`;
                        }
                    } else {
                        this.$message({
                            type: 'error',
                            message:
                                (res.error && res.error.message) || 'failed',
                        });
                    }
                },
            );
        },
    },
};
</script>

<style lang="scss">
.reset-password-page {
    padding: 32px 0 64px;
    background: #f5f5f5;

    .page-content {
        width: 1160px;
        margin: 0 auto;
        background: #fff;
        border-radius: 2px;

        &-reset,
        &-success {
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .reset-title {
            font-weight: bold;
            color: #333;
            font-size: 24px;
            margin: 92px 0 32px;
        }

        .reset-form {
            width: 340px;

            &-tip {
                color: #666;
                font-size: 14px;
                text-align: center;
            }

            .input-container {
                margin-top: 20px;

                &-content {
                    position: relative;
                    margin-top: 11px;
                }

                label {
                    color: #333;
                    font-size: 16px;
                }

                input {
                    padding-right: 48px;

                    &:focus + svg {
                        display: block;
                    }
                }

                svg {
                    // display: none;
                    position: absolute;
                    font-size: 24px;
                    right: 12px;
                    top: 8px;
                    cursor: pointer;
                    color: #888;
                }

                &-error {
                    .klk-input-inner {
                        border-color: #fd5252;
                    }
                }
            }

            .input-error-tip {
                font-size: 12px;
                color: #fd5252;
                margin-top: 12px;
            }
        }

        .input-submit {
            line-height: 48px;
            background: var(--primary-color);
            border-radius: 2px;
            color: #fff;
            font-weight: 500;
            font-size: 18px;
            text-align: center;
            margin: 24px 0 200px;
            cursor: pointer;
            width: 340px;
        }

        &-success {
            img {
                width: 60px;
                height: 60px;
                margin: 92px 0 40px;
            }

            .success-title {
                font-size: 24px;
                color: #333;
            }

            .success-subtitle {
                margin: 24px 0 32px;
                font-size: 16px;
                color: #666;
            }
        }
    }
}
</style>
