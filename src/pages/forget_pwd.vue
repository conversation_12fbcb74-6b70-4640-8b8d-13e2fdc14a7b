<template lang="html">
    <div class="agent_forget_pwd">
        <div class="link_expired_tip" v-show="link_expired">
            {{ $t('global_email_link_expired_header') }}
        </div>
        <div class="input_header">{{ $t('global.forgetpwd') }}</div>
        <div class="input_wrapper">
            <klk-input name="email" v-model="email"> </klk-input>
            <div class="message"></div>
        </div>
        <div class="input_tip">{{ $t('input_find_password_tip') }}</div>
        <klk-button class="agent_forget_pwd_btn" @click="submitForm">
            {{ $t('confirm') }}
        </klk-button>
    </div>
</template>

<script>
import urlObj from '@/common/url';
import { acceptLanguage } from '@/common/const_data';

export default {
    name: 'AgentForgetPwd',
    data() {
        return {
            email: '',
        };
    },
    computed: {
        link_expired() {
            return this.$route.query.expired;
        },
    },
    methods: {
        showDialogSuccess(tip) {
            this.$message({
                type: 'success',
                message: tip || 'success',
            });
        },
        showDialogFail(tip) {
            this.$message({
                type: 'error',
                message: tip || 'failed',
            });
        },
        hideAlldialogs() {
            Object.keys(this.dialog).forEach(key => (this.dialog[key] = false));
        },
        submitForm() {
            if (!this.email) {
                return;
            }
            klook.ajaxPost(
                urlObj.find_pwd,
                {
                    userName: this.email,
                    user_channel: window.KLK_JOINT_VENTRUES.id,
                    role: 'agent',
                },
                resp => {
                    if (resp.success) {
                        this.showDialogSuccess(this.$t('global.hassend.tips'));
                    } else {
                        this.showDialogFail(resp.desc || resp.error.message);
                    }
                },
                acceptLanguage,
            );
        },
    },
};
</script>

<style lang="scss">
.agent_forget_pwd {
    width: 440px;
    border-radius: 2px;
    background-color: #ffffff;
    box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.12);
    margin: 0 auto;
    margin-top: 122px;
    padding: 34px 50px 50px 50px;
    .link_expired_tip {
        width: 100%;
        font-size: 18px;
        line-height: 1.33;
        text-align: left;
        color: #ff5722;
        margin-bottom: 25px;
    }
    .input_header {
        width: 100%;
        margin-bottom: 12px;
        font-size: 18px;
        line-height: 1.33;
        text-align: left;
        color: #424242;
    }
    .input_wrapper {
        margin-bottom: 12px;
    }
    .input_tip {
        width: 100%;
        font-size: 16px;
        height: 66px;
        text-align: left;
        color: #888888;
        font-family: PingFangSC;
        margin-bottom: 30px;
        line-height: 22px;
    }
    .agent_forget_pwd_btn {
        width: 340px;
    }
}
</style>
