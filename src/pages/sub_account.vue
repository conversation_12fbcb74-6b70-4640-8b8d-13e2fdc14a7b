<template lang="html">
    <div class="sub_account">
        <div class="title">{{ $t('sub_accounts') }}</div>
        <div class="search_wrapper">
            <form class="search_content">
                <div class="search_item">
                    <span class="label">
                        {{ $t('account_status') }}
                    </span>
                    <klk-select v-model="search_form.status">
                        <klk-option
                            v-for="(item, index) in status_list"
                            :key="index"
                            :value="index"
                            :label="item"
                        >
                        </klk-option>
                    </klk-select>
                </div>

                <div class="search_item">
                    <span class="label">
                        {{ $t('email_address') }}
                    </span>
                    <klk-input v-model="search_form.email"></klk-input>
                </div>
                <div class="search_item">
                    <span class="label">
                        {{ $t('phone_number') }}
                    </span>
                    <klk-input v-model="search_form.mobile"></klk-input>
                </div>
            </form>

            <klk-button
                class="add_sub_account_btn"
                @click="searchSubAccountData"
            >
                {{ $t('find') }}
            </klk-button>
        </div>
        <klk-button
            class="add_sub_account_btn"
            @click="showAddSubAccountDialog"
            icon="icon_user_user"
        >
            <span>{{ $t('add_sub_account') }}</span>
        </klk-button>
        <klk-button
            type="secondary"
            class="toggle_eye_icon"
            @click="toggleLoadSubAccountData"
            >{{ toggleText }}</klk-button
        >

        <klk-table :columns="cols" :data="tableData">
            <div slot="account-status" slot-scope="{ row }">
                {{ row.freeze ? $t('locked') : $t('normal') }}
            </div>
            <div slot="modify" slot-scope="{ row, rowIndex }">
                <div>
                    <a
                        class="clickable_text"
                        v-show="!row.freeze"
                        @click="showLockAccountDialog(rowIndex)"
                        >{{ $t('lock_account') }}</a
                    >
                </div>
                <div>
                    <a
                        class="clickable_text"
                        v-show="row.freeze"
                        @click="showUnlockAccountDialog(rowIndex)"
                        >{{ $t('unlock_account') }}</a
                    >
                </div>
                <div>
                    <a
                        class="clickable_text"
                        @click="showResetPwdDialog(rowIndex)"
                        >{{ $t('reset_password') }}</a
                    >
                </div>
                <div>
                    <a
                        class="clickable_text"
                        v-show="!row.balanceLookup && !row.freeze"
                        @click="showAllowBalanceAccessDialog(rowIndex)"
                        >{{ $t('allow_balance_access') }}</a
                    >
                </div>
                <div>
                    <a
                        class="clickable_text"
                        v-show="row.balanceLookup && !row.freeze"
                        @click="showRemoveBalanceAccessDialog(rowIndex)"
                        >{{ $t('remove_balance_access') }}</a
                    >
                </div>
                <div v-show="has_batch_order">
                    <a
                        class="clickable_text"
                        v-show="!row.allowBatchOrder"
                        @click="showAllowBatchOrderDialog(rowIndex)"
                        >{{ $t('bulk_buy_visible') }}</a
                    >
                </div>
                <div v-show="has_batch_order">
                    <a
                        class="clickable_text"
                        v-show="row.allowBatchOrder"
                        @click="showRemoveBatchOrderDialog(rowIndex)"
                        >{{ $t('bulk_buy_inVisible') }}</a
                    >
                </div>
                <div>
                    <router-link
                        class="clickable_text"
                        :to="{
                            name: 'bookings',
                            query: {
                                sub_account: tableData[rowIndex].subAccountId,
                            },
                        }"
                    >
                        {{ $t('view_bookings') }}
                    </router-link>
                </div>
            </div>
        </klk-table>
        <klk-pagination
            :total="pagination.total"
            :page-size="pagination.pageSize"
            :current.sync="pagination.curPage"
            @change="handleCurrentChange"
        ></klk-pagination>
        <!--    重置密码-->
        <reset-password
            :visible.sync="dialog.reset_pwd_dialog_show"
            @update-sub-account="updateSubAccount"
        >
        </reset-password>
        <!--    添加子账号-->
        <add-sub-account
            @load-sub-account-data="loadSubAccountData"
            :country-code-obj="countryCodeObj"
            :visible.sync="dialog.add_sub_account_dialog_show"
        ></add-sub-account>
    </div>
</template>

<script>
import urlObj from '@/common/url';
import addSubAccount from './subAccount/add-sub-account.vue';
import resetPassword from './subAccount/reset-password.vue';

export default {
    name: 'SubAccount',
    components: {
        resetPassword,
        addSubAccount,
    },
    data() {
        return {
            is_encryption: true,
            loadingData: false,
            is_china_agent: window.KLK_USER_INFO.agent_category === 'China',
            has_batch_order: (window.KLK_USER_INFO.permissions || {})
                .batch_order, //有大宗采购

            activatedRowIndex: -1,
            operation_content_tip: '',
            tableData: [],
            countryCodeObj: {
                main: [],
                others: [],
            },
            pagination: {
                total: 0,
                pageSize: 5,
                curPage: 1,
            },
            dialog: {
                reset_pwd_dialog_show: false,
                add_sub_account_dialog_show: false,
            },

            dialog_error_tip: '',
            dialog_success_tip: '',
            validation: {},
            status_list: [this.$t('all'), this.$t('normal'), this.$t('locked')],
            search_form: {
                // 搜索条件
                status: 0,
                email: '',
                mobile: '',
            },
        };
    },
    mounted() {
        this.loadSubAccountData();
        this.loadCountryCode();
    },
    computed: {
        cols() {
            return [
                { title: this.$t('first_name'), key: 'firstName' },
                { title: this.$t('last_name'), key: 'lastName' },
                { title: this.$t('email_address'), key: 'email' },
                { title: this.$t('country_code'), key: 'countryCode' },
                { title: this.$t('phone_number'), key: 'phoneNumber' },
                { title: this.$t('account_status'), slot: 'account-status' },
                { title: this.$t('modify'), slot: 'modify', width: '120px' },
            ];
        },
        toggleText() {
            return this.is_encryption
                ? this.$t('show_all_texts')
                : this.$t('hide_partial_texts');
        },
    },
    methods: {
        toggleLoadSubAccountData() {
            this.is_encryption = !this.is_encryption;
            this.loadSubAccountData();
        },
        showDialogSuccess(tip) {
            this.$message({
                type: 'success',
                message: tip || 'success',
            });
        },
        showDialogFail(tip) {
            this.$message({
                type: 'error',
                message: tip || 'error',
            });
        },
        loadCountryCode() {
            klook.ajaxGet(urlObj.agent_account_countries, resp => {
                if (resp.success) {
                    this.countryCodeObj = resp.result || {};
                }
            });
        },

        updateSubAccount(data) {
            let subAccountData = this.tableData[this.activatedRowIndex];
            klook.ajaxPostJSON(
                urlObj.agent_sub_account_update(subAccountData.subAccountId),
                data,
                resp => {
                    if (resp.success) {
                        this.loadSubAccountData();
                        this.$message({
                            type: 'success',
                            message: this.$t('success'),
                        });
                    }
                },
            );
        },

        submitForm(formName) {
            let staticOperation = [
                    'lock',
                    'unlock',
                    'allow_balance_access',
                    'remove_balance_access',
                    'allow_batch_order',
                    'remove_batch_order',
                ],
                actionMapToData = {
                    lock: {
                        freeze: true,
                    },
                    unlock: {
                        freeze: false,
                    },
                    allow_balance_access: {
                        balanceLookup: true,
                    },
                    remove_balance_access: {
                        balanceLookup: false,
                    },
                    allow_batch_order: {
                        allowBatchOrder: true,
                    },
                    remove_batch_order: {
                        allowBatchOrder: false,
                    },
                };
            if (staticOperation.indexOf(formName) !== -1) {
                this.updateSubAccount(actionMapToData[formName]);
            }
        },
        hideAllDialogs() {
            Object.keys(this.dialog).forEach(key => (this.dialog[key] = false));
        },
        formatStr() {
            //匹配 {0}{1}...并替换 formatStr(string,{0},{1}..)
            let str = arguments[0];
            let list = Array.prototype.slice.call(
                arguments,
                1,
                arguments.length,
            );
            let reg = /\{(\d)\}/g;
            return str.replace(reg, function(m, num) {
                return list[num];
            });
        },
        showLockAccountDialog(rowIndex) {
            this.activatedRowIndex = rowIndex;
            // 去标加密: 去掉名字
            const content = this.formatStr(this.$t('whether_lock_sb_account'));

            this.$confirm(content).then(({ result }) => {
                if (result) {
                    this.submitForm('lock');
                }
            });
        },
        showUnlockAccountDialog(rowIndex) {
            this.activatedRowIndex = rowIndex;
            // 去标加密: 去掉名字
            const content = this.formatStr(
                this.$t('whether_unlock_sb_account'),
            );
            this.$confirm(content).then(({ result }) => {
                if (result) {
                    this.submitForm('unlock');
                }
            });
        },
        showAddSubAccountDialog() {
            this.dialog.add_sub_account_dialog_show = true;
        },

        showResetPwdDialog(rowIndex) {
            this.dialog.reset_pwd_dialog_show = true;
            this.activatedRowIndex = rowIndex;
        },
        showAllowBalanceAccessDialog(rowIndex) {
            this.activatedRowIndex = rowIndex;
            // 去标加密: 去掉名字
            const content = this.formatStr(
                this.$t('whether_allow_sb_access_to_balance'),
                '',
            );
            this.$confirm(content).then(({ result }) => {
                if (result) {
                    this.submitForm('allow_balance_access');
                }
            });
        },
        showRemoveBalanceAccessDialog(rowIndex) {
            this.activatedRowIndex = rowIndex;
            // 去标加密: 去掉名字
            const content = this.formatStr(
                this.$t('whether_remove_sb_access_to_balance'),
                '',
            );
            this.$confirm(content).then(({ result }) => {
                if (result) {
                    this.submitForm('remove_balance_access');
                }
            });
        },

        showAllowBatchOrderDialog(rowIndex) {
            this.activatedRowIndex = rowIndex;
            // 去标加密: 去掉名字
            const content = this.formatStr('是否允许{0}进行大宗采购？', '');
            this.$confirm(content).then(({ result }) => {
                if (result) {
                    this.submitForm('allow_batch_order');
                }
            });
        },
        showRemoveBatchOrderDialog(rowIndex) {
            this.activatedRowIndex = rowIndex;
            // 去标加密: 去掉名字
            const content = this.formatStr('是否禁止{0}进行大宗采购？', '');
            this.$confirm(content).then(({ result }) => {
                if (result) {
                    this.submitForm('remove_batch_order');
                }
            });
        },
        handleCurrentChange(curPage) {
            this.pagination.curPage = curPage;
            this.loadSubAccountData();
        },
        searchSubAccountData() {
            this.pagination.curPage = 1;
            this.loadSubAccountData();
        },
        loadSubAccountData() {
            this.hideAllDialogs();
            this.loadingData = true;
            const data = Object.assign(
                {},
                {
                    page: this.pagination.curPage,
                    per_page: this.pagination.pageSize,
                },
                this.search_form,
            );

            const url = this.is_encryption
                ? urlObj.agent_sub_account
                : `${urlObj.agent_sub_account}?show_original=1`;

            klook.ajaxGet(url, data, resp => {
                this.loadingData = false;
                if (resp.success) {
                    const result = resp.result || {};
                    this.pagination.total = result.totalCount;
                    this.tableData = result.subAccounts;
                } else {
                    this.showDialogFail(resp.error && resp.error.message);
                }
            });
        },
    },
};
</script>

<style lang="scss">
.sub_account {
    .toggle_eye_icon {
        margin-left: 20px;
        border-color: #999;
        color: #666;
    }
    .clickable_text {
        color: var(--primary-color);
    }

    .header {
        font-size: 24px;
        color: #424242;
        margin-top: 32px;
        text-align: center;
    }

    .search_wrapper {
        margin-top: 20px;
        display: flex;
        justify-content: space-between;

        .search_content {
            width: 800px;
            display: flex;

            .search_item {
                flex: 1;
                padding-right: 20px;
                display: flex;
                align-items: center;

                .label {
                    display: inline-block;
                    flex: 0 0 56px;
                    padding-right: 8px;
                }
            }
        }
    }

    .title {
        width: 100%;
        height: 52px;
        font-size: 24px;
        color: #333333;
        border-bottom: solid 1px #e0e0e0;
    }

    .add_sub_account_btn {
        margin: 20px 0 32px;
    }

    .status_dialog {
        width: 356px;
        line-height: 20px;
        padding: 0 0 22px;

        .content {
            margin: 60px 50px 0;
        }
    }

    .set_pwd_dialog {
        input {
            width: 296px;
        }

        .header {
            height: 40px;
            line-height: 40px;
        }

        .pwd_tip {
            font-size: 14px;
            color: #888888;
            margin: 10px 0 24px;
            text-align: center;
        }
    }
}
</style>
