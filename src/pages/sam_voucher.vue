<template>
    <div class="otp" v-loading="loading">
        <div class="logo">
            <img
                src="https://res.klook.com/image/upload/v1640168445/UED%20Team%EF%BC%88for%20DE%20only%EF%BC%89/Email/CN_logo_3x.png"
            />
        </div>
        <div class="opt-content">
            <div class="tip">
                <div>身份验证</div>
                为保证凭证安全，请输入联系手机号后四位进行身份验证
            </div>
            <div v-if="mobile" class="phone-number">
                {{ mobile }}
            </div>
            <OtpInput
                v-model="code"
                class="opt-input"
                :class="{
                    'opt-input--error': errorMessage && !inputDisabled,
                    'opt-input--disabled': inputDisabled,
                }"
                :code-length="4"
                :input-disabled="inputDisabled"
                @on-complete="handleCodeInputComplete"
            />
            <div v-if="errorMessage" class="error-container">
                <img
                    src="https://res.klook.com/image/upload/v1741155741/pihwkdzelqgeik7ufqxr.png"
                    class="icon"
                />
                <span class="error-text">{{ errorMessage }}</span>
            </div>
        </div>
    </div>
</template>

<script>
import urlObj from '@/common/url';
import OtpInput from '@/components/otp/otp-input.vue';

export default {
    name: 'samVoucher',
    components: {
        OtpInput,
    },
    data() {
        return {
            loading: false,
            code: '',
            errorMessage: '',
            inputDisabled: false,
        };
    },
    computed: {
        queryData() {
            return this.$route.query;
        },
        mobile() {
            const { mobile } = this.queryData;
            if (/^[0-9\-*]+$/.test(mobile)) {
                return mobile;
            }
            return '参数错误';
        },
    },
    mounted() {},
    methods: {
        handleCodeInputComplete(code) {
            this.loading = true;
            klook.ajaxGet(
                urlObj.sam_ticket_voucher,
                {
                    token: this.queryData.token,
                    code,
                },
                resp => {
                    this.loading = false;
                    this.errorMessage = '';
                    this.inputDisabled = false;
                    const { success, result, error } = resp;
                    if (success && result) {
                        window.location.replace(result);
                    } else if (error && error.code === 'P0100') {
                        // 超过5次
                        this.errorMessage = error.message;
                        this.inputDisabled = true;
                    } else {
                        // 验证失败
                        this.errorMessage = error.message;
                    }
                },
            );
        },
    },
};
</script>

<style>
#headerContainer {
    display: none;
}

#app {
    padding-top: 22px;
}
</style>
<style lang="scss" scoped>
.otp {
    padding: 0 22px;

    .logo {
        width: 118px;

        img {
            width: 100%;
        }
    }

    .opt-content {
        padding-top: 40px;
    }

    .tip {
        font-weight: 400;
        font-size: 16px;
        line-height: 150%;
        color: #757575;
        div {
            font-weight: 600;
            font-size: 28px;
            line-height: 132%;
            margin-bottom: 12px;
            color: #212121;
        }
    }

    .phone-number {
        font-weight: 600;
        font-size: 16px;
        line-height: 24px;
        width: 100%;
        margin-bottom: 32px;
    }
    .error-container {
        display: flex;
        align-items: flex-start;
        color: #f44622;
        margin-top: 6px;

        .icon {
            width: 16px;
            display: block;
            margin-right: 4px;
        }

        .error-text {
            font-size: 14px;
            font-weight: 400;
        }
    }
    .opt-input {
        width: 228px;
        &--error {
            ::v-deep .klk-input-inner,
            ::v-deep .klk-otp-validate_label {
                border-color: #f44622;
            }
        }
        &--disabled {
            ::v-deep .klk-input-inner,
            ::v-deep .klk-otp-validate_label {
                background-color: rgba(250, 250, 250, 1);
            }
        }
    }
}
</style>
