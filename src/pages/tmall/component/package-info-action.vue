<template>
    <div class="package-info-action">
        <span
            >相关同业订单编号
            <span class="booking-number">{{ bookingRefNo }}</span></span
        >
        <span> {{ bookingTimeUtc ? `预定日期 ${bookingTimeUtc}` : '' }}</span>
        <span> {{ totalAmount ? `总金额 ${totalAmount}` : '' }}</span>
        <span
            :class="{
                clickable_text: !send_status,
                'disabled-tap': send_status,
            }"
            @click="hanlePostCode"
            >飞猪发码</span
        >
        <span
            :class="{
                clickable_text: !consume_status,
                'disabled-tap': consume_status,
            }"
            @click="hanleCanelVerf"
            >飞猪核销</span
        >
    </div>
</template>

<script>
export default {
    name: 'activity-and-package',
    props: {
        bookingRefNo: {
            type: [Number, String],
            required: true,
        },
        bookingTimeUtc: {
            type: String,
        },
        totalAmount: {
            type: String,
        },
        send_status: {
            type: Number,
        },
        consume_status: {
            type: Number,
        },
    },
    methods: {
        hanlePostCode() {
            if (!this.send_status) {
                this.$emit('handle-post-code');
            }
        },
        hanleCanelVerf() {
            if (!this.consume_status) {
                this.$emit('handle-cancel-verf');
            }
        },
    },
};
</script>

<style lang="scss">
.package-info-action {
    width: 100%;
    > span {
        display: block;
        margin: 5px 0;
    }
    .booking-number {
        color: var(--primary-color);
    }
}
</style>
