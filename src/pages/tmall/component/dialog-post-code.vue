<template>
    <klk-modal
        :open.sync="editPostCode"
        :closable="true"
        title="飞猪发码"
        @on-close="cancelBooking"
        @on-cancel="cancelBooking"
        @on-confirm="submitBooking"
        class="dialog_change_alert"
    >
        <div class="input_booking">
            <klk-input type="text" v-model="code"> </klk-input>
            <p class="error-tips">{{ errorTip }}</p>
        </div>
    </klk-modal>
</template>

<script>
export default {
    data() {
        return {
            code: '',
        };
    },
    props: {
        editPostCode: {
            type: Boolean,
            default: false,
        },
        value: {
            type: String | Number,
        },
        errorTip: {
            type: String,
        },
    },
    watch: {
        value(val) {
            this.code = val;
        },
    },
    created() {
        this.code = this.value;
    },
    methods: {
        submitBooking() {
            this.$emit('submit-booking', this.code);
        },
        cancelBooking() {
            this.$emit('cancel-booking');
            this.code = this.value;
        },
    },
};
</script>

<style lang="scss">
.dialog_change_alert {
    .title {
        margin-top: 10px;
        text-align: left;
        height: 40px;
        line-height: 40px;
        font-weight: bold;
        font-size: 16px;
    }
    .input_booking {
        margin: 20px 0;
        text-align: center;
        .error-tips {
            margin-top: 6px;
            overflow-y: scroll;
            max-height: 200px;
            font-style: normal;
            color: red;
            font-size: 12px;
        }
    }
}
</style>
