<template>
    <div class="tmall-page">
        <div class="search-wrapper">
            <div class="search-header">
                <h3 class="title">Tmall Bookings</h3>
                <div class="search-input-wrapper">
                    <p class="search-title">订单号</p>
                    <klk-input
                        size="small"
                        class="search-input"
                        v-model="tmallInfo.tmall_order_id"
                        placeholder="请输入"
                    >
                        <span
                            slot="append"
                            class="search-icon"
                            @click="handleSearch"
                        >
                            <klk-icon type="icon_edit_search" size="16" />
                        </span>
                    </klk-input>
                </div>
            </div>
            <hr />
            <div class="search-content">
                <booking-date-range
                    v-model="tmallInfo.bookingDate"
                    label="日期范围"
                    :hasRangeSelect="true"
                />
                <booking-select
                    v-model="tmallInfo.send_status"
                    :options="sendStatusList"
                    label="飞猪发码状态"
                />
                <booking-select
                    v-model="tmallInfo.consume_status"
                    :options="sendConsumeList"
                    label="飞猪核销状态"
                />
            </div>
            <klk-button class="search" @click="handleSearch">搜索</klk-button>
        </div>
        <hr />
        <div class="result-wrapper">
            <div class="result-header">
                <h2 class="header-left">订单记录</h2>
                <div class="header-right">
                    <klk-button
                        size="small"
                        type="secondary"
                        @click="handleConsumeCode('')"
                    >
                        批量核销订单
                    </klk-button>
                </div>
            </div>
            <klk-table
                class="result-table"
                v-loading="loading"
                :columns="columns"
                :data="tableData"
                :span-method="rowSpanMethod"
                selectable
                :selected.sync="selected"
                :row-selectable="rowSelectable"
                @select="hanleSelected"
                border
            >
                <package-info-action
                    slot="packageAction"
                    slot-scope="{ row }"
                    :bookingRefNo="row.tmall_order_id"
                    :bookingTimeUtc="getRuleDate(row.booking_time_utc)"
                    :totalAmount="row.payment_amount"
                    :send_status="row.send_status"
                    :consume_status="row.consume_status"
                    @handle-post-code="handleSendCode(row)"
                    @handle-cancel-verf="handleConsumeCode(row.tmall_order_id)"
                />
                <activity-and-package
                    slot="activityPackage"
                    slot-scope="{ row }"
                    :activityId="row.activity_id"
                    :activityName="row.activity_name"
                    :packageName="row.package_name"
                />
                <unit-info
                    slot="units_info"
                    slot-scope="{ row }"
                    :unit-info="row.units_info"
                    :currencySymbol="currencySymbol"
                />
                <div slot="status" slot-scope="{ row }" class="status">
                    <span class="hover_tip">{{ row.status_label }}</span>
                    <klk-icon
                        :color="statusTypeList[row.status].color"
                        :type="statusTypeList[row.status].type"
                    ></klk-icon>
                </div>
                <div slot="consume_status" slot-scope="{ row }">
                    {{ getSendStatus(+row.consume_status) }}
                </div>
                <div
                    slot="note"
                    slot-scope="{ row, rowIndex }"
                    class="note"
                    @mouseenter="handleNote(rowIndex)"
                    @mouseleave="row.editNote = false"
                >
                    <span v-if="!row.editNote">{{ row.note }}</span>
                    <input
                        v-if="row.editNote"
                        type="text"
                        v-model="row.note"
                        @blur="
                            handleUpdateNote(
                                row.note,
                                rowIndex,
                                row.tmall_order_id,
                            )
                        "
                    />
                </div>
                <div slot="voucher" slot-scope="{ row }">
                    <a
                        href="javascript:;"
                        @click="
                            row.voucher_url ? openVouch(row.voucher_url) : ''
                        "
                        :class="
                            (row.status === 4 || row.status === 8) &&
                            row.voucher_url
                                ? 'clickable_text'
                                : 'disabled-tap'
                        "
                    >
                        查看凭证</a
                    >
                </div>
            </klk-table>
        </div>

        <klk-pagination
            :current.sync="pagination.curPage"
            :page-size="pagination.pageSize"
            :total="pagination.total"
            @change="handleCurrentChange"
        ></klk-pagination>
        <dialog-post-code
            v-model="postCode"
            :edit-post-code="editPostCode"
            :error-tip="errorTip"
            @submit-booking="submitPostCode"
            @cancel-booking="
                editPostCode = false;
                errorTip = '';
            "
        />
    </div>
</template>
<script>
import find from 'lodash/find';
import urlObj from '@/common/url';
import { subMonths, format, startOfDay, addDays } from 'date-fns';
import bookingDateRange from '@/pages/bookings/components/booking-date-range.vue';
import bookingSelect from '@/pages/bookings/components/booking-select.vue';
import activityAndPackage from '@/pages/bookings/components/activity-package.vue';
import unitInfo from '@/pages/bookings/components/unit-info.vue';
import {
    getRuleDate,
    booking_status_list,
    statusTypeList,
} from '@/common/const_data';
import { openVouch, formatDateRFC3339 } from '@/common/util';
import packageInfoAction from './component/package-info-action.vue';
import dialogPostCode from './component/dialog-post-code.vue';

export default {
    name: 'TmallBooking',
    data() {
        const columns = [
            {
                title: '',
                width: '200px',
                slot: 'packageAction',
            },
            {
                title: '活动及套餐',
                width: '200px',
                slot: 'activityPackage',
            },
            {
                title: '出行日期',
                width: '130px',
                key: 'participation_date',
            },
            {
                title: '购买数量&价格',
                width: '100px',
                slot: 'units_info',
            },
            {
                title: '退款金额',
                width: '100px',
                key: 'refunded_amount',
            },
            {
                title: '状态',
                width: '120px',
                slot: 'status',
            },
            {
                title: '飞猪码',
                width: '130px',
                key: 'voucher_code',
            },
            {
                title: '飞猪核销状态',
                width: '130px',
                slot: 'consume_status',
            },
            {
                title: '备注',
                slot: 'note',
                width: '150px',
            },
            {
                title: '操作',
                slot: 'voucher',
                width: '120px',
            },
        ];
        return {
            columns,
            tmallInfo: {
                bookingDate: {
                    dateStart: subMonths(startOfDay(new Date()), 1),
                    dateEnd: startOfDay(new Date()),
                },
                send_status: -1,
                consume_status: -1,
                tmall_order_id: '',
            },
            selected: [],
            loading: false,
            tableData: [],
            currencySymbol: '',
            pagination: {
                total: 0,
                pageSize: 5,
                curPage: 1,
            },
            postCode: '',
            editPostCode: false,
            errorTip: '',
            curMashangId: '',
            selectList: new Set(),
        };
    },
    computed: {
        statusTypeList() {
            return Object.assign(statusTypeList, { '-1': 'coupon-info' });
        },
        sendStatusList() {
            return [
                {
                    label: '全部',
                    value: -1,
                },
                {
                    label: '未发码',
                    value: 0,
                },
                {
                    label: '已发码',
                    value: 1,
                },
            ];
        },
        sendConsumeList() {
            return [
                {
                    label: '全部',
                    value: -1,
                },
                {
                    label: '未核销',
                    value: 0,
                },
                {
                    label: '已核销',
                    value: 1,
                },
            ];
        },
        bookingStatusList() {
            return booking_status_list();
        },
    },
    components: {
        bookingDateRange,
        packageInfoAction,
        activityAndPackage,
        unitInfo,
        bookingSelect,
        dialogPostCode,
    },
    mounted() {
        this.loadData();
    },
    methods: {
        openVouch,
        getRuleDate,
        successMessage(msg) {
            this.$message({
                type: 'success',
                message: msg,
            });
        },
        warningMessage(msg) {
            this.$message({
                type: 'warning',
                message: msg,
            });
        },
        rowSelectable(row) {
            return !row.consume_status;
        },
        getSendStatus(value) {
            return this.sendConsumeList.find(item => item.value === value)
                .label;
        },
        hanleSelected(row) {
            this.tableData.forEach(item => {
                if (this.selectList.has(item.tmall_order_id)) {
                    this.selectList.delete(item.tmall_order_id);
                }
            });
            row.map(v => {
                this.selectList.add(v.tmall_order_id);
            });
        },
        loadData() {
            const processData = list => {
                let arrItem = [];
                list.forEach(cur => {
                    if (cur.tickets && cur.tickets.length) {
                        (cur.tickets || []).forEach((ticket, index) => {
                            ticket.tmall_order_id = cur.tmall_order_id;
                            ticket.klk_order_no = cur.klk_order_no;
                            ticket.booking_time_utc = cur.booking_time_utc;
                            ticket.payment_amount = cur.payment_amount;
                            ticket.voucher_code = cur.voucher_code;
                            ticket.send_status = cur.send_status;
                            ticket.consume_status = cur.consume_status;
                            ticket.note = cur.note;
                            ticket.refunded_amount = `${this.currencySymbol}${ticket.refunded_amount}`;
                            ticket.participation_date = ticket.participation_date
                                ? format(
                                      ticket.participation_date,
                                      'YYYY-MM-DD',
                                  )
                                : '';
                            ticket.status_label =
                                this.bookingStatusList.find(
                                    item => item.value == ticket.status,
                                ).label || '';
                            ticket.editNote = false;
                            ticket.rowspan =
                                index === 0 ? cur.tickets.length : 0;
                            arrItem.push(ticket);
                        });
                    } else {
                        cur.status = -1;
                        cur.status_label = '未生成';
                        cur.rowspan = 1;
                        cur.editNote = false;
                        arrItem.push(cur);
                    }
                });
                return arrItem;
            };

            const searchData = {
                tmall_order_id: this.tmallInfo.tmall_order_id,
                send_status: this.tmallInfo.send_status,
                consume_status: this.tmallInfo.consume_status,
                start_time: formatDateRFC3339(
                    this.tmallInfo.bookingDate.dateStart,
                ),
                end_time: formatDateRFC3339(
                    addDays(this.tmallInfo.bookingDate.dateEnd, 1),
                ),
                page_size: this.pagination.pageSize,
                page_no: this.pagination.curPage,
            };
            this.loading = true;
            klook.ajaxGet(urlObj.tmall.get_order_list, searchData, resp => {
                this.loading = false;
                if (resp.success) {
                    this.pagination.total = resp.result.count;
                    this.tableData = processData(resp.result.list || []);
                    this.selected = this.tableData.filter(item =>
                        this.selectList.has(item.tmall_order_id),
                    );
                    this.currencySymbol = find(
                        this.currencyList,
                        item =>
                            item.value === (resp.result || {}).currency_code,
                    ).title.split(' | ')[1];
                } else {
                    this.tableData = [];
                }
            });
        },
        handleCurrentChange(curPage) {
            this.pagination.curPage = curPage;
            this.loadData();
        },
        rowSpanMethod(row, col, rowIndex, columnIndex) {
            let columnArr = [0, 1, 7, 8, 9];
            if (columnArr.includes(columnIndex)) {
                if (row.rowspan) {
                    return {
                        rowspan: row.rowspan,
                        colspan: 1,
                    };
                } else {
                    return {
                        rowspan: 0,
                        colspan: 0,
                    };
                }
            }
        },
        handleNote(index) {
            this.tableData[index].editNote = true;
        },
        handleUpdateNote(note, rowIndex, id) {
            this.tableData[rowIndex].editNote = false;
            klook.ajaxPostJSON(
                urlObj.tmall.update_note(id),
                {
                    note: note,
                },
                resp => {
                    if (resp.success) {
                        this.successMessage('修改成功');
                    } else {
                        this.warningMessage('修改失败');
                    }
                },
            );
        },
        handleSearch() {
            this.pagination.curPage = 1;
            this.loadData();
        },
        handleSendCode(row) {
            this.editPostCode = true;
            this.postCode = row.voucher_code;
            this.curMashangId = row.tmall_order_id;
        },
        submitPostCode(code) {
            klook.ajaxPostJSON(
                urlObj.tmall.send_code(this.curMashangId),
                {
                    voucher_code: code,
                },
                resp => {
                    if (resp.success) {
                        this.editPostCode = false;
                        this.postCode = '';
                        this.successMessage('发码成功');
                        this.loadData();
                    } else {
                        this.warningMessage(resp.error.message);
                        this.errorTip = resp.error.message;
                    }
                },
            );
        },
        handleConsumeCode(val) {
            let ids = [val];
            if (!val) {
                ids = [...this.selectList];
            }
            klook.ajaxPostJSON(
                urlObj.tmall.cancel_after_vertication,
                {
                    tmall_order_ids: ids,
                },
                resp => {
                    if (resp.success) {
                        this.successMessage('核销成功');
                        this.loadData();
                    } else {
                        this.warningMessage(resp.error.message);
                    }
                },
            );
        },
    },
};
</script>

<style lang="scss">
.tmall-page {
    .search-wrapper {
        width: 100%;
        margin: 20px 0 40px;
        .search-header {
            .title {
                text-align: left;
            }
            text-align: right;
            .search-input-wrapper {
                display: inline-flex;
                align-items: center;
                .search-title {
                    padding-right: 6px;
                }
                .search-input {
                    width: 260px;
                    .search-icon {
                        cursor: pointer;
                        padding: 0 6px;
                    }
                }
            }
            margin-bottom: 20px;
        }
        .input_wrapper {
            display: inline-block;
            width: auto;
            margin-right: 10px;
            .input_label {
                margin-bottom: 6px;
                width: auto;
            }
            .klk-select {
                width: 237px;
            }
        }
        .search {
            margin: 24px 0 30px 0;
            width: 112px;
        }
    }
    .result-wrapper {
        .result-header {
            margin: 20px 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
            .header-left {
                color: var(--primary-color);
            }
        }
        .result-table {
            input {
                width: auto;
            }
            .klk-table-cell {
                overflow: visible;
                word-break: break-all;
            }
            .status {
                position: relative;
                overflow: visible;
                span.hover_tip {
                    top: 15%;
                    padding: 5px 8px;
                    background-color: #fff;
                    border: 1px solid #ccc;
                    box-shadow: 0px 0px 5px #bbb;
                    border-radius: 2px;
                    position: absolute;
                    z-index: 99;
                    display: none;
                }
                &:hover span {
                    display: initial;
                }
            }
            .note {
                cursor: pointer;
                width: auto;

                input {
                    width: 100px;
                }
                span {
                    word-break: normal;
                    width: auto;
                    display: block;
                    white-space: pre-wrap;
                    word-wrap: break-word;
                }
                min-height: 20px;
            }
        }
    }
    .clickable_text {
        cursor: pointer;
        color: var(--primary-color);
    }
    .disabled-tap {
        color: #d5d5d5;
        cursor: not-allowed;
    }
}
</style>
