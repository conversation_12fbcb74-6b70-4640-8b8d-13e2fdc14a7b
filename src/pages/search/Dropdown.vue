<template>
    <div :class="{ 'dropdown-component': true, 'dropdown-component-show': visible }">
        <slot name="input" :show="onFocus" :hide="onBlur"></slot>
        <div class="dropdown-component-content" v-show="visible">
            <slot :hide="onBlur"></slot>
        </div>
    </div>
</template>

<script>
export default {
    name: 'DropdownComponent',
    data() {
        return {
            visible: false
        }
    },
    watch: {
        visible(newVal) {
            if (newVal === true) {
                this.$emit('show');
                document.addEventListener('mousedown', this.onMouseDown);
            } else {
                document.removeEventListener('mousedown', this.onMouseDown);
            }
        }
    },
    methods: {
        onFocus() {
            this.visible = true;
        },
        onBlur() {
            const activeEl = document.activeElement;
            if (this.$el && this.$el.contains(activeEl)) {
                return ;
            }
            this.visible = false;
        },
        onMouseDown(e) {
            const isDescendantOfRoot = this.$el && this.$el.contains(e.target);
            if (!isDescendantOfRoot) {
                this.visible = false;
            }
        }
    }
}
</script>

<style lang="scss">
.dropdown-component {
    position: relative;

    &-content {
        position: absolute;
        left: -1px;
        background: #fff;
        border-radius: 2px;
        border: solid 1px #e0e0e0;
        box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.08);
        min-width: 100%;
        box-sizing: content-box;
        z-index: 101;
    }
}
</style>
