<template>
    <div class="group-template-item">
        <checkbox-component
            class="group-template-item-tag group-template-item-name"
            :value="parentChoosed"
            @input="val => toggle(this.template, val, true)"
        >
            <label>{{ this.template.name }}</label>
        </checkbox-component>
        <div v-if="template.children" class="group-template-item-tags">
            <checkbox-component
                v-for="(tag, index) in template.children"
                :key="index"
                class="group-template-item-tag"
                :value="isChecked(tag)"
                @input="val => toggle(tag, val, false)"
            >
                <label>{{ tag.name }}</label>
            </checkbox-component>
        </div>
    </div>
</template>

<script>
import CheckboxComponent from './Checkbox.vue';

export default {
    name: 'GroupTemplateItem',
    components: {
        CheckboxComponent,
    },
    props: {
        template: { type: Object, default: () => {} },
        value: { type: Array, default: () => [] },
    },
    computed: {
        parentChoosed() {
            return this.value.includes(this.template.id);
        },
    },
    methods: {
        isChecked(tag) {
            return this.parentChoosed || this.value.includes(tag.id);
        },
        toggle(tag, val, isParent) {
            const siblingTags = this.template.children || [];
            if (val === true) {
                if (
                    isParent ||
                    siblingTags.every(
                        siblingTag =>
                            this.value.includes(siblingTag.id) ||
                            siblingTag === tag,
                    )
                ) {
                    this.$emit('input', [this.template.id]);
                    return;
                }
                this.$emit('input', [...this.value, tag.id]);
            } else if (val === false) {
                if (isParent) {
                    this.$emit('input', []);
                    return;
                }
                if (this.parentChoosed) {
                    this.$emit(
                        'input',
                        siblingTags
                            .filter(siblingTag => siblingTag !== tag)
                            .map(t => t.id),
                    );
                    return;
                }
                this.$emit(
                    'input',
                    this.value.filter(id => id !== tag.id),
                );
            }
        },
    },
};
</script>

<style lang="scss">
.group-template-item {
    border-bottom: 1px solid #eee;

    &-tag {
        display: flex;
        align-items: center;
        margin-right: 18px;
        margin-bottom: 10px;
        color: #666;

        &:last-child {
            margin-right: 0;
        }

        label {
            margin-left: 8px;
            white-space: nowrap;
        }
    }

    &-name {
        font-weight: 500;
        color: #333;
        font-size: 16px;
        margin: 12px 0;
        display: inline-flex;
    }

    &-tags {
        display: flex;
        padding-left: 26px;
        flex-wrap: wrap;
    }
}
</style>
