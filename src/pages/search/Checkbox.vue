<template>
    <div @click="toggleChecked" class="checkbox-component">
        <div :class="{ checkbox: true, 'checkbox-checked': value }"></div>
        <slot></slot>
    </div>
</template>

<script>
export default {
    name: 'CheckboxContainer',
    props: ['value'],
    methods: {
        toggleChecked() {
            this.$emit('input', !this.value);
        },
    },
};
</script>

<style lang="scss">
.checkbox-component {
    padding: 7px;

    &:hover {
        background: #f5f5f5;
    }
}
.checkbox {
    display: inline-block;
    width: 16px;
    height: 16px;
    border-radius: 2px;
    border: 1px solid #d1d1d1;
    background: #fff;
    position: relative;
    box-sizing: border-box;

    &-checked {
        background: var(--primary-color);
        border-color: var(--primary-color);

        &::before {
            content: '';
            position: absolute;
            width: 9px;
            height: 5px;
            border-bottom: 2px solid #fff;
            border-left: 2px solid #fff;
            left: 2px;
            top: 2px;
            transform: rotate(-45deg);
        }
    }
}
</style>
