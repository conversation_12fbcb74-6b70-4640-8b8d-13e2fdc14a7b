<template>
    <div class="search-result">
        <div class="page-content" v-loading="isFetching">
            <div style="height: 102px">
                <div class="filter-area" v-show="total || !isKeywordSearch">
                    <div class="filter-area-row">
                        <dropdown-component
                            class="filter-item"
                            @show="tempDestinations = [...filter.destinations]"
                        >
                            <div
                                slot="input"
                                class="dropdown-component-input"
                                slot-scope="{ show }"
                                tabindex="-1"
                                @focus="show"
                            >
                                <div class="input-multi">
                                    <label
                                        >{{
                                            $t('search_related_destination')
                                        }}:</label
                                    >
                                    <div class="input-multi-list">
                                        <div
                                            v-for="(city,
                                            index) in filter.destinations"
                                            :key="index"
                                            class="input-multi-list-item"
                                        >
                                            {{ city.name }}
                                        </div>
                                        <div
                                            class="input-multi-list-default"
                                            v-show="
                                                (filter.destinations || [])
                                                    .length < 1
                                            "
                                        >
                                            {{
                                                $t(
                                                    'search_related_destination_holder',
                                                )
                                            }}
                                        </div>
                                    </div>
                                </div>
                                <span
                                    class="input-clear"
                                    v-show="(filter.destinations || []).length"
                                    @mousedown.prevent="
                                        filter.destinations = []
                                    "
                                    ><klk-icon
                                        size="10"
                                        type="icon_navigation_close"
                                    ></klk-icon
                                ></span>
                                <klk-icon
                                    type="icon_navigation_chevron_down"
                                    size="14"
                                ></klk-icon>
                            </div>
                            <template slot-scope="{ hide }">
                                <div class="dropdown-component-multi">
                                    <checkbox-component
                                        v-for="(city, index) in cities"
                                        :key="index"
                                        class="dropdown-component-multi-item"
                                        :value="isDestinationChecked(city)"
                                        @input="
                                            val =>
                                                setFilterDestinations(city, val)
                                        "
                                    >
                                        <label>{{ city.name }}</label>
                                    </checkbox-component>
                                </div>
                                <div class="dropdown-component-btns">
                                    <span
                                        class="cancel"
                                        @mousedown.prevent="
                                            tempDestinations = []
                                        "
                                        >{{ $t('search_reset') }}</span
                                    >
                                    <span
                                        @click="
                                            filter.destinations = [
                                                ...tempDestinations,
                                            ];
                                            hide();
                                        "
                                    >
                                        {{ $t('confirm') }}
                                    </span>
                                </div>
                            </template>
                        </dropdown-component>
                        <div class="filter-separator"></div>
                        <dropdown-component
                            class="filter-item filter-template"
                            @show="choosedCategory = [...filter.frontend_ids]"
                        >
                            <div
                                slot="input"
                                class="dropdown-component-input"
                                slot-scope="{ show }"
                                tabindex="-1"
                                @focus="show"
                            >
                                <div class="input-multi">
                                    <label
                                        >{{
                                            $t('search_related_categories')
                                        }}
                                        :</label
                                    >
                                    <div class="input-multi-list">
                                        <div
                                            v-for="(template,
                                            index) in displayFilterCategory"
                                            :key="index"
                                            class="input-multi-list-item"
                                        >
                                            {{ template }}
                                        </div>
                                        <div
                                            class="input-multi-list-default"
                                            v-show="
                                                (filter.frontend_ids || [])
                                                    .length < 1
                                            "
                                        >
                                            {{
                                                $t(
                                                    'search_related_categories_holder',
                                                )
                                            }}
                                        </div>
                                    </div>
                                </div>
                                <span
                                    class="input-clear"
                                    v-show="displayFilterCategory.length"
                                    @mousedown.prevent="
                                        filter.frontend_ids = []
                                    "
                                    ><klk-icon
                                        size="10"
                                        type="icon_navigation_close"
                                    ></klk-icon
                                ></span>
                                <klk-icon
                                    type="icon_navigation_chevron_down"
                                    size="14"
                                ></klk-icon>
                            </div>
                            <template slot-scope="{ hide }">
                                <div class="filter-template-list">
                                    <group-template-item-component
                                        :template="template"
                                        :value="choosedCategory[index] || []"
                                        @input="
                                            val => setFilterCategory(val, index)
                                        "
                                        v-for="(template,
                                        index) in frontend_tree"
                                        :key="index"
                                    >
                                    </group-template-item-component>
                                </div>
                                <div class="dropdown-component-btns">
                                    <span
                                        class="cancel"
                                        @mousedown.prevent="
                                            choosedCategory = [];
                                            filter.frontend_ids = [];
                                        "
                                        >{{ $t('search_reset') }}</span
                                    >
                                    <span
                                        @click="
                                            filter.frontend_ids = [
                                                ...choosedCategory,
                                            ];
                                            hide();
                                        "
                                    >
                                        {{ $t('confirm') }}
                                    </span>
                                </div>
                            </template>
                        </dropdown-component>
                    </div>
                    <div class="filter-area-row">
                        <dropdown-component class="filter-item filter-date">
                            <div
                                class="dropdown-component-input"
                                slot="input"
                                slot-scope="{ show }"
                                tabindex="-1"
                                @focus="show"
                            >
                                <span class="input-content">{{
                                    displayFilterDate ||
                                        $t('search_date_all_available')
                                }}</span>
                                <span
                                    class="input-clear"
                                    @mousedown.prevent="filter.date = ''"
                                    v-show="displayFilterDate"
                                    ><klk-icon
                                        size="10"
                                        type="icon_navigation_close"
                                    ></klk-icon
                                ></span>
                                <klk-icon
                                    type="icon_navigation_chevron_down"
                                    size="14"
                                ></klk-icon>
                            </div>
                            <template slot-scope="{ hide }">
                                <div class="date-shortcut">
                                    <span
                                        class="btn"
                                        @click="setFilterDate('today', hide)"
                                        >{{ $t('search_date_today') }}</span
                                    >
                                    <span
                                        class="btn"
                                        @click="setFilterDate('tomorrow', hide)"
                                        >{{ $t('search_date_tomorrow') }}</span
                                    >
                                </div>
                                <klk-date-picker
                                    :date="filter.date"
                                    :is-selectable="isDayBlocked"
                                    :max-date="maxDate"
                                    @change="day => setFilterDate(day, hide)"
                                ></klk-date-picker>
                            </template>
                        </dropdown-component>
                        <div class="filter-separator"></div>
                        <div class="filter-item confirm-type">
                            <label>
                                <klk-icon size="14" type="fast"></klk-icon>
                                {{ $t('search_instant_confirm') }}
                            </label>
                            <klk-switch v-model="filter.instant"></klk-switch>
                        </div>
                        <div class="filter-separator"></div>
                        <dropdown-component class="filter-item filter-sort">
                            <div
                                slot="input"
                                tabindex="-1"
                                slot-scope="{ show }"
                                @focus="show"
                                class="dropdown-component-input"
                            >
                                <span class="input-content"
                                    >{{ displaySortType }}
                                </span>
                                <klk-icon
                                    type="icon_navigation_chevron_down"
                                    size="14"
                                ></klk-icon>
                            </div>
                            <template slot-scope="{ hide }">
                                <div
                                    v-for="(item, index) in sort"
                                    :key="index"
                                    :class="{
                                        'filter-sort-item': true,
                                        'filter-sort-item-choosed':
                                            filter.sort === item.type,
                                    }"
                                    @click="setFilterSort(item.type, hide)"
                                >
                                    {{ item.text }}
                                </div>
                            </template>
                        </dropdown-component>
                    </div>
                </div>
            </div>
            <div class="filter-activities" v-show="total">
                <div class="filter-activities-total">
                    {{
                        $t('search_result_find', [
                            this.total,
                            this.searchKeyword,
                        ])
                    }}
                </div>
                <div class="filter-activities-list">
                    <activity-card
                        :class="{ disabled_card: activity.is_blocked }"
                        v-for="activity in activities"
                        :key="activity.id"
                        :activity="activity"
                        @clickWish="clickWish(activity.is_blocked)"
                    >
                    </activity-card>
                </div>
                <div class="filter-activities-pagination">
                    <klk-pagination
                        :total="total"
                        :page-size="24"
                        :current.sync="curPage"
                        @change="handlePageChange"
                    ></klk-pagination>
                </div>
            </div>
            <div class="no-result" v-show="isKeywordSearch && total === 0">
                <img src="@/assets/imgs/search_no_result.png" />
                <p class="no-result-message">
                    {{ $t('search_no_result', [this.searchKeyword]) }}
                </p>
                <router-link :to="{ name: 'agent_index' }">{{
                    $t('search_go_home')
                }}</router-link>
            </div>
            <div
                class="no-result no-result-filter"
                v-show="!isKeywordSearch && total === 0"
            >
                <p class="no-result-message">
                    {{ $t('search_filter_no_result') }}
                </p>
                <a href="javascript:void(0);" @click="resetFilter">{{
                    $t('search_reset_filter')
                }}</a>
            </div>
        </div>
    </div>
</template>

<script>
import urlObj from '@/common/url';
import { processActivityInfo, expansionFn } from '@/common/util';
import ActivityCard from '@/pages/tpl/ActivityCard.vue';
import {
    addDays,
    isDate,
    isToday,
    isTomorrow,
    format,
    startOfDay,
} from 'date-fns';
import throttle from 'lodash/throttle';
import DropdownComponent from './Dropdown.vue';
import CheckboxComponent from './Checkbox.vue';
import GroupTemplateItemComponent from './GroupTemplateItem.vue';

const deepFlatten = arr =>
    [].concat(...arr.map(v => (Array.isArray(v) ? deepFlatten(v) : v)));

export default {
    name: 'SearchResultPage',
    data() {
        return {
            filter: {
                instant: false,
                vertical_types: '100,102',
                sort: 'most_relevant',
                frontend_ids: [],
                destinations: [],
                date: '',
            },
            searchKeywordInput: '',
            searchKeyword: '',
            tempDestinations: [],
            choosedCategory: [],
            cities: [],
            frontend_tree: [],
            activities: [],
            availableDates: [],
            isFetching: false,
            isKeywordSearch: true,
            total: 0,
            curPage: 1,
            sort: [],
            maxDate: new Date(),
        };
    },
    watch: {
        $route: {
            handler() {
                this.searchKeyword = decodeURIComponent(
                    this.$route.query.keyword || '',
                );
                this.searchKeywordInput = this.searchKeyword;
                this.isKeywordSearch = true;
                this.fetchResult();
            },
            immediate: true,
        },
        filter: {
            handler() {
                this.isKeywordSearch = false;
                this.curPage = 1;
                this.fetchResult();
            },
            deep: true,
        },
    },
    computed: {
        displayFilterDate() {
            if (!isDate(this.filter.date)) {
                return '';
            }

            if (isToday(this.filter.date)) {
                return this.$t('search_date_today');
            }

            if (isTomorrow(this.filter.date)) {
                return this.$t('search_date_tomorrow');
            }

            return format(this.filter.date, 'YYYY-MM-DD');
        },
        displaySortType() {
            const sortText =
                (this.sort.find(item => item.type === this.filter.sort) || {})
                    .text || '';
            return `${this.$t('search_sort_by')}: ${sortText}`;
        },
        displayFilterCategory() {
            let categoryNameMaps = this.frontend_tree.reduce((acc, cur) => {
                acc[cur.id] = cur.name;
                if (cur.children && cur.children.length) {
                    cur.children.forEach(child => (acc[child.id] = child.name));
                }
                return acc;
            }, {});
            return deepFlatten(this.filter.frontend_ids).map(
                tid => categoryNameMaps[tid],
            );
        },
        destinationIds() {
            return (this.tempDestinations || []).map(item => item.id);
        },
        sortMap() {
            return {
                most_relevant: 'Best Match',
                participants: 'Most Popular',
                publish_time: 'Publish Time',
            };
        },
    },
    components: {
        ActivityCard,
        DropdownComponent,
        CheckboxComponent,
        GroupTemplateItemComponent,
    },
    mounted() {
        this.throttleScrollHandler = throttle(this.handleScroll, 100);
        window.addEventListener('scroll', this.throttleScrollHandler);
    },
    beforeDestroy() {
        window.removeEventListener('scroll', this.throttleScrollHandler);
    },
    methods: {
        clickWish(is_blocked) {
            if (is_blocked) {
                return;
            }
        },
        setFilterDate(day, hide) {
            switch (day) {
                case 'today':
                    this.filter.date = new Date();
                    break;
                case 'tomorrow':
                    this.filter.date = addDays(new Date(), 1);
                    break;
                default:
                    if (isDate(day)) {
                        this.filter.date = day;
                    }
            }
            typeof hide === 'function' && hide();
        },
        setFilterSort(type, hide) {
            this.filter.sort = type;
            typeof hide === 'function' && hide();
        },
        setFilterDestinations(city, value) {
            switch (value) {
                case false:
                    this.tempDestinations.splice(
                        this.tempDestinations.indexOf(city),
                        1,
                    );
                    break;
                case true:
                    this.tempDestinations.push(city);
                    break;
            }
        },
        setFilterCategory(val, index) {
            if (this.choosedCategory.length < index + 1) {
                this.choosedCategory.length = index + 1;
            }
            this.choosedCategory.splice(index, 1, val);
        },
        isDestinationChecked(city) {
            return this.destinationIds.includes(city.id);
        },
        getAvailableDates(time) {
            const { from, days } = time;
            return [...Array(+days).keys()].reduce((acc, val) => {
                acc.push(format(addDays(new Date(from), val), 'YYYY-MM-DD'));
                return acc;
            }, []);
        },
        fetchResult() {
            if (!this.searchKeyword) {
                return;
            }
            const { vertical_types, frontend_ids, instant, date } = this.filter;

            const searchParams = {
                query: this.searchKeyword,
                city_ids: (this.filter.destinations || [])
                    .map(item => item.id)
                    .join(','),
                vertical_types,
                instant,
                page_num: this.curPage,
                page_size: 24,
                sort: this.filter.sort,
                frontend_ids: frontend_ids.flat().join(','),
                start_time: date
                    ? `${format(startOfDay(date), 'YYYY-MM-DDTHH:mm:ss')}Z`
                    : '',
            };
            Object.keys(searchParams).forEach(key => {
                if (key !== 'query' && !searchParams[key]) {
                    delete searchParams[key];
                }
            });
            this.isFetching = true;
            klook.ajaxGet(urlObj.search.search_common, searchParams, res => {
                this.isFetching = false;
                if (res.success) {
                    const { activity_info, total } =
                        res.result.search_result || {};
                    // sort, price
                    const {
                        frontend_tree,
                        time,
                        sort,
                        destinations,
                    } = res.result.aggr_condition;
                    this.sort = sort;
                    this.cities = expansionFn(destinations);
                    this.frontend_tree = frontend_tree || [];
                    this.total = total || 0;
                    this.availableDates = this.getAvailableDates(time);
                    this.maxDate = new Date(
                        this.availableDates[this.availableDates.length - 1],
                    );
                    this.activities = processActivityInfo.call(
                        this,
                        activity_info,
                    );
                } else {
                    this.activities = [];
                    this.total = 0;
                }
            });
        },
        isDayBlocked(day) {
            return this.availableDates.includes(format(day, 'YYYY-MM-DD'));
        },
        handlePageChange(val) {
            this.curPage = val;
            this.fetchResult();
        },
        resetFilter() {
            this.filter = {
                instant: false,
                vertical_types: '100,102',
                sort: 'most_relevant',
                frontend_ids: [],
                destinations: [],
                date: '',
            };
        },
        handleScroll() {
            const scrollTop =
                document.documentElement.scrollTop || document.body.scrollTop;
            const targetEl = this.$el.querySelector('.filter-area');
            if (scrollTop < 46 || !targetEl) {
                targetEl.classList.remove(
                    'filter-area-fixed',
                    'filter-area-fixed-down',
                );
                return;
            }

            targetEl.classList.add('filter-area-fixed');
            if (this.lastScroll < scrollTop) {
                targetEl.classList.add('filter-area-fixed-down');
            } else {
                targetEl.classList.remove('filter-area-fixed-down');
            }

            this.lastScroll = scrollTop;
        },
    },
};
</script>

<style lang="scss">
.search-result {
    padding: 40px 0;
    background: #fff;
    outline: 9999px solid #fff;

    .page-content {
        width: 1160px;
        margin: 0 auto;
    }

    .circle-clear {
        background: #e0e0e0;
        width: 24px;
        height: 24px;
        border-radius: 50%;
        position: relative;
        transform: rotate(45deg);
        cursor: pointer;
        margin: 0 20px;
        display: inline-block;

        &::before,
        &:after {
            content: '';
            width: 16px;
            height: 2px;
            position: absolute;
            left: 4px;
            background: #999;
            top: 11px;
        }

        &::after {
            transform: rotate(90deg);
        }
    }

    .search-input {
        text-align: center;
        line-height: 56px;
        height: 56px;
        margin-bottom: 40px;

        input {
            border: 1px solid #e0e0e0;
            border-right: 0;
            border-radius: 2px 0 0 2px;
            white-space: nowrap;
            width: 713px;
            height: 100%;
            vertical-align: top;
            box-sizing: border-box;
            padding: 0 20px;
            font-size: 16px;
        }

        .btn-search {
            display: inline-block;
            width: 148px;
            background: var(--primary-color);
            height: 100%;
            border-radius: 0 2px 2px 0;
        }
    }

    .confirm-type {
        display: flex;
        align-items: center;
        justify-content: space-between;

        .klk-icon-fast {
            color: var(--primary-color);
        }
    }

    .filter-item {
        padding: 0 20px;
    }

    .filter-area {
        background: #fff;
        border: 1px solid #e0e0e0;

        &-row {
            display: flex;
            border-bottom: 1px solid #e0e0e0;
            height: 50px;
            width: 100%;
            margin: 0 auto;

            &:last-child {
                border: none;
            }
        }

        .filter-city,
        .filter-template,
        .filter-item {
            flex: 1 1;

            .tag-input {
                border: none;
            }
        }
        .filter-separator {
            width: 1px;
            background: #e0e0e0;
        }

        .filter-template .tag-list {
            right: -1px;
            left: initial;
        }

        &-fixed {
            border: none;
            width: 100%;
            top: 60px;
            left: 0;
            box-shadow: 0 2px 6px 0 rgba(0, 0, 0, 0.12);
            z-index: 99;
            position: fixed;
            transition: transform 0.3s ease-in-out;
        }

        &-fixed-down {
            transform: translateY(-50px);
        }
    }

    span.btn {
        line-height: 32px;
        border-radius: 2px;
        border: 1px solid #b2b2b2;
        display: inline-block;
        height: 32px;
        text-align: center;
        width: 83px;
        cursor: pointer;
        margin-right: 7px;

        &:hover {
            background: var(--primary-color);
            color: #fff;
            border-color: var(--primary-color);
        }
    }

    .dropdown-component-input {
        display: flex;
        height: 100%;
        align-items: center;

        &:focus {
            outline: none;
        }

        .input-content {
            flex: 1 1;
            min-width: 0;
            text-overflow: ellipsis;
            overflow: hidden;
            white-space: nowrap;
        }

        .klk-icon-down {
            color: #333;
            transition: transform ease-in-out 0.3s;
            margin-left: 20px;
        }

        .input-clear {
            width: 24px;
            height: 24px;
            border-radius: 100%;
            background: #f5f5f5;
            text-align: center;
            line-height: 24px;
            margin-left: 20px;
        }

        .klk-icon-close {
            cursor: pointer;
            color: #999;
            font-size: 10px;
        }

        .input-multi {
            flex: 1 1;
            display: flex;
            justify-content: space-between;
            line-height: 1.2;

            &-list {
                flex: 1 1;
                max-width: 250px;
                display: flex;
                margin-left: 60px;
                min-width: 0;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                text-align: right;

                &-item {
                    margin-right: 20px;
                    color: #333;
                    font-weight: 500;

                    &:last-child {
                        margin-right: 0;
                    }
                }

                &-default {
                    flex: 1;
                    text-align: right;
                    color: #b2b2b2;
                }
            }
        }
    }

    .dropdown-component-show .dropdown-component-input .klk-icon-down {
        transform: rotate(180deg);
    }

    .dropdown-component-multi {
        display: flex;
        padding: 0 20px;
        flex-wrap: wrap;

        &-item {
            margin: 18px 12px 0 0;
            display: flex;
            align-items: center;

            label {
                display: inline-block;
                margin-left: 8px;
            }
        }
    }

    .filter-template-list,
    .dropdown-component-multi {
        max-height: 350px;
        overflow-y: scroll;
    }

    .dropdown-component-btns {
        padding: 24px 24px 20px;
        text-align: right;

        span {
            display: inline-block;
            background: var(--primary-color);
            color: #fff;
            padding: 8px 16px;
            border-radius: 2px;
            margin-right: 32px;
            cursor: pointer;

            &:last-child {
                margin-right: 0;
            }
        }

        span.cancel {
            background: #fff;
            color: #666;

            &:hover {
                color: var(--primary-color);
            }
        }
    }

    .filter-template {
        .dropdown-component-content {
            right: -1px;
            left: inherit;
        }

        .group-template-item {
            margin: 0 24px;
        }
    }

    .filter-date {
        .date-shortcut {
            padding: 20px;
            border-bottom: 1px solid #e0e0e0;
        }
    }

    .filter-sort-item {
        height: 40px;
        line-height: 40px;
        padding-left: 14px;
        width: 100%;

        &:hover {
            background: #f5f5f5;
            cursor: pointer;
        }
    }

    .filter-sort-item-choosed {
        background: var(--primary-color);
        color: #fff;

        &:hover {
            background: var(--primary-color);
        }
    }

    .filter-activities {
        &-total {
            margin: 24px 0 16px;
            color: #666;
        }

        &-list {
            display: flex;
            flex-wrap: wrap;
        }

        .activity-card {
            width: 372px;
            margin: 0 22px 20px 0;

            &:nth-child(3n) {
                margin-right: 0;
            }
        }
        &-pagination {
            margin: 10px 0 30px;
            overflow: hidden;
            display: flex;
            justify-content: center;
        }
    }

    .no-result {
        margin-top: 80px;
        text-align: center;

        img {
            width: 180px;
            height: 124px;
        }

        &-message {
            font-size: 20px;
            color: #333;
            margin: 20px 0;
        }

        a {
            font-size: 16px;
            font-weight: 500;
            color: #fff;
            display: inline-block;
            padding: 11px 30px;
            border-radius: 2px;
            background: var(--primary-color);
        }

        &-filter {
            margin-top: 60px;
        }
    }
    .disabled_card {
        .card-content {
            color: grey;
            cursor: not-allowed;
            .card-content-title,
            .sell-price,
            .klk-icon-fast {
                color: grey;
            }
        }
        .image-wrapper {
            cursor: not-allowed;
        }
    }
}
</style>
