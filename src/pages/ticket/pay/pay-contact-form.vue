<template>
    <klk-form
        inline
        ref="form"
        :model="contactForm"
        :rules="contactRules"
        class="ticket-pay-contact-form form-content"
    >
        <klk-form-item :label="$t('title')" prop="title" class="is-required">
            <klk-select v-model="contactForm.title">
                <klk-option
                    v-for="item in ['MR', 'MRS']"
                    :key="item"
                    :value="item"
                    :label="item"
                >
                </klk-option>
            </klk-select>
        </klk-form-item>
        <klk-form-item
            :label="$t('first_name')"
            prop="firstName"
            class="is-required"
        >
            <klk-input
                v-model="contactForm.firstName"
                placeholder="As shown on passport"
            >
            </klk-input>
        </klk-form-item>
        <klk-form-item
            :label="$t('family_name')"
            prop="familyName"
            class="is-required"
        >
            <klk-input
                v-model="contactForm.familyName"
                placeholder="As shown on passport"
            >
            </klk-input>
        </klk-form-item>

        <klk-form-item
            :label="$t('country_code')"
            prop="countryCode"
            class="is-required"
        >
            <klk-select :filterable="true" v-model="contactForm.countryCode">
                <klk-option
                    v-for="item in countries"
                    :key="item.country_code"
                    :value="item.country_code"
                    :label="item.country_name"
                >
                </klk-option>
            </klk-select>
        </klk-form-item>

        <klk-form-item
            :label="$t('mobile_number')"
            prop="mobile"
            class="is-required"
        >
            <klk-input
                v-model="contactForm.mobile"
                :placeholder="$t('please_enter_search')"
            >
            </klk-input>
        </klk-form-item>

        <klk-form-item
            prop="email"
            :class="{ 'is-required': contactForm.voucherToTraveler }"
        >
            <template slot="label">
                <klk-checkbox v-model="contactForm.voucherToTraveler">
                    {{ $t('email_address') }}
                </klk-checkbox>
            </template>
            <klk-input
                v-model="contactForm.email"
                :placeholder="$t('please_enter_search')"
                :disabled="!contactForm.voucherToTraveler"
            >
            </klk-input>
        </klk-form-item>

        <klk-form-item
            class="trade-number-item"
            :label="$t('agent_booking_ref_no')"
            prop="tradeOrderNumber"
        >
            <klk-input
                v-model="contactForm.tradeOrderNumber"
                :placeholder="$t('please_enter_search')"
            >
            </klk-input>
        </klk-form-item>
        <klk-form-item
            v-if="priceInfo.unit_order_supported"
            class="is-unit-order"
            label=" "
            prop="isUnitOrder"
        >
            <klk-checkbox
                v-model="contactForm.isUnitOrder"
                :disabled="skuCount < 2"
            >
                {{ $t('split_by_product_quantity') }} <br />
            </klk-checkbox>
            <div class="open" @click="showUnitOrderTip = true">
                {{ $t('read_before_check') }}
            </div>
        </klk-form-item>

        <klk-modal
            :open.sync="showUnitOrderTip"
            :show-cancel-button="false"
            @on-confirm="showUnitOrderTip = false"
        >
            <strong>
                {{ $t('check_option_explanation') }}
            </strong>
            <br />
            <br />
            {{ $t('example_explanation') }}
            <br />
            <br />
            {{ $t('no_check_option_explanation') }}
            <br />
            <br />
            {{ $t('feature_usage') }}
            <br />
            <br />
            {{ $t('max_min_quantity') }}
        </klk-modal>
    </klk-form>
</template>

<script>
export default {
    props: {
        priceInfo: {
            type: Object,
            required: true,
            default: () => {},
        },
        countries: {
            default: () => [],
            required: true,
        },
        contactForm: {
            default: () => {},
            required: true,
        },
        selectedSkus: {
            type: Array,
            required: true,
            default: () => [],
        },
    },
    data() {
        const validateName = (rule, value, callback) => {
            if (!value) {
                return callback(new Error(this.$t('pay_required_text_field')));
            }
            if (!/^[\u4e00-\u9fa5_a-zA-Z\s]+$/.test(value)) {
                callback(new Error(this.$t('global_validate_letter')));
            } else {
                callback();
            }
        };
        const validateMobileNumber = (rule, value, callback) => {
            if (!value) {
                return callback(new Error(this.$t('pay_required_text_field')));
            }
            if (!/^\d{6,17}$/.test(value)) {
                callback(new Error(this.$t('order_phone_number_validate')));
            } else {
                callback();
            }
        };
        const validateEmail = (rule, value, callback) => {
            if (!this.contactForm.voucherToTraveler) {
                callback();
            } else {
                if (!value) {
                    return callback(
                        new Error(this.$t('pay_required_text_field')),
                    );
                }
                if (!klook.checkEmail(value)) {
                    callback(
                        new Error(
                            this.$t('global_validate_plzEnterValidEmail'),
                        ),
                    );
                } else {
                    callback();
                }
            }
        };
        return {
            showUnitOrderTip: false,
            contactRules: {
                title: [
                    {
                        required: true,
                        message: this.$t('pay_required_select_field'),
                        trigger: 'change',
                    },
                ],
                firstName: [{ validator: validateName, trigger: 'blur' }],
                familyName: [{ validator: validateName, trigger: 'blur' }],
                countryCode: [
                    {
                        required: true,
                        message: this.$t('pay_required_select_field'),
                        trigger: 'change',
                    },
                ],
                mobile: [{ validator: validateMobileNumber, trigger: 'blur' }],
                voucherLanguage: [
                    {
                        required: true,
                        message: this.$t('pay_required_select_field'),
                        trigger: 'change',
                    },
                ],
                email: [{ validator: validateEmail, trigger: 'blur' }],
            },
        };
    },
    computed: {
        skuCount() {
            return this.selectedSkus.reduce((sum, item) => sum + item.count, 0);
        },
        voucherLanguages() {
            return [
                { name: 'en_US', label: 'English' },
                { name: 'zh_CN', label: '简体中文' },
                { name: 'zh_TW', label: '繁體中文' },
            ];
        },
    },
    methods: {
        checkValid() {
            let flag = false;
            this.$refs.form.validate(valid => {
                flag = valid;
            });
            return flag;
        },
    },
};
</script>

<style lang="scss">
.ticket-pay-contact-form {
    .trade-number-item {
        .klk-form-item-label {
            line-height: 29px;
        }
    }
    .is-unit-order {
        width: 310px;
        .open {
            cursor: pointer;
            text-decoration: underline;
            color: var(--primary-color);
        }
    }
}
</style>
