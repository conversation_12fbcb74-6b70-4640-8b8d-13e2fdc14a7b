<template>
    <div class="ticket-pay-index" v-loading="loading || loadingSettlement">
        <div class="pay-wrapper">
            <div class="pay-content-left">
                <pay-package
                    ref="payPackage"
                    :price-info="priceInfo"
                    :selected-skus="selectedSkus"
                    :isAid="isNewActivityVersion"
                    :booking-tags="bookingTags"
                ></pay-package>
                <div>
                    <div
                        class="traveler-info"
                        v-if="
                            hasExtraInfo ||
                                hasBookingTravelerInfo ||
                                hasUnitTravelerInfo
                        "
                    >
                        <p class="title">
                            {{ $t('travaler_information') }}
                        </p>
                        <pay-traveler-info
                            id="travelerSection"
                            ref="travelerInfoForm"
                            :traveler-info-form="travelerInfoForm"
                        ></pay-traveler-info>
                    </div>
                    <div class="contact-info">
                        <p class="title">
                            {{ $t('concact_information') }}
                        </p>
                        <pay-contact-form
                            id="contactSection"
                            ref="contactInfoForm"
                            :price-info="priceInfo"
                            :contact-form="contactForm"
                            :countries="countries"
                            :selected-skus="selectedSkus"
                        >
                        </pay-contact-form>
                    </div>
                    <div class="pay-now-wrapper">
                        <klk-button @click="payOrder">
                            {{ $t('pay_btn') }}
                        </klk-button>
                    </div>
                </div>
            </div>
            <div class="pay-content-right">
                <pay-order-detail
                    :selected-skus="selectedSkus"
                    :price-info="priceInfo"
                ></pay-order-detail>
            </div>
        </div>
    </div>
</template>

<script>
import { cloneDeep } from 'lodash';
import { mapState, mapGetters, mapActions } from 'vuex';
import urlObj from '@/common/url';
import { openOrderCheckout } from '@/common/standaloneCheckout';
import { isAfter, isSameDay } from 'date-fns';
import PayPackage from './pay-package.vue';
import PayOrderDetail from './pay-order-detail.vue';
import PayContactForm from './pay-contact-form.vue';
import PayTravelerInfo from './traveler-info/index.vue';
import { elmScrollTop } from '../activity/activity';
import { microsecondTimestampToStr } from './pay';

export default {
    name: 'TicketPayIndex',
    components: {
        PayPackage,
        PayOrderDetail,
        PayContactForm,
        PayTravelerInfo,
    },
    data() {
        return {
            loading: false,
            priceInfo: {},
            countries: [],
            selectedSkus: [],
            bookingTags: [],
            firstTimeLoadData: false,
            loadingSettlement: false,
            travelerInfoForm: {},
            contactForm: {
                // 联系人信息
                title: '',
                firstName: '',
                familyName: '',
                countryCode: '',
                mobile: '',
                voucherToTraveler: false,
                email: '',
                tradeOrderNumber: '',
            },
            oldTotalPrice: '',
            defaultDate: '', // 进入页面默认日期
            isNewActivityVersion: null, // 用于标识活动版本
        };
    },
    computed: {
        ...mapState('ticket/pay', [
            'packageId',
            'selectedQuality',
            'skuPrices',
            'selectedDate',
            'selectedTime',
            'selectedQuality',
        ]),
        ...mapGetters('ticket/pay', ['selectedArrangementId']),
        hasExtraInfo() {
            return (
                this.travelerInfoForm.extraInfo &&
                this.travelerInfoForm.extraInfo.length > 0
            );
        },
        hasBookingTravelerInfo() {
            return (
                this.travelerInfoForm.travelerInfo &&
                this.travelerInfoForm.travelerInfo.booking_traveler_info
            );
        },
        hasUnitTravelerInfo() {
            return (
                this.travelerInfoForm.travelerInfo &&
                this.travelerInfoForm.travelerInfo.unit_traveler_info
            );
        },
    },
    watch: {
        selectedDate(newVal, oldVal) {
            if (!newVal) return;
            if (!oldVal) {
                this.defaultDate = newVal;
            }
        },
        selectedQuality: {
            handler: function(newVal) {
                if (newVal && newVal.length === 0) return;
                const skuInputs = document.getElementsByName('skuQualityInput');
                if (skuInputs && skuInputs.length) {
                    skuInputs.forEach(item => item.blur());
                }
                this.fetchSettlementInfo();
            },
            immediate: true,
        },
    },
    methods: {
        ...mapActions({
            checkActivityVersion: 'ticket/activity/checkActivityVersion',
        }),
        formatDate(date) {
            return date ? date.substring(0, 19).replace('T', ' ') : '';
        },
        getGenerateOrderData() {
            const paySelectSession =
                (this.$refs['payPackage'] &&
                    this.$refs['payPackage'].getPaySelectSession()) ||
                {};
            let mobileCountry = (this.countries || []).find(
                c => c.country_code === this.contactForm.countryCode,
            );
            if (mobileCountry) {
                mobileCountry = /[(（)]?\+(\d+)[)）]?/.exec(
                    mobileCountry.country_name,
                );
                mobileCountry = mobileCountry ? mobileCountry[1] : '';
            }

            return {
                vertical_param: {},
                payment_info: {
                    user_total_pay: this.priceInfo.actual_amount,
                },
                traveller_info: {
                    // 出行人信息
                    title: this.contactForm.title,
                    first_name: this.contactForm.firstName,
                    family_name: this.contactForm.familyName,
                    countryCode: this.contactForm.countryCode,
                    mobile: `${mobileCountry}-${this.contactForm.mobile}`,
                    voucher_to_traveler: this.contactForm.voucherToTraveler,
                    email: this.contactForm.voucherToTraveler
                        ? this.contactForm.email
                        : '',
                    trade_order_number: this.contactForm.tradeOrderNumber,
                },
                item_info_list: [
                    // 下单信息
                    {
                        activity_id: this.priceInfo.activity_id,
                        is_unit_order: !!this.contactForm.isUnitOrder,
                        supplier_timeslots_session_id:
                            paySelectSession.supplier_timeslots_session_id ||
                            '',
                        activity_template_id: this.priceInfo
                            .activity_template_id,
                        package_id: this.priceInfo.package_id,
                        // schedule_id: this.priceInfo.schedule_id,
                        start_time: this.formatDate(
                            this.priceInfo.schedule_time,
                        ),
                        item_list: this.selectedSkus.map(item => {
                            return {
                                sku_id: item.sku_id,
                                count: item.count,
                            };
                        }),
                        other_info: {
                            extra_info: this.hasExtraInfo
                                ? this.travelerInfoForm.extraInfo
                                : null,
                            traveler_other_info: {
                                booking_traveler_info: this
                                    .hasBookingTravelerInfo
                                    ? this.travelerInfoForm.travelerInfo
                                          .booking_traveler_info
                                    : null,
                                unit_traveler_info: this.hasUnitTravelerInfo
                                    ? this.travelerInfoForm.travelerInfo
                                          .unit_traveler_info
                                    : null,
                            },
                        },
                    },
                ],
            };
        },
        cloneOldUnitTravelerInfo(oldUnitTravelerInfoArr, unitTravelerInfoArr) {
            oldUnitTravelerInfoArr.forEach(oldUnitTravelerInfo => {
                const index = (unitTravelerInfoArr || []).findIndex(
                    info => info.sku_name === oldUnitTravelerInfo.sku_name,
                );
                if (index !== -1) {
                    const oldItem = cloneDeep(
                        oldUnitTravelerInfo.other_info_item || [],
                    );
                    const newItem = cloneDeep(
                        unitTravelerInfoArr[index].other_info_item || [],
                    );
                    if (oldItem && oldItem.length > 0) {
                        oldItem.forEach(item => {
                            const itemIndex = newItem.findIndex(
                                info => info.id === item.id,
                            );
                            if (itemIndex !== -1) {
                                newItem[itemIndex] = cloneDeep(item);
                            }
                        });
                    }
                    unitTravelerInfoArr[index].other_info_item = cloneDeep(
                        newItem,
                    );
                }
            });
        },
        fetchSettlementInfo(onlyUpdatePrice) {
            if (!this.selectedArrangementId) return false;
            this.selectedSkus = [];
            this.selectedQuality.forEach((quality, index) => {
                if (quality > 0) {
                    this.selectedSkus.push({
                        sku_id: this.skuPrices[index].sku_id,
                        sku_name: this.skuPrices[index].name,
                        count: quality,
                    });
                }
            });
            if (this.selectedSkus.length === 0) return;
            this.loadingSettlement = true;
            let param = {
                arrangement_id: this.selectedArrangementId,
                start_time: microsecondTimestampToStr(
                    this.selectedDate,
                    this.selectedTime,
                ),
                price_items: this.selectedSkus,
            };
            return new Promise(resolve => {
                klook.ajaxPostJSON(
                    urlObj.ticket.pay.booking_settlement,
                    param,
                    res => {
                        this.loadingSettlement = false;
                        if (!res.success) {
                            this.$alert(res.error && res.error.message).then(
                                () => {
                                    window.history.back();
                                },
                            );
                        } else {
                            const result = res.result || {};
                            resolve(result);
                            // 门票活动一次只能购买一个套餐
                            const buyInfo =
                                result.ref_items[0].commodity_info || {};
                            this.priceInfo = Object.assign(
                                buyInfo,
                                result.price_info,
                            );
                            if (onlyUpdatePrice) return;
                            const structuredOtherInfo =
                                result.ref_items[0].structured_other_info || {};
                            this.bookingTags =
                                result.ref_items[0].package_tags || [];
                            const travelerOtherInfo =
                                structuredOtherInfo.traveler_other_info || {};
                            // 需要保留上一次填写的other info
                            if (this.hasBookingTravelerInfo) {
                                travelerOtherInfo.booking_traveler_info = cloneDeep(
                                    this.travelerInfoForm.travelerInfo
                                        .booking_traveler_info,
                                );
                            }
                            if (this.hasUnitTravelerInfo) {
                                const oldUnitTravelerInfoArr = cloneDeep(
                                    this.travelerInfoForm.travelerInfo
                                        .unit_traveler_info,
                                );
                                const unitTravelerInfoArr =
                                    travelerOtherInfo.unit_traveler_info;
                                if (
                                    unitTravelerInfoArr &&
                                    unitTravelerInfoArr.length > 0
                                ) {
                                    // 保留用户填写的other info
                                    this.cloneOldUnitTravelerInfo(
                                        oldUnitTravelerInfoArr,
                                        unitTravelerInfoArr,
                                    );
                                }
                            }
                            this.$set(
                                this.travelerInfoForm,
                                'travelerInfo',
                                travelerOtherInfo,
                            );

                            if (!this.hasExtraInfo) {
                                this.$set(
                                    this.travelerInfoForm,
                                    'extraInfo',
                                    structuredOtherInfo.extra_info || [],
                                );
                            }
                            if (!this.firstTimeLoadData) {
                                this.firstTimeLoadData = true;
                                this.checkActivityVersion(
                                    this.priceInfo.activity_id,
                                ).then(isNew => {
                                    this.isNewActivityVersion = isNew;
                                });
                            }
                        }
                    },
                );
            });
        },
        fetchCountries() {
            // 联系人这里要包含所有country
            klook.ajaxGet(
                `${urlObj.agent_account_countries}?all=1`,
                null,
                res => {
                    if (res.success && res.result) {
                        this.countries = (res.result.main || [])
                            .concat(res.result.others || [])
                            .sort((a, b) =>
                                String(a.country_name).localeCompare(
                                    b.country_name,
                                ),
                            )
                            .filter(item => item.country_code !== 'RU');
                    }
                },
            );
        },
        payOrder() {
            let completeType = true;
            if (!this.$refs['payPackage'].checkValid()) {
                completeType = false;
                elmScrollTop('contactSection', 60);
            }
            if (
                this.$refs['travelerInfoForm'] &&
                !this.$refs['travelerInfoForm'].checkValid()
            ) {
                completeType = false;
                elmScrollTop('travelerSection', 60);
            }
            if (!this.$refs['contactInfoForm'].checkValid()) {
                completeType = false;
                elmScrollTop('contactSection', 60);
            }

            if (!completeType) return;

            const generateData = this.getGenerateOrderData();
            this.loading = true;
            klook.ajaxPostJSON(
                urlObj.ticket.pay.booking_order,
                generateData,
                res => {
                    this.loading = false;
                    if (res.success && res.result) {
                        const result = res.result || {};
                        let order_no = result.order_no;
                        if (
                            result.payment_status === 'finished' ||
                            +result.payment_status === 4
                        ) {
                            window.location = `${
                                window.KLK_LANG_PATH
                            }/pay/result?orderid=${encodeURIComponent(
                                order_no,
                            )}`;
                            return;
                        }
                        openOrderCheckout(order_no);
                    } else {
                        // 存在促销过期的活动，需要重新计算价格
                        if (res.error.code.toString() === '01009002001') {
                            const oldTotalPrice = this.priceInfo.actual_amount;
                            this.fetchSettlementInfo(true).then(() => {
                                const formatPrice = price => {
                                    return `${this.getCurrencySymbolByCurrencyCode(
                                        this.priceInfo.currency,
                                    )} ${price}`;
                                };
                                this.$confirm(
                                    this.$t('promotion_expired_tip', [
                                        formatPrice(oldTotalPrice),
                                        formatPrice(
                                            this.priceInfo.actual_amount,
                                        ),
                                    ]),
                                    this.$t('promotion_expired_tip', {
                                        'ok-label': this.$t(
                                            'promotion_expired_tip_continue',
                                        ),
                                        'cancel-label': this.$t(
                                            'promotion_expired_tip_back',
                                        ),
                                    }),
                                ).then(({ result }) => {
                                    if (result) {
                                        this.payOrder();
                                    }
                                });
                            });
                        }
                        // 提示生成订单失败
                        this.$message({
                            type: 'error',
                            message:
                                (res.error && res.error.message) ||
                                'Failed to generate order!',
                        });
                    }
                },
            );
        },
    },
    created() {
        if (window.KLK_MARKET === 'cn') {
            this.contactForm.title = 'MR';
            this.contactForm.countryCode = 'CN';
        }
        const { package_id, sku_id } = this.$route.params;
        const date = this.$route.query.date;
        if (!package_id || !sku_id) {
            this.$router.push({ name: '404' });
        }
        this.$store.commit('ticket/pay/UPDATE_PACKAGE_ID', package_id);
        this.$store.commit('ticket/pay/UPDATE_SKU_ID', sku_id);
        if (
            date &&
            (isSameDay(date, new Date()) || isAfter(date, new Date()))
        ) {
            this.$store.commit('ticket/pay/UPDATE_SELECTED_DATE', date);
        }
        this.fetchCountries();
    },
    mounted() {
        this.$store.commit('UPDATE_SHOPPING_CART_STATUS', false);
    },
    beforeDestroy() {
        this.$store.commit('UPDATE_SHOPPING_CART_STATUS', true);
    },
    destroyed() {
        this.$store.commit('ticket/pay/UPDATE_SELECTED_DATE', '');
        this.$store.commit('ticket/pay/UPDATE_SKU_PRICES', []);
        this.$store.commit('ticket/pay/UPDATE_SELECTED_QUALITY', []);
    },
};
</script>

<style lang="scss">
.ticket-pay-index {
    background: #f5f5f5;
    padding: 20px 0 30px;

    .pay-wrapper {
        display: flex;
        justify-content: space-between;
        margin: 0 auto;
        width: 1150px;

        .pay-content-left {
            width: 800px;
            flex: 0 0 800px;

            .contact-info,
            .traveler-info {
                margin-top: 20px;
                border-radius: 2px;

                .title {
                    padding-left: 16px;
                    font-weight: 600;
                    font-size: 18px;
                    color: #ffffff;
                    line-height: 48px;
                    background: #4c87e6;
                }

                .form-content {
                    padding: 32px 4px 24px 32px;
                    background: #ffffff;

                    .klk-form-item {
                        .klk-input,
                        .klk-select {
                            width: 335px;
                        }
                    }
                }
            }

            .pay-now-wrapper {
                background: #ffffff;
                margin-top: 20px;
                padding: 24px 32px;
                text-align: right;

                .tips {
                    width: 460px;
                    font-size: 14px;
                }
            }
        }

        .pay-content-right {
            position: fixed;
            top: 80px;
            right: calc((100% - 1150px) / 2);
            background: #f5f5f5;
            width: 330px;
        }
    }
}
</style>
