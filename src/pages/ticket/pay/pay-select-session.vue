<template>
    <div
        v-if="supplierTimeList && supplierTimeList.length"
        class="pay-select-session"
    >
        <klk-form
            inline
            ref="form"
            :model="selectSessionForm"
            :rules="selectSessionRules"
        >
            <klk-form-item
                :label="$t('pay.select.session')"
                prop="supplier_timeslots_session_id"
                class="is-required supplier_timeslots_session_id"
            >
                <div class="select" @click="onChange">
                    <klk-select
                        v-model="
                            selectSessionForm.supplier_timeslots_session_id
                        "
                        :placeholder="$t('activity.tips.please_select_unit')"
                    >
                        <klk-option value="1" label="1">{{
                            $t('activity.tips.please_select_unit')
                        }}</klk-option>
                        <klk-option
                            v-for="item in supplierTimeList"
                            :key="item.session_id"
                            :value="item.session_id"
                            :label="item.origin_session_label"
                        >
                            {{ item.origin_session_label }}
                        </klk-option>
                    </klk-select>
                </div>
            </klk-form-item>
        </klk-form>
        <klk-drawer :visible.sync="showSelectSession" direction="right">
            <div class="drawer">
                <div class="close">
                    <klk-icon
                        class="icon"
                        size="20"
                        type="icon_navigation_close"
                        @click="showSelectSession = false"
                    />
                </div>
                <klk-radio-group
                    v-if="supplierTimeList && supplierTimeList.length"
                    class="time-group"
                    v-model="selectSessionForm.supplier_timeslots_session_id"
                >
                    <klk-radio
                        v-for="item in supplierTimeList"
                        :key="item.session_id"
                        :group-value="item.session_id"
                    >
                        <div
                            v-for="game in item.games"
                            :key="game.time_range_label"
                            class="label"
                        >
                            <span>{{ game.name }}</span>
                            <p>{{ game.time_range_label }}</p>
                        </div>
                    </klk-radio>
                </klk-radio-group>
            </div>
        </klk-drawer>
    </div>
</template>

<script>
import urlObj from '@/common/url';

export default {
    components: {},
    props: {
        priceInfo: {
            type: Object,
            required: true,
            default: () => {},
        },
        selectedSkus: {
            type: Array,
            required: true,
            default: () => {},
        },
    },
    watch: {
        priceInfo(newVal) {
            const { package_id, activity_id } = this.priceInfo;
            if (
                package_id &&
                activity_id &&
                newVal.schedule_time &&
                this.selectedSkus
            ) {
                this.getSupplierTimeslots(newVal.schedule_time);
            }
        },
    },
    data() {
        return {
            selectSessionForm: {
                supplier_timeslots_session_id: '',
            },
            selectSessionRules: {
                supplier_timeslots_session_id: [
                    {
                        required: true,
                        message: this.$t('pay_required_select_field'),
                        trigger: 'change',
                    },
                ],
            },
            showSelectSession: false,
            supplierTimeList: [],
        };
    },
    mounted() {
        this.getSupplierTimeslots(this.priceInfo.schedule_time);
    },
    methods: {
        checkValid() {
            let flag = false;
            this.$refs.form &&
                this.$refs.form.validate(valid => {
                    flag = valid;
                });
            return this.$refs.form ? flag : true;
        },
        getData() {
            return this.selectSessionForm;
        },
        onChange() {
            this.showSelectSession = true;
        },
        getSupplierTimeslots(start_time) {
            const { package_id, activity_id } = this.priceInfo;
            klook.ajaxPostJSON(
                urlObj.ticket.pay.supplier_timeslots,
                {
                    package_id,
                    activity_id,
                    start_time,
                    skus: this.selectedSkus.map(item => {
                        return {
                            sku_id: item.sku_id,
                            count: item.count,
                        };
                    }),
                },
                ({ result, success }) => {
                    if (success && result.items) {
                        this.supplierTimeList = result.items || [];
                    }
                },
            );
        },
    },
};
</script>

<style lang="scss" scoped>
.pay-select-session {
    margin: 12px 0 0 0;

    .supplier_timeslots_session_id {
        width: 100%;
    }

    .select {
        position: relative;

        &::after {
            content: '';
            left: 0;
            top: 0;
            cursor: pointer;
            position: absolute;
            width: 100%;
            height: 100%;
        }
    }

    .drawer {
        width: 580px;
        padding: 24px 0;
    }

    .close {
        margin-bottom: 24px;
        padding: 0 50px;

        .icon {
            cursor: pointer;
        }
    }

    .time-group {
        max-height: calc(100vh - 100px);
        overflow: auto;
        padding: 0 50px;

        .klk-radio {
            border: solid 1px #e0e0e0;
            padding: 14px;
            width: 100%;

            &-checked {
                border-color: #4985e6;
            }
        }
    }

    .label {
        margin-bottom: 16px;

        span {
            font-size: 16px;
        }

        p {
            padding-top: 4px;
            font-size: 14px;
        }
    }
}
</style>
