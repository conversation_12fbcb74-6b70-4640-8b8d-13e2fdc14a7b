import { format } from 'date-fns';

/**
 * 处理package schedules
 */
const handlePackageSchedules = schedules => {
    let result = {};
    if (Array.isArray(schedules)) {
        schedules.forEach(function(item) {
            item.time = format(item.date, 'HH:mm');
            let dateKey = format(item.date, 'YYYY-MM-DD');
            if (!result[dateKey]) {
                result[dateKey] = [item];
            } else {
                result[dateKey].push(item);
            }
        });
    }
    return result;
};

function getTimesFromSchedules(date, schedules) {
    if (!date || !schedules) {
        return [];
    }
    return schedules[date] || [];
}

/**
 * 将高精度时间戳转换为格式化字符串（YYYY-MM-DD HH:MM:SS）
 *
 * @param {number} timestamp - 高精度时间戳（可能是微秒级或带额外精度的毫秒级）
 * @returns {string} 格式化后的时间字符串，例如 "2025-06-18 08:00:00"
 */

function microsecondTimestampToStr(date, time) {
    if (!date) {
        return '';
    }
    // date 格式 'YYYY-MM-DD'
    // time 格式 'HH:mm' or falsy
    const timePart = time ? `${time}:00` : '00:00:00';
    return `${date} ${timePart}`;
}

export {
    handlePackageSchedules,
    getTimesFromSchedules,
    microsecondTimestampToStr,
};
