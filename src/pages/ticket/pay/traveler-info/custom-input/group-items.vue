<template>
    <div class="group-items-wrapper">
        <div
            class="group-items-content"
            v-for="(groupItem, index) in secondGroup"
            :key="groupItem.id"
        >
            <klk-form-item
                :label="groupItem.name"
                :prop="getProp(groupItem.style.type, index)"
                :rules="setRules(groupItem)"
                :class="{ 'is-required': groupItem.style.required }"
            >
                <custom-input
                    :disabled="disabled"
                    :form-item="groupItem"
                    :prepend-prop="getProp(groupItem.style.type)"
                    :set-rules="setRules"
                >
                </custom-input>
            </klk-form-item>
        </div>
    </div>
</template>

<script>
import CustomInput from './index.vue';

export default {
    name: 'GroupItems',
    components: {
        CustomInput,
    },
    props: {
        disabled: {
            type: Boolean,
            default: false,
        },
        formItem: {
            type: Object,
            required: true,
            default: () => {},
        },
        prependProp: {
            type: String,
            required: true,
            default: '',
        },
        setRules: {
            type: Function,
            required: true,
        },
    },
    computed: {
        secondGroup() {
            if (this.formItem.style.type === 6) {
                return this.formItem.options;
            } else {
                return [this.formItem];
            }
        },
    },
    methods: {
        getProp(type, index) {
            // 3 : 单选 、 4 : 多选 、 8 : 电话这三个的值放在operation中,其他都放在content中
            const typeName = [3, 4, 8].includes(type) ? 'operation' : 'content';
            if (this.formItem.style.type === 6) {
                return `${this.prependProp}.options.${index}.${typeName}`;
            } else {
                return `${this.prependProp}.${typeName}`;
            }
        },
    },
};
</script>

<style lang="scss">
.group-items-wrapper {
    display: flex;
    width: 694px;
    flex-wrap: wrap;
    justify-content: space-between;

    .group-items-content {
        .klk-form-item {
            margin-top: 20px;
            flex: 1;
            width: 50%;

            .klk-form-item-label {
                &::after {
                    content: '';
                }
            }

            &.is-required {
                .klk-form-item-label {
                    &::after {
                        content: '*';
                        color: red !important;
                    }
                }
            }
        }
    }
}
</style>
