<template>
    <div
        class="traveler-info-form-item"
        :class="{ 'is-group': formItem.is_group }"
    >
        <!--input-item-->
        <klk-input
            v-if="formItem.style.type === 0"
            type="text"
            style-type="outlined"
            :placeholder="formItem.hint"
            :disabled="disabled"
            v-model="formItem.content"
        >
        </klk-input>

        <!--datepicker-item-->
        <date-picker
            v-if="formItem.style.type === 1"
            :date-data="formItem"
            :disabled="disabled"
            @dateChange="handleChangeGeneralDate($event, formItem)"
        ></date-picker>

        <!--time-item-->
        <time-picker
            v-if="[2, 9].includes(formItem.style.type)"
            :time-data="formItem"
            :disabled="disabled"
            @timeChange="handleChangeGeneralTime($event, formItem)"
        >
        </time-picker>

        <!--select-item-->
        <select-single
            v-if="formItem.style.type === 3"
            :data="formItem"
            :disabled="disabled"
            :language-list="languageList"
            :country-list="countryList"
            v-model="formItem.operation"
        ></select-single>

        <select-multi
            v-if="formItem.style.type === 4"
            :data="formItem"
            :disabled="disabled"
            :language-list="languageList"
            :country-list="countryList"
            :passVal.sync="formItem.operation"
        ></select-multi>

        <!--checkbox-item-->
        <checkbox
            v-if="formItem.style.type === 5"
            v-model="formItem.content"
            :disabled="disabled"
            :label="formItem.hint"
        ></checkbox>
        <!--textarea-item-->
        <klk-input
            class="textarea"
            v-if="formItem.style.type === 7"
            type="textarea"
            :disabled="disabled"
            :placeholder="formItem.hint"
            words-count
            :maxlength="formItem.style.match_rule.max_len || 100"
            :words-count-function="wordsCountFunction"
            v-model="formItem.content"
        >
        </klk-input>

        <!--phone-item-->
        <div v-if="formItem.style.type === 8" class="phone-wrapper">
            <klk-form-item
                :rules="setRules(formItem)"
                :prop="`${prependProp}.operation`"
            >
                <select-single
                    :disabled="disabled"
                    :language-list="languageList"
                    :country-list="countryList"
                    :hint="$t('global.select.palceholder')"
                    :data="formItem"
                    v-model="formItem.operation"
                ></select-single>
            </klk-form-item>
            <klk-form-item
                :rules="getMobileRule(formItem)"
                :prop="`${prependProp}.content`"
            >
                <klk-input
                    type="text"
                    style-type="outlined"
                    :placeholder="formItem.hint"
                    v-model="formItem.content"
                >
                </klk-input>
            </klk-form-item>
        </div>
    </div>
</template>

<script>
import { cloneDeep } from 'lodash';
import getAllLanguageCode from './js/languageCode';
import getAllCountryCode from './js/countryCode';
import languageMap from './js/languageMap';
import TimePicker from './time-picker.vue';
import DatePicker from './date-picker.vue';
import Checkbox from './checkbox.vue';
import SelectSingle from './select-single.vue';
import SelectMulti from './select-multi.vue';

export default {
    components: {
        TimePicker,
        DatePicker,
        Checkbox,
        SelectSingle,
        SelectMulti,
    },
    props: {
        disabled: {
            type: Boolean,
            default: false,
        },
        formItem: {
            type: Object,
            required: true,
            default: () => {},
        },
        prependProp: {
            type: String,
            required: true,
            default: '',
        },
        setRules: {
            type: Function,
            required: true,
        },
    },
    computed: {
        languageList() {
            return this.setJson('languageList');
        },
        countryList() {
            return this.setJson('countryList');
        },
    },
    methods: {
        getMobileRule(formItem) {
            const item = cloneDeep(formItem);
            item.style.type = 0;
            return this.setRules(item);
        },
        setJson(type) {
            let data =
                type === 'countryList'
                    ? getAllCountryCode()
                    : getAllLanguageCode();
            data.forEach(item => {
                item.name = JSON.parse(
                    JSON.stringify(
                        item.i18ns[languageMap[window.KLK_LANG]] ||
                            item.i18ns['en_US'],
                    ),
                );
                item.areaCode =
                    type === 'countryList'
                        ? `${item.name} (+${item.country_number})`
                        : '';
                item.field_key =
                    type === 'countryList' ? item.country_code : item.code5;
                delete item.i18ns;
            });
            return data;
        },
        handleChangeGeneralTime(e, item) {
            item.content = e;
        },
        handleChangeGeneralDate(e, item) {
            item.content = e;
        },
        wordsCountFunction(wordCount) {
            return `${(wordCount || '').length}/100`;
        },
    },
};
</script>

<style lang="scss">
.traveler-info-form-item {
    &.is-group {
        width: 694px !important;
    }

    .textarea {
        width: 694px !important;
    }

    .phone-wrapper {
        max-width: 694px !important;
        display: flex;
        justify-content: space-between;
    }
}
</style>
