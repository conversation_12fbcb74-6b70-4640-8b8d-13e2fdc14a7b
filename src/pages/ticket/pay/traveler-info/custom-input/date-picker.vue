<template>
    <div class="form-item-date-picker" v-click-outside="close">
        <klk-input
            :disabled="disabled"
            prepend-icon="icon_time_calendar"
            :append-icon="arrowIcon"
            v-model="dateText"
            @focus="handleFocus"
            readonly
            :placeholder="dateData.hint || ''"
        >
        </klk-input>
        <klk-date-picker
            v-show="showDatePicker"
            ref="datePicker"
            view-switchable
            :disabled="disabled"
            :date.sync="currentDate"
            :first-day-of-week="1"
            :min-date="
                dateData.style.match_rule.min_date
                    ? new Date(dateData.style.match_rule.min_date)
                    : new Date('1000-01-01')
            "
            :max-date="
                dateData.style.match_rule.max_date
                    ? new Date(dateData.style.match_rule.max_date)
                    : new Date('3000-01-01')
            "
            @select="onChange"
        ></klk-date-picker>
    </div>
</template>

<script>
import ClickOutside from 'vue-click-outside';
import { addDays, format, isAfter, isBefore, parse } from 'date-fns';

export default {
    name: 'DatePicker',

    directives: {
        ClickOutside,
    },
    props: {
        disabled: {
            type: Boolean,
            default: false,
        },
        dateData: {
            type: Object,
            default: null,
        },
    },
    data() {
        return {
            doUpdate: true,
            dateText: this.formatDate(this.dateData.content),
            showDatePicker: false,
            currentDate: parse(this.dateData.content || new Date()),
        };
    },
    computed: {
        arrowIcon() {
            return this.showDatePicker
                ? 'icon_navigation_chevron_up'
                : 'icon_navigation_chevron_down';
        },
    },
    watch: {
        dateData: {
            handler(val) {
                if (val.content && !this.checkDateValid(val)) {
                    this.$emit('dateChange', '');
                    this.doUpdate = false; // 为空时设置为默认值，所以不更新
                } else if (!val.content) {
                    this.dateText = '';
                    this.currentDate = parse(val.content || new Date());
                    this.$refs.datePicker &&
                        this.$refs.datePicker.setViewDate(
                            parse(val.content || new Date()),
                        );
                } else {
                    this.doUpdate = true; // 这里需要正常更新
                    this.dateText = this.formatDate(val.content);
                    this.currentDate = parse(val.content || new Date());
                    this.$refs.datePicker &&
                        this.$refs.datePicker.setViewDate(
                            parse(val.content || new Date()),
                        );
                }
            },
            immediate: true,
            deep: true,
        },
    },
    methods: {
        checkDateValid(data) {
            const isBeforeDate =
                !data.style.match_rule.min_date ||
                isBefore(
                    addDays(new Date(data.style.match_rule.min_date), -1),
                    data.content,
                );
            const isAfterDate =
                !data.style.match_rule.max_date ||
                isAfter(new Date(data.style.match_rule.max_date), data.content);

            return data.content && isBeforeDate && isAfterDate;
        },
        formatDate(d) {
            return d ? format(d, 'YYYY-MM-DD') : '';
        },
        onChange(d) {
            if (this.doUpdate) {
                this.dateText = format(d, 'YYYY-MM-DD');
                this.$emit('dateChange', this.dateText);
                this.showDatePicker = false;
            }

            this.doUpdate = true;
        },
        handleFocus() {
            this.showDatePicker = true;
        },
        close(e) {
            const pickerDom = this.$refs.datePicker.$el;
            if (
                e &&
                pickerDom &&
                (pickerDom.contains(e.target) ||
                    [...e.path].includes(pickerDom))
            ) {
                return false;
            }
            this.showDatePicker = false;
        },
    },
};
</script>

<style lang="scss">
.form-item-date-picker {
    position: relative;

    .klk-date-picker {
        position: absolute;
        left: 0;
        top: 50px;
        border: 1px solid #e4e7ed;
        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
        z-index: 88;
    }

    // 修改箭头大小
    .klk-input-inner .klk-input-append svg {
        font-size: 16px;
    }
}
</style>
