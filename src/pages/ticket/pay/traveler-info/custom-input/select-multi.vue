<template>
    <div class="select-multi">
        <klk-select
            :disabled="disabled"
            class="public-select"
            style-type="outlined"
            multiple
            filterable
            :max-height="300"
            :value="selectVal"
            @change="handleChange"
            :placeholder="data.hint"
        >
            <klk-option
                v-for="(select, i) in selectDataList"
                :key="`${i}-${i.id}`"
                :value="select.field_key"
                :label="
                    data.style.type === 8 &&
                    [1, 2].includes(data.style.option_all_type)
                        ? select.formattedCountryTxt
                        : select.name
                "
            >
            </klk-option>
        </klk-select>
    </div>
</template>

<script>
export default {
    name: 'SelectMulti',
    props: {
        data: {
            type: Object,
            default: () => {},
        },
        disabled: {
            type: Boolean,
            default: false,
        },
        passVal: {
            type: Array,
            default: () => [],
        },
        languageList: {
            type: Array,
            default: () => [],
        },
        countryList: {
            type: Array,
            default: () => [],
        },
    },
    data() {
        return {
            selectVal: [],
        };
    },
    computed: {
        selectDataList() {
            const type = this.data.style.option_all_type;
            if (type === 1) {
                return this.countryList;
            } else if (type === 2) {
                return this.languageList;
            } else {
                return this.data.options;
            }
        },
        listMap() {
            const list = this.selectDataList;
            const map = new Map();
            list.forEach(o => map.set(o.field_key, o));
            return map;
        },
    },
    watch: {
        passVal: {
            handler(newVal, oldVal) {
                newVal = newVal || [];
                oldVal = oldVal || [];
                if (newVal.length !== oldVal.length) {
                    let list = [];
                    newVal.forEach(item => {
                        list.push(item.field_key);
                    });
                    this.selectVal = list;
                }
            },
        },
    },

    methods: {
        handleChange(val) {
            let tpl = [];
            val.forEach(item => {
                tpl.push(this.listMap.get(item));
            });
            this.$emit('update:passVal', tpl);
        },
    },
    created() {
        // 初始化的时候的回填功能
        if (this.passVal && this.passVal.length > 0) {
            let list = [];
            this.passVal.forEach(item => {
                list.push(item.field_key);
            });
            this.selectVal = list;
        }
    },
};
</script>
