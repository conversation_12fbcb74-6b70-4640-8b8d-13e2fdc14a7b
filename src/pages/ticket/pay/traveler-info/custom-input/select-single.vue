<template>
    <div class="select-single">
        <klk-select
            class="public-select"
            style-type="outlined"
            v-model="selectVal"
            :disabled="disabled"
            :placeholder="hint || data.hint || ''"
            filterable
        >
            <template v-if="isNeedRecommend">
                <klk-option-group label="Frequently">
                    <klk-option
                        v-for="(select, i) in recommendObj.recommendList"
                        :key="`${i}-${i.id}`"
                        :value="select.field_key"
                        :label="
                            data.style.type === 8 &&
                            [1, 2].includes(data.style.option_all_type)
                                ? select.areaCode
                                : select.name
                        "
                    />
                </klk-option-group>
                <klk-option-group
                    :label="$t('country.otherCountriesOrDistricts')"
                >
                    <klk-option
                        v-for="(select, i) in recommendObj.otherList"
                        :key="`${i}-${i.id}`"
                        :value="select.field_key"
                        :label="
                            data.style.type === 8 &&
                            [1, 2].includes(data.style.option_all_type)
                                ? select.areaCode
                                : select.name
                        "
                    />
                </klk-option-group>
            </template>
            <template v-else>
                <klk-option
                    v-for="(select, i) in selectDataList"
                    :key="`${i}-${i.id}`"
                    :value="select.field_key"
                    :label="
                        data.style.type === 8 &&
                        [1, 2].includes(data.style.option_all_type)
                            ? select.areaCode
                            : select.name
                    "
                />
            </template>
        </klk-select>
    </div>
</template>

<script>
import { getLangPreferCountryCode } from './js/langConf';

export default {
    name: 'SelectSingle',
    props: {
        data: {
            type: Object,
            default: () => {},
        },
        value: {
            type: Array,
            default: () => [],
        },
        disabled: {
            type: Boolean,
            default: false,
        },
        hint: {
            type: String,
            default: '',
        },
        languageList: {
            type: Array,
            default: () => [],
        },
        countryList: {
            type: Array,
            default: () => [],
        },
    },
    computed: {
        isNeedRecommend() {
            // country 的时候要加上推荐和搜索
            const type = this.data.style.option_all_type;
            return type === 1;
        },
        selectDataList() {
            const type = this.data.style.option_all_type;
            if (type === 1) {
                return this.countryList;
            } else if (type === 2) {
                return this.languageList;
            } else {
                return this.data.options;
            }
        },
        recommendObj() {
            let recommendList = [];
            let otherList = [];

            if (this.isNeedRecommend) {
                const preferLangList = getLangPreferCountryCode(
                    window.KLK_LANG,
                );

                recommendList = this.countryList
                    .filter(option =>
                        preferLangList.includes(option.country_code),
                    )
                    .sort(
                        (a, b) =>
                            preferLangList.indexOf(a.country_code) -
                            preferLangList.indexOf(b.country_code),
                    );
                otherList = this.countryList.filter(
                    option => !preferLangList.includes(option.country_code),
                );
            }
            return { recommendList, otherList };
        },
        selectVal: {
            get() {
                return this.value && this.value.length
                    ? this.value[0].field_key
                    : '';
            },
            set(newValue) {
                let emitVal = [];
                for (let i = 0; i < this.selectDataList.length; i++) {
                    if (newValue === this.selectDataList[i].field_key) {
                        emitVal.push(this.selectDataList[i]);
                        break;
                    }
                }
                this.$emit('input', emitVal);
            },
        },
    },
};
</script>
