<template>
    <klk-checkbox
        :disabled="disabled"
        v-model="checkData"
        class="custom-checkbox"
    >
        {{ label }}
    </klk-checkbox>
</template>

<script>
export default {
    name: 'CheckBox',
    props: {
        disabled: {
            type: Boolean,
            default: false,
        },
        value: {
            type: String,
            default: '0',
        },
        label: {
            type: String,
            default: '',
        },
    },
    computed: {
        checkData: {
            get() {
                return this.value === '1' || this.value === true;
            },
            set(newValue) {
                const tpl = newValue ? '1' : '0';
                this.$emit('input', tpl);
            },
        },
    },
};
</script>

<style lang="scss">
.custom-checkbox {
    width: 335px;
}
</style>
