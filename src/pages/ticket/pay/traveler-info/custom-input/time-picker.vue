<template>
    <div
        class="form-item-time-picker"
        :class="timeData.style.type !== 9 ? 'time-date-picker' : 'time-picker'"
    >
        <date-picker
            :disabled="disabled"
            v-if="timeData && timeData.style.type !== 9"
            :date-data="timeDate"
            @dateChange="handleChangeGeneralDate"
        >
        </date-picker>
        <div class="time-picker-content">
            <klk-select
                :disabled="disabled"
                class="time-select"
                style-type="outlined"
                :max-height="300"
                v-model="time.hour"
            >
                <klk-option
                    v-for="(select, i) in hourList"
                    :key="`hour_${i}`"
                    :value="select"
                    :label="select"
                ></klk-option>
            </klk-select>
            <div class="divider">:</div>
            <klk-select
                :disabled="disabled"
                class="time-select"
                style-type="outlined"
                :max-height="300"
                v-model="time.minute"
            >
                <klk-option
                    v-for="(select, i) in minuteList"
                    :key="`min_${i}`"
                    :value="select"
                    :label="select"
                ></klk-option>
            </klk-select>
        </div>
    </div>
</template>

<script>
import DataPicker from './date-picker.vue';

export default {
    name: 'FormItemTimePicker',
    components: {
        DataPicker,
    },
    props: {
        timeData: {
            type: Object,
            default: null,
        },
        disabled: {
            type: Boolean,
            default: false,
        },
    },
    watch: {
        timeData(newVal, oldVal) {
            if (newVal && oldVal && newVal.content !== oldVal.content) {
                this.generateTime();
            }
        },

        time: {
            handler() {
                if (this.timeData.style.type !== 9) {
                    if (this.time.date && this.time.hour && this.time.minute) {
                        const tpl = `${this.time.date} ${this.time.hour}:${this.time.minute}`;
                        this.$emit('timeChange', tpl);
                    }
                } else {
                    if (this.time.hour && this.time.minute) {
                        const tpl = `${this.time.hour}:${this.time.minute}`;
                        this.$emit('timeChange', tpl);
                    }
                }
            },
            deep: true,
        },
    },
    data() {
        return {
            time: {
                hour: '',
                minute: '',
                date: '',
            },
            timeDate: Object.assign({}, this.timeData),
        };
    },
    computed: {
        hourList() {
            let _list = [];
            for (let i = 0; i < 24; i++) {
                if (i < 10) {
                    _list.push(`0${i}`);
                } else {
                    _list.push(`${i}`);
                }
            }
            return _list;
        },
        minuteList() {
            let _list = [];
            for (let i = 0; i < 12; i++) {
                if (i < 2) {
                    _list.push(`0${i * 5}`);
                } else {
                    _list.push(`${i * 5}`);
                }
            }
            return _list;
        },
    },
    methods: {
        generateTime() {
            if (this.timeData && this.timeData.style.type !== 9) {
                this.date = this.timeData.content.split(' ')[0];
                const getTime = this.timeData.content.split(' ')[1].split(':');
                this.time.hour = getTime[0];
                this.time.minute = getTime[1];
            } else {
                const getTime = this.timeData.content.split(':');
                this.time.hour = getTime[0];
                this.time.minute = getTime[1];
            }
        },
        handleChangeGeneralDate(e) {
            this.time.date = e;
        },
    },
    created() {
        // 回填功能（兼容自助修改订单）
        this.generateTime();
    },
};
</script>
<style lang="scss">
.form-item-time-picker {
    .time-picker-content {
        width: 335px;
        display: flex;
        justify-content: space-between;
        vertical-align: center;
        .time-select {
            flex: 1;
        }
        .divider {
            line-height: 44px;
            width: 20px;
            text-align: center;
        }
    }
}
</style>
