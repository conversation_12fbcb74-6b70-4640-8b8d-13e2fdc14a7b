<template>
    <klk-form
        :model="travelerInfoForm"
        ref="form"
        class="ticket-pay-traveler-info form-content"
        inline
    >
        <!--    booking traveler info-->
        <div v-if="travelerInfoForm.travelerInfo.booking_traveler_info">
            <klk-form-item
                v-for="(bookingTravelerInfo, index) in travelerInfoForm
                    .travelerInfo.booking_traveler_info"
                :label="bookingTravelerInfo.name"
                :key="bookingTravelerInfo.id"
                :rules="
                    setRules(
                        bookingTravelerInfo,
                        bookingTravelerInfo.style.type === 8,
                    )
                "
                :prop="
                    getProp(
                        `travelerInfo.booking_traveler_info.${index}.`,
                        bookingTravelerInfo,
                    )
                "
                :class="{ 'is-required': bookingTravelerInfo.style.required }"
            >
                <traveler-info-item
                    :disabled="disabled"
                    :form-item="bookingTravelerInfo"
                    :prepend-prop="
                        `travelerInfo.booking_traveler_info.${index}.`
                    "
                    :set-rules="setRules"
                >
                </traveler-info-item>
            </klk-form-item>
        </div>

        <!--      unit traveler info-->
        <div
            v-if="
                travelerInfoForm.travelerInfo.unit_traveler_info &&
                    travelerInfoForm.travelerInfo.unit_traveler_info.length > 0
            "
        >
            <div
                v-for="(unitTravelers, index) in travelerInfoForm.travelerInfo
                    .unit_traveler_info"
                :key="index"
            >
                <template v-if="unitTravelers.other_info_item">
                    <klk-section-title class="traveler-info-title" size="h4">
                        {{ unitTravelers.sku_name }}
                    </klk-section-title>

                    <klk-form-item
                        v-for="(unitTravelerInfo,
                        itemIndex) in unitTravelers.other_info_item"
                        :label="unitTravelerInfo.name"
                        :key="unitTravelerInfo.id"
                        :rules="setRules(unitTravelerInfo)"
                        :prop="
                            getProp(
                                `travelerInfo.unit_traveler_info.${index}.other_info_item.${itemIndex}.`,
                                unitTravelerInfo,
                            )
                        "
                        :class="{
                            'is-required': unitTravelerInfo.style.required,
                        }"
                    >
                        <traveler-info-item
                            :disabled="disabled"
                            :form-item="unitTravelerInfo"
                            :prepend-prop="
                                `travelerInfo.unit_traveler_info.${index}.other_info_item.${itemIndex}.`
                            "
                            :set-rules="setRules"
                        ></traveler-info-item>
                    </klk-form-item>
                </template>
            </div>
        </div>
        <!--    extra info-->
        <div
            v-if="
                travelerInfoForm.extraInfo &&
                    travelerInfoForm.extraInfo.length > 0
            "
        >
            <klk-section-title class="traveler-info-title" size="h3">
                {{ $t('extra_info') }}
            </klk-section-title>
            <klk-form-item
                v-for="(extraInfo, index) in travelerInfoForm.extraInfo"
                :label="extraInfo.name"
                :key="extraInfo.id"
                :rules="setRules(extraInfo)"
                :prop="getProp(`extraInfo.${index}.`, extraInfo)"
                :class="{ 'is-required': extraInfo.style.required }"
            >
                <traveler-info-item
                    :disabled="disabled"
                    :form-item="extraInfo"
                    :prepend-prop="`extraInfo.${index}.`"
                    :set-rules="setRules"
                ></traveler-info-item>
            </klk-form-item>
        </div>
    </klk-form>
</template>

<script>
import TravelerInfoItem from './traveler-info-item.vue';

export default {
    components: {
        TravelerInfoItem,
    },
    props: {
        travelerInfoForm: {
            type: Object,
            required: true,
            default: () => {},
        },
        disabled: {
            type: Boolean,
            default: false,
        },
    },

    methods: {
        getProp(str, formItem) {
            const type = formItem.style.type;
            // 3 : 单选 、 4 : 多选 、 8 : 电话这三个的值放在operation中,其他都放在content中
            return str + ([3, 4, 8].includes(type) ? 'operation' : 'content');
        },
        checkValid() {
            let flag = false;
            this.$refs.form.validate(valid => {
                flag = valid;
            });
            return flag;
        },
        testId(id) {
            // 注意：这里如果改了，需要同步给admin端的 Poyan Zhu
            // 1 "验证通过!", 0 //校验不通过
            const format = /^(([1][1-5])|([2][1-3])|([3][1-7])|([4][1-6])|([5][0-4])|([6][1-5])|([7][1])|([8][1-2]))\d{4}(([1][9]\d{2})|([2]\d{3}))(([0][1-9])|([1][0-2]))(([0][1-9])|([1-2][0-9])|([3][0-1]))\d{3}[0-9xX]$/;
            //号码规则校验
            if (!format.test(id)) {
                return false;
            }
            //区位码校验
            //出生年月日校验   前正则限制起始年份为1900;
            const year = id.substr(6, 4), //身份证年
                month = id.substr(10, 2), //身份证月
                date = id.substr(12, 2), //身份证日
                time = Date.parse(month + '-' + date + '-' + year), //身份证日期时间戳date
                now_time = Date.parse(new Date()), //当前时间戳
                dates = new Date(year, month, 0).getDate(); //身份证当月天数
            if (time > now_time || date > dates) {
                return false;
            }
            //校验码判断
            const c = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2]; //系数
            const b = ['1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2']; //校验码对照表
            const id_array = id.split('');
            let sum = 0;
            for (let k = 0; k < 17; k++) {
                sum += parseInt(id_array[k]) * parseInt(c[k]);
            }
            return id_array[17].toUpperCase() === b[sum % 11].toUpperCase();
        },
        // 校验规则设置
        setRules(data, noNeedValidate) {
            if (noNeedValidate) {
                return [];
            }

            const type = data.style.type === 8 ? 'mobileCode' : '';
            const isRequired = data.style.required;
            let ruleList = [];
            const trigger = data.style.type === 1 ? 'change' : 'blur';

            ruleList.push({
                validator: (_rule, value, callback) => {
                    if (isRequired && (!value || !value.length > 0)) {
                        callback(new Error(this.$t('all_required')));
                    }
                    // checkbox
                    if (
                        isRequired &&
                        data.style.type === 5 &&
                        (value === '0' || !value)
                    ) {
                        callback(new Error(this.$t('all_required')));
                    }
                    // 单独验证长度
                    if (
                        [0, 3].includes(data.style.match_rule.type) &&
                        type !== 'mobileCode'
                    ) {
                        const len = (value || '').length;
                        const minLen = data.style.match_rule.min_len || 0;
                        const maxLen = data.style.match_rule.max_len || 1000000;
                        if (minLen > len || len > maxLen) {
                            callback(new Error(this.$t('all_required_right')));
                        }
                    }
                    // 因为手机区号和手机号放在一起了，这里需要特殊处理
                    if (data.style.match_rule.regex && type !== 'mobileCode') {
                        const reg = new RegExp(data.style.match_rule.regex);
                        if (!reg.test(value)) {
                            callback(new Error(this.$t('all_required_right')));
                        } else {
                            callback();
                        }
                    } else if (data.style.match_rule.type === 2) {
                        if (value.includes('.')) {
                            callback(new Error(this.$t('all_required_right')));
                        } else if (
                            !(
                                data.style.match_rule.min_num <=
                                    Number(value) &&
                                Number(value) <= data.style.match_rule.max_num
                            )
                        ) {
                            callback(new Error(this.$t('all_required_right')));
                        } else {
                            callback();
                        }
                    } else if (data.style.match_rule.type === 4) {
                        const IDValidator = this.testId(value);
                        if (!IDValidator) {
                            callback(new Error(this.$t('all_required_right')));
                        } else {
                            callback();
                        }
                    } else {
                        callback();
                    }
                },
                trigger: trigger,
            });
            return ruleList;
        },
    },
};
</script>

<style lang="scss">
.ticket-pay-traveler-info {
    width: 100%;
    padding-top: 12px !important;

    .traveler-info-title {
        margin: 20px 0;
    }
}
</style>
