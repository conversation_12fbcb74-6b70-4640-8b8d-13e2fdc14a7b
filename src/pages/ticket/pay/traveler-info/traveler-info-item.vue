<template>
    <div class="traveler-info-item">
        <custom-input
            :disabled="disabled"
            :form-item="formItem"
            :prepend-prop="prependProp"
            :set-rules="setRules"
        >
        </custom-input>
        <group-items
            :disabled="disabled"
            :prepend-prop="`${prependProp}operation.0`"
            :set-rules="setRules"
            v-if="
                formItem.is_group &&
                    formItem.operation &&
                    formItem.operation.length > 0
            "
            :form-item="formItem.operation[0]"
        >
        </group-items>
    </div>
</template>

<script>
import CustomInput from './custom-input/index.vue';
import GroupItems from './custom-input/group-items.vue';

export default {
    name: 'TravelerInfoItem',
    components: {
        CustomInput,
        GroupItems,
    },
    props: {
        formItem: {
            type: Object,
            required: true,
            default: () => {},
        },
        prependProp: {
            type: String,
            required: true,
            default: '',
        },
        setRules: {
            type: Function,
            required: true,
        },
        disabled: {
            type: <PERSON><PERSON><PERSON>,
            default: false,
        },
    },
};
</script>

<style lang="scss"></style>
