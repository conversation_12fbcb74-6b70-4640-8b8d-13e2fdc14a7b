<template>
    <div class="ticket-pay-package-time date-time-wrapper" v-if="isShowTimes">
        <p class="title">{{ $t('participation_time') }}</p>
        <div
            class="date-time-content time"
            @click="updateSelectedTime(item)"
            v-for="item in timesArrangement"
            :key="item.arrangement_id"
            :class="{
                active: selectedTime === item.time,
                disabled: item.stock <= 0,
            }"
        >
            {{ item.time }}
        </div>
    </div>
</template>

<script>
import { mapState } from 'vuex';
import { standardFormatDate } from '../../activity/util';

export default {
    props: {
        timesArrangement: {
            default: () => [],
            required: true,
        },
        activityInfo: {
            type: Object,
            required: true,
            default: () => {},
        },
    },
    computed: {
        ...mapState('ticket/pay', [
            'packageId',
            'skuId',
            'selectedDate',
            'selectedTime',
        ]),
        isShowTimes() {
            if (!this.timesArrangement || this.timesArrangement.length === 0)
                return false;
            return (
                this.timesArrangement.length > 1 ||
                this.timesArrangement[0].time !== '00:00'
            );
        },
    },
    methods: {
        formatValue(arrangement) {
            return arrangement && arrangement.date
                ? standardFormatDate(arrangement.date, 1)
                : '';
        },
        updateSelectedTime(item) {
            if (item.stock <= 0) return;
            this.$store.commit('ticket/pay/UPDATE_SELECTED_TIME', item.time);
        },
    },
};
</script>

<style lang="scss"></style>
