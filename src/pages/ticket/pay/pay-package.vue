<template>
    <div class="ticket-pay-package">
        <!--    日期-->
        <pay-package-date
            v-loading="loadingSkus"
            :activity-info="activityInfo"
            @changeTours="changeTours"
        ></pay-package-date>

        <!--    时间-->
        <pay-package-time
            :activity-info="activityInfo"
            :times-arrangement="timesArrangement"
        >
        </pay-package-time>

        <!--    数量-->
        <pay-package-sku
            :activity-info="activityInfo"
            :min-pax="minPax"
            :max-pax="maxPax"
            :isAid="isAid"
            :is-tours="isTours"
            :is-new-activity-version="isNewActivityVersion"
            v-loading="loadingSkus"
        ></pay-package-sku>
        <!-- 选择时间段 -->
        <pay-select-session
            v-if="bookingTags.includes('usj_timeslot')"
            ref="paySelectSession"
            :selected-skus="selectedSkus"
            :price-info="priceInfo"
        />
    </div>
</template>

<script>
import { mapState, mapGetters } from 'vuex';
import urlObj from '@/common/url';
import { TEMPLATE_VERTICAL_MAP } from '@/common/const_data';
import { handlePackageSchedules, getTimesFromSchedules } from './pay';
import { countOriginalSellPrice } from '../../activity/util';
import PayPackageDate from './pay-package-date.vue';
import PayPackageTime from './pay-package-time.vue';
import PayPackageSku from './pay-package-sku.vue';
import PaySelectSession from './pay-select-session.vue';

export default {
    name: 'TicketPayPackage',
    components: {
        PaySelectSession,
        PayPackageDate,
        PayPackageTime,
        PayPackageSku,
    },
    props: {
        priceInfo: {
            type: Object,
            required: true,
            default: () => {},
        },
        bookingTags: {
            type: Array,
            required: true,
            default: () => [],
        },
        selectedSkus: {
            type: Array,
            required: true,
            default: () => [],
        },
        isAid: {
            type: [Boolean, Number],
            required: true,
            default: false,
        },
        isNewActivityVersion: {
            type: Boolean,
            required: true,
            default: false,
        },
    },
    data() {
        return {
            minPax: 0,
            maxPax: 9999,
            loadingSkus: false,
            timesArrangement: [],
            activityInfo: {},
            isTours: false,
        };
    },
    computed: {
        ...mapState('ticket/pay', [
            'packageId',
            'skuId',
            'selectedDate',
            'selectedTime',
            'packageSchedules',
        ]),
        ...mapGetters('ticket/pay', ['selectedArrangementId']),
    },
    watch: {
        selectedDate(newVal) {
            newVal && this.getTimesArrangement();
        },

        selectedArrangementId(newVal) {
            newVal && this.fetchSkus();
        },
    },
    methods: {
        fetchSkus() {
            this.loadingSkus = true;
            klook.ajaxGet(
                urlObj.arrangements_units(this.selectedArrangementId),
                {
                    sku_id: this.skuId,
                },
                res => {
                    if (res.success && res.result) {
                        const packageInfo = res.result.package_info || {};
                        this.minPax = packageInfo.min_pax || 1;
                        this.maxPax = packageInfo.max_pax;
                        let skuPrices = (res.result.prices || []).map(sku => {
                            // format market_price if unit is promotion
                            if (sku.is_promotion) {
                                sku.market_price = countOriginalSellPrice(sku);
                            }
                            return sku;
                        });
                        const index = skuPrices.findIndex(
                            skuPrice => +skuPrice.sku_id === +this.skuId,
                        );
                        const sku = skuPrices[index];
                        skuPrices.splice(index, 1);
                        skuPrices.unshift(sku);
                        this.$store.commit(
                            'ticket/pay/UPDATE_SKU_PRICES',
                            skuPrices,
                        );
                    }
                    this.loadingSkus = false;
                },
            );
        },
        checkValid() {
            return this.$refs.paySelectSession
                ? this.$refs.paySelectSession.checkValid()
                : true;
        },
        getPaySelectSession() {
            return (
                (this.$refs.paySelectSession &&
                    this.$refs.paySelectSession.getData()) ||
                {}
            );
        },
        getTimesArrangement() {
            this.timesArrangement = getTimesFromSchedules(
                this.selectedDate,
                this.packageSchedules,
            );
            if (!this.selectedTime && this.timesArrangement.length > 0) {
                this.$store.commit(
                    'ticket/pay/UPDATE_SELECTED_TIME',
                    this.timesArrangement[0].time,
                );
            }
        },

        fetchPackageSchedules() {
            klook.ajaxGet(
                urlObj.packages_schedules,
                {
                    package_ids: this.packageId,
                },
                res => {
                    if (res.success && res.result) {
                        const packageSchedules = handlePackageSchedules(
                            res.result && res.result[this.packageId],
                        );
                        this.$store.commit(
                            'ticket/pay/UPDATE_PACKAGE_SCHEDULES',
                            packageSchedules,
                        );
                        if (this.selectedDate) {
                            this.getTimesArrangement();
                        }
                    }
                },
            );
        },
    },
    created() {
        // packages schedule
        this.fetchPackageSchedules();
        // fetch sku price to get isTours
    },
    mounted() {
        this.activityInfo = {
            id: this.priceInfo.activity_id,
            verticalType:
                TEMPLATE_VERTICAL_MAP[this.priceInfo.activity_template_id],
        };
    },
};
</script>

<style lang="scss">
.ticket-pay-package {
    padding: 4px 20px 12px 20px;
    background: #ffffff;
    border-radius: 4px;

    .title {
        margin-bottom: 10px;
    }

    .date-time-wrapper {
        padding: 10px 0 8px 0;

        .date-time-content {
            margin-top: 8px;
            display: inline-block;
            margin-right: 10px;
            padding: 7px 3px;
            vertical-align: top;
            border: 1px solid #f5f5f5;
            text-align: center;
            border-radius: 4px;
            cursor: pointer;

            &.date-content {
                min-width: 60px;
                height: 52px;
            }

            &:last-child {
                margin-right: 0;
            }

            &.disabled {
                background: #f5f5f5;
                cursor: not-allowed;

                &:hover {
                    border-color: #f5f5f5;
                }
            }

            &.other-date {
                display: inline-flex;
                align-items: center;

                .calendar {
                    width: 40px;
                    height: 40px;
                    margin-left: 4px;
                }
            }

            &.time {
                padding: 0;
                box-sizing: border-box;
                width: 62px;
                line-height: 26px;
                border-radius: 2px;
                font-size: 16px;
            }

            &:hover {
                border-color: #4d87e5;
            }

            &.active {
                background: #f1f6fd;
                border-color: #4d87e5;
                color: #4d87e5;
            }

            .date {
                font-weight: 600;
                font-size: 12px;
            }

            .price {
                font-size: 10px;
            }
        }
    }
}
</style>
