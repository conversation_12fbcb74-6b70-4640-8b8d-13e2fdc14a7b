<template>
    <div class="ticket-pay-order-detail">
        <p class="activity-name">
            {{ priceInfo.activity_name }}
        </p>
        <p class="package-name">
            {{ priceInfo.package_name }}
        </p>
        <div class="unit-wrapper">
            <p class="title">
                {{ $t('global.date') }}
            </p>
            <p class="info">
                {{ formatDate(priceInfo.schedule_time) }}
            </p>
        </div>
        <div class="unit-wrapper">
            <p class="title">
                {{ $t('units') }}
            </p>
            <p class="info" v-for="sku in selectedSkus" :key="sku.sku_id">
                {{ sku.count }} x {{ sku.sku_name }}
            </p>
        </div>
        <klk-divider class="divider"></klk-divider>
        <div class="pay-total">
            <div class="amount-title">{{ $t('payment_amount') }}</div>
            <div class="total-price">
                <span class="currency">{{
                    getCurrencySymbolByCurrencyCode(priceInfo.currency)
                }}</span>
                <span class="price">{{
                    formatPriceThousands(priceInfo.actual_amount)
                }}</span>
            </div>
            <span class="total-saving" v-if="priceInfo.SavingAmount > 0">
                {{ $t('total_saving') }}:
                {{ getCurrencySymbolByCurrencyCode(priceInfo.currency) }}
                {{ formatPriceThousands(priceInfo.SavingAmount) }}
            </span>
            <div class="total-tips">
                {{ $t('pay_total_tip') }}
            </div>

            <div class="data-security-tip">
                <div class="data-security-content">
                    <p class="title">{{ $t('pay_data_security') }}</p>
                    <p class="info">
                        {{ $t('pay_security_tip1') }}
                    </p>
                    <p class="info">
                        {{ $t('pay_security_tip2') }}
                    </p>
                    <p class="info">
                        <a :href="privacyStatementUrl" target="_blank">{{
                            $t('pay_privacy_statement')
                        }}</a>
                    </p>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    props: {
        priceInfo: {
            type: Object,
            required: true,
            default: () => {},
        },
        selectedSkus: {
            type: Array,
            required: true,
            default: () => [],
        },
    },
    computed: {
        privacyStatementUrl() {
            return klook.isCNSite
                ? 'https://www.klook.cn/zh-CN/tetris/tnc/agent-privacy-policy/'
                : '/conditions.html';
        },
    },
    methods: {
        formatDate(date) {
            return date ? date.substring(0, 19).replace('T', ' ') : '';
        },
    },
};
</script>

<style lang="scss">
.ticket-pay-order-detail {
    background: #fff;
    padding: 24px;
    line-height: 16px;
    font-size: 14px;

    .activity-name {
        margin-bottom: 6px;
        line-height: 19px;
        font-size: 16px;
        font-weight: 600;
    }

    .package-name {
        margin-bottom: 20px;
    }

    .unit-wrapper {
        padding-bottom: 6px;

        .title {
            color: #999999;
        }

        .title,
        .info {
            margin-bottom: 5px;
        }
    }

    .divider {
        width: 330px;
        margin: 24px -24px;
    }

    .pay-total {
        .amount-title {
            font-weight: 600;
            margin-bottom: 10px;
        }

        .total-price {
            font-weight: 600;
            font-size: 20px;
            margin-bottom: 6px;

            .currency {
                color: #999999;
            }

            .price {
                margin-left: 6px;
                color: #4d87e5;
            }
        }

        .total-saving {
            margin: 12px 0;
            padding: 0 4px;
            font-size: 12px;
            line-height: 20px;
            background: #f5f5f5;
            display: inline-block;
            margin-top: 6px;
        }

        .total-tips {
            font-size: 12px;
            line-height: 17px;
            color: #666666;
        }
    }

    .data-security-tip {
        margin: 20px -24px -24px;
        padding-top: 20px;
        background: #f5f5f5;

        .data-security-content {
            padding: 20px;
            background: #ebebeb;
            border-radius: 2px;
            font-size: 12px;
            color: #666666;

            .title {
                margin-bottom: 4px;
                font-weight: 600;
                font-size: 14px;
                color: #333333;
            }

            .info {
                margin-top: 8px;

                a {
                    color: #333333;
                    text-decoration: underline;
                }
            }
        }
    }
}
</style>
