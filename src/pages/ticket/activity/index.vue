<template>
    <div class="ticket-activity-index">
        <!--    导航-->
        <activity-nav :activity-info="activityInfo"></activity-nav>
        <!--    图片-->
        <klk-carousel>
            <klk-carousel-item
                v-for="item in activityImages"
                :key="item.image_url"
            >
                <img
                    class="banner-img"
                    :src="item.image_url"
                    :alt="item.image_desc"
                />
            </klk-carousel-item>
        </klk-carousel>
        <!--    活动信息-->
        <activity-intro
            :activity-info="activityInfo"
            @update-activity="updateActivityInfo"
        ></activity-intro>
        <!--    套餐选择-->
        <div id="packageOptions">
            <klk-section-title
                size="h2"
                class="package-options-title"
                decorative-line
            >
                {{ $t('activity.v2.label.package_options') }}
            </klk-section-title>
            <!--   套餐详情-->
            <activity-package
                v-for="packageItem in packages"
                :key="packageItem.package_id"
                :package-info="packageItem"
                :activity-info="activityInfo"
                @toggle-sku-detail="toggleSkuDetail"
                :show-sku-details="showSkuDetails"
            >
            </activity-package>
        </div>
        <div class="activity-info-wrapper">
            <div class="left-wrapper">
                <klk-markdown :content="summary"></klk-markdown>
                <!--    活动概览-->
                <activity-what-to-expect
                    id="whatToExpect"
                    :activity-info="activityInfo"
                >
                </activity-what-to-expect>
            </div>
            <div class="right-wrapper">
                <div class="nav-item-content">
                    <div
                        class="nav-item"
                        :class="{ active: nav.key === navActive }"
                        v-for="nav in navList"
                        :key="nav.key"
                    >
                        <span class="text" @click="goNav(nav.key)">
                            {{ nav.text }}
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import { mapState } from 'vuex';
import urlObj from '@/common/url';
import { filterBlockData } from '@/common/util';
import throttle from 'lodash/throttle';
import { markdownArrToHtml } from '@/pages/activity/util';
import ActivityNav from './activity-nav.vue';
import ActivityIntro from './activity-intro.vue';
import ActivityWhatToExpect from './activity-what-to-expect.vue';
import ActivityPackage from './package/index.vue';
import { handleImages, replaceLink, elmScrollTop } from './activity';

export default {
    name: 'TicketActivityIndex',
    components: {
        ActivityNav,
        ActivityIntro,
        ActivityWhatToExpect,
        ActivityPackage,
    },
    props: {
        activityInfo: {
            type: Object,
            required: true,
            default: () => {},
        },
    },
    data() {
        return {
            navActive: 'packageOptions',
            showSkuDetails: [],
            showSkuCalendars: [],
        };
    },
    computed: {
        ...mapState('ticket/activity', ['packages']),
        activityImages() {
            const images =
                (this.activityInfo.image_item &&
                    this.activityInfo.image_item.banner) ||
                [];
            return handleImages(images, 1290, 460, this.activityInfo.title);
        },
        navList() {
            return [
                {
                    key: 'packageOptions',
                    text: this.$t('activity.v2.label.package_options'),
                },
                {
                    key: 'whatToExpect',
                    text: this.$t('activity.v2.label.activity_desc'),
                },
            ];
        },
        summary() {
            if (this.activityInfo.use_section) {
                return markdownArrToHtml(
                    this.activityInfo.section_content.highlight_render_obj,
                );
            } else {
                return replaceLink(this.activityInfo.summary);
            }
        },
    },
    methods: {
        filterBlockData,
        toggleSkuDetail(id) {
            if (this.showSkuDetails.includes(id)) {
                this.showSkuDetails.splice(
                    this.showSkuDetails.findIndex(item => item === id),
                    1,
                );
            } else {
                this.showSkuDetails.push(id);
            }
        },
        navActiveByScroll() {
            return throttle(() => {
                const navList = [...this.navList].reverse();
                navList.forEach(item => {
                    const el = document.getElementById(item.key);
                    if (el && el.getBoundingClientRect().bottom > 0) {
                        this.navActive = item.key;
                    }
                });
            }, 100)();
        },
        goNav(nav) {
            elmScrollTop(nav);
        },
        loadPackageInfo() {
            this.$store.commit(
                'ticket/activity/UPDATE_ACTIVITY_INFO',
                this.activityInfo || {},
            );
            //过滤掉is_blocked的套餐filterPackage
            const packages = this.filterBlockData(
                this.activityInfo.packages || [],
            );
            if (packages.length === 0) return;
            klook.ajaxGet(
                urlObj.ticket.activity.package_skus(
                    packages.map(item => item.package_id).join(','),
                ),
                {},
                res => {
                    if (res.success && res.result) {
                        packages.forEach(packageItem => {
                            packageItem.skus =
                                (res.result || {})[packageItem.package_id] ||
                                [];
                        });
                        this.$store.commit(
                            'ticket/activity/UPDATE_PACKAGES',
                            packages,
                        );
                    }
                },
            );
        },
        updateActivityInfo() {
            this.$parent
                .loadActivityInfo()
                .then(() => {
                    this.loadPackageInfo();
                })
                .catch(error => {
                    console.error('Update activity info failed:', error);
                });
        },
    },
    created() {
        this.loadPackageInfo();
    },
    mounted() {
        this.$store.commit('UPDATE_SHOPPING_CART_STATUS', false);
        window.addEventListener('scroll', this.navActiveByScroll);
    },
    beforeDestroy() {
        this.$store.commit('UPDATE_SHOPPING_CART_STATUS', true);
        window.removeEventListener('scroll', this.navActiveByScroll);
    },
};
</script>

<style lang="scss">
.ticket-activity-index {
    .gray {
        color: #888888;
    }

    width: 1172px;
    margin: 0 auto;

    .klk-carousel {
        width: 1120px;
        height: 400px;

        .banner-img {
            height: 100%;
            border-radius: 4px;
        }
    }

    .package-options-title {
        margin-bottom: 20px;
    }

    .activity-info-wrapper {
        position: relative;
        display: flex;
        justify-content: space-between;

        .left-wrapper {
            flex: 0 0 760px;
        }

        .right-wrapper {
            flex: 1 1 auto;
            position: sticky;
            top: 20px;

            .nav-item-content {
                position: sticky;
                top: 60px;

                .nav-item {
                    margin-top: 20px;
                    float: right;
                    width: 280px;

                    font-size: 16px;
                    font-weight: bold;
                    position: relative;

                    &::before {
                        display: inline-block;
                        content: '';
                        position: absolute;
                        left: 0;
                        top: 0;
                        width: 4px;
                        border-radius: 2px;
                        height: 16px;
                        margin-top: 3px;
                    }

                    &.active {
                        color: #4c87e6;

                        &::before {
                            background-color: #4c87e6;
                        }
                    }

                    .text {
                        cursor: pointer;
                        margin-left: 16px;
                    }
                }
            }
        }
    }
}
</style>
