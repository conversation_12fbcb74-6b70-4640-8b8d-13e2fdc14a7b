<template>
    <div class="activity-nav">
        <router-link :to="{ name: 'agent_index' }"
            >{{ $t('global.text.index') }}
        </router-link>
        <klk-icon
            class="right-arrow"
            type="icon_navigation_chevron_right"
            size="8"
        ></klk-icon>
        <router-link
            :to="{
                name: 'city_activity',
                params: { city_id: activityInfo.city_id },
            }"
            >{{ activityInfo.city_name }}
        </router-link>
        <klk-icon
            class="right-arrow"
            type="icon_navigation_chevron_right"
            size="8"
        ></klk-icon>
        <span class="gray">{{ activityInfo.title }}</span>
    </div>
</template>

<script>
export default {
    name: 'TicketActivityNav',
    props: {
        activityInfo: {
            required: true,
            default: () => {},
        },
    },
};
</script>

<style lang="scss">
.activity-nav {
    margin: 10px 0;

    .right-arrow {
        margin: 0 15px;
    }
}
</style>
