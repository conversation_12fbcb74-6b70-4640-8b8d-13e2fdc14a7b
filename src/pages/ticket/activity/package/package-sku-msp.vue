<template>
    <div
        class="package-sku-msp gray"
        v-if="(sku.minimum_selling_prices || []).length > 0"
    >
        MSP：{{ sku.curreny }}
        {{ getMSPPrice(sku.currency, sku.minimum_selling_prices) }}
        <klk-poptip>
            <klk-icon type="icon_tips_tips"></klk-icon>
            <div slot="content" class="package-sku-msp-content">
                <p>{{ $t('suggest_low_price') }}</p>
                <ul class="package-sku-msp-item">
                    <li
                        v-for="(item, index) in sku.minimum_selling_prices"
                        :key="index"
                    >
                        <span>
                            {{ item.currency }}
                        </span>
                        <span>
                            {{ item.value }}
                        </span>
                    </li>
                </ul>
            </div>
        </klk-poptip>
    </div>
</template>

<script>
export default {
    name: 'TicketActivityPackageSkuMsp',
    props: {
        sku: {
            type: Object,
            required: true,
            default: () => {},
        },
        activityInfo: {
            type: Object,
            required: true,
            default: () => {},
        },
    },
    methods: {
        getMSPPrice(currency, prices) {
            return (prices.find(item => item.currency === currency) || {})
                .value;
        },
    },
};
</script>

<style lang="scss">
.package-sku-msp {
    display: inline-block;

    .package-sku-msp-content {
        text-align: left;

        .package-sku-msp-item {
            width: 130px;
            margin-top: 10px;

            li {
                display: flex;
                justify-content: space-between;
            }
        }
    }
}
</style>
