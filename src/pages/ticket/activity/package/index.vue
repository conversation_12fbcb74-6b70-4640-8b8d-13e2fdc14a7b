<template>
    <div class="activity-package-index">
        <div class="package-title">
            {{ packageInfo.package_name }}
            <!-- <p class="sub-package-name" v-if="packageInfo.sub_name">
                {{ packageInfo.sub_name }}
            </p> -->
        </div>
        <div v-for="(sku, index) in packageInfo.skus" :key="sku.sku_id">
            <div
                v-show="index < defaultShowNum || showAll"
                class="sku-wrapper"
                :class="{ 'show-detail': showSkuDetails.includes(sku.sku_id) }"
            >
                <klk-poptip class="sku-name" :content="sku.price_name">
                    {{ sku.price_name }}
                </klk-poptip>

                <div class="sku-detail">
                    <div class="left-content">
                        <p>
                            <klk-icon
                                size="14"
                                type="icon_time_calendar"
                                color="#36B37E"
                            ></klk-icon>
                            <span class="gray">
                                {{ $t('available_from') }}:
                            </span>
                            <span v-if="packageInfo.has_stocks">
                                {{ availableDate }}
                            </span>
                        </p>
                        <p>
                            <klk-icon
                                size="14"
                                type="icon_time_time"
                                color="#FFAB00"
                            ></klk-icon>
                            <span class="gray">
                                {{ $t('cut_off_time') }}:
                            </span>
                            <span v-if="packageInfo.has_stocks">
                                {{ cutOffTimeText }}
                            </span>
                        </p>
                    </div>
                    <div
                        class="right-content"
                        name="ticketActivityReadDetail"
                        @click="$emit('toggle-sku-detail', sku.sku_id)"
                    >
                        {{
                            showSkuDetails.includes(sku.sku_id)
                                ? $t('fold_up')
                                : $t('detail')
                        }}
                        <klk-icon
                            :class="{ up: showSkuDetails.includes(sku.sku_id) }"
                            class="detail-icon"
                            size="12"
                            type="detail"
                        ></klk-icon>
                    </div>
                </div>
                <div class="flash-sale">
                    <div class="flash-sale-icon" v-if="sku.flash_sale">
                        {{ $t('flash_sale') }}
                    </div>
                </div>
                <div class="price-content">
                    <p class="price">
                        <span
                            v-html="
                                $t('price_from', [
                                    formatPrice(
                                        sku.selling_price,
                                        sku.currency,
                                    ),
                                ])
                            "
                        ></span>

                        <del
                            class="gray"
                            v-if="+sku.market_price > +sku.selling_price"
                            >{{ sku.market_price }}
                        </del>
                    </p>
                    <!--          MSP-->
                    <package-sku-msp
                        :sku="sku"
                        :activity-info="activityInfo"
                    ></package-sku-msp>
                </div>
                <div class="calendar-content">
                    <!--    价格日历-->
                    <package-sku-calendar
                        :activity-info="activityInfo"
                        :package-id="packageInfo.package_id"
                        :sku-id="sku.sku_id"
                        :reset-date="true"
                        @change-date="changeDate"
                    ></package-sku-calendar>
                </div>
                <div class="btn-content">
                    <klk-button
                        :disabled="!packageInfo.has_stocks"
                        name="ticketActivityBookingNow"
                        @click="bookingNow(packageInfo.package_id, sku.sku_id)"
                    >
                        <span v-if="packageInfo.has_stocks">
                            {{ $t('booking_now') }}</span
                        >
                        <span v-else>
                            {{ $t('activity.v2.btn.sold_out') }}</span
                        >
                    </klk-button>
                </div>
            </div>
            <!--    套餐详情-->
            <package-sku-detail
                :package-detail="packageInfo"
                v-show="showSkuDetails.includes(sku.sku_id)"
            ></package-sku-detail>
        </div>
        <div
            class="show-all"
            v-show="packageInfo.skus.length > defaultShowNum"
            @click="showAll = !showAll"
        >
            {{ showAll ? $t('fold_up') : $t('detail') }}
            <klk-icon
                :class="{ up: showAll }"
                class="detail-icon"
                size="12"
                type="detail"
            ></klk-icon>
        </div>
    </div>
</template>

<script>
import { differenceInDays, format } from 'date-fns';
import PackageSkuDetail from './package-sku-detail.vue';
import PackageSkuCalendar from './package-sku-calendar.vue';
import PackageSkuMsp from './package-sku-msp.vue';

export default {
    name: 'TicketActivityPackageIndex',
    props: {
        activityInfo: {
            type: Object,
            required: true,
            default: () => {},
        },
        packageInfo: {
            required: true,
            default: () => {},
        },
        showSkuDetails: {
            default: () => [],
        },
    },
    components: {
        PackageSkuDetail,
        PackageSkuCalendar,
        PackageSkuMsp,
    },
    data() {
        return {
            defaultShowNum: 5,
            showAll: false,
        };
    },
    computed: {
        currency() {
            return window.currency;
        },
        cutOffTimeText() {
            let { available_date, block_out_time } = this.packageInfo;
            let time = block_out_time.split(' ')[1];
            available_date = format(available_date, 'YYYY-MM-DD');
            block_out_time = format(block_out_time, 'YYYY-MM-DD');
            let day = differenceInDays(available_date, block_out_time);
            if (!day && time === '00:00:00') {
                time = '23:59:00';
                day = 1;
            }
            day = day
                ? this.$t('before_travel', [day])
                : this.$t('day_of_travel');
            return `${day} ${time}`;
        },
        availableDate() {
            return this.packageInfo.available_date.substring(0, 10);
        },
    },
    methods: {
        changeDate(data) {
            window.open(
                this.$router.resolve({
                    name: 'payTicket',
                    params: {
                        package_id: data.packageId,
                        sku_id: data.skuId,
                    },
                    query: {
                        date: data.date,
                    },
                }).href,
            );
        },
        formatPrice(value, currencyCode) {
            return `${currencyCode} ${this.formatPriceThousands(value)}`;
        },

        bookingNow(package_id, sku_id) {
            window.open(
                this.$router.resolve({
                    name: 'payTicket',
                    params: {
                        package_id,
                        sku_id,
                    },
                }).href,
            );
        },
    },
};
</script>

<style lang="scss">
.activity-package-index {
    margin-bottom: 20px;
    position: relative;

    .detail-icon {
        color: #4d87e5;
        cursor: pointer;
        transform: rotate(90deg);

        &.up {
            transform: rotate(-90deg);
        }
    }

    .package-title {
        padding: 20px;
        background: #f5f5f5;
        border-radius: 4px;
        font-weight: 600;
        font-size: 16px;
        line-height: 19px;

        .sub-package-name {
            font-weight: normal;
            font-size: 14px;
        }
    }

    .sku-wrapper {
        display: flex;
        align-items: center;
        border: 1px solid #f5f5f5;
        border-radius: 4px;
        margin-top: 8px;

        &:hover,
        &.show-detail {
            background: #f1f6fd;
        }

        .sku-name {
            padding: 0 60px 0 20px;
            flex: 0 0 200px;
            font-size: 16px;
            font-weight: 600;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 2;
        }

        .sku-detail {
            margin: 20px 0;
            flex: 0 0 351px;
            padding: 5px 10px;
            font-size: 12px;
            background: #f1f6fd;
            border-radius: 4px;
            display: flex;
            justify-content: space-between;
            align-items: center;

            .right-content {
                font-size: 14px;
                cursor: pointer;
                color: #4d87e5;
            }
        }

        .flash-sale {
            flex: 0 0 140px;
            padding-left: 48px;

            .flash-sale-icon {
                width: 96px;
                border-radius: 2px;
            }
        }

        .price-content {
            flex: 0 0 236px;
            text-align: right;

            .price {
                margin: 0 4px;
                font-size: 18px;

                del {
                    margin-left: 8px;
                }
            }
        }

        .calendar-content {
            flex: 0 0 72px;
            text-align: right;

            .calendar {
                cursor: pointer;
                height: 40px;
                width: 40px;
            }
        }

        .btn-content {
            padding-right: 6px;
            flex: 1 1 auto;
            text-align: right;
        }
    }

    .show-all {
        color: #4d87e5;
        text-align: center;
        line-height: 24px;
        margin-top: 3px;
        border: 1px solid #f5f5f5;
    }
}
</style>
