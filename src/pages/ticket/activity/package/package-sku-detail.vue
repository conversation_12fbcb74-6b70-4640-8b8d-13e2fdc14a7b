<template>
    <div class="package-sku-detail">
        <div class="triangle-up"></div>
        <div class="feature-detail">
            <feature-icons
                :multi_language_icons="packageDetail.multi_language_icons"
            ></feature-icons>
        </div>
        <template v-if="!packageDetail.use_section">
            <div class="description" v-if="packageOption">
                <klk-section-title size="h4" decorative-line desc-indent>
                    {{ $t('activity.v2.label_more_info') }}
                </klk-section-title>
                <klk-markdown :content="packageOption"></klk-markdown>
            </div>
            <div class="confirmation" v-if="guideLines || confirmationDetail">
                <klk-divider class="divider"></klk-divider>
                <klk-section-title size="h4" decorative-line desc-indent>
                    {{ $t('activity.v2.label.reserve_policy') }}
                </klk-section-title>
                <klk-markdown
                    v-if="guideLines"
                    :content="guideLines"
                ></klk-markdown>
                <klk-markdown
                    v-if="confirmationDetail"
                    :content="confirmationDetail"
                ></klk-markdown>
            </div>
            <div class="how-to-use" v-if="howToUse">
                <klk-divider class="divider"></klk-divider>
                <klk-section-title size="h4" decorative-line desc-indent>
                    {{ $t('activity.v2.label.how_to_use') }}
                </klk-section-title>
                <klk-markdown :content="howToUse"></klk-markdown>
            </div>
            <activity-images :images="packageDetail.images"> </activity-images>
        </template>
        <template v-else>
            <section-render
                :isPackageInfo="true"
                :sections="
                    packageDetail.section_content &&
                        packageDetail.section_content.sections
                "
            ></section-render>
        </template>
    </div>
</template>

<script>
import ActivityImages from '@/pages/tpl/activity-images.vue';
import SectionRender from '@/pages/activity/components/section-render.vue';
import FeatureIcons from '../../../activity/components/feature-icons.vue';
import { replaceLink } from '../activity';

export default {
    name: 'TicketActivityPackageDetail',
    components: {
        FeatureIcons,
        SectionRender,
        ActivityImages,
    },
    props: {
        packageDetail: {
            required: true,
            default: () => {},
        },
    },
    computed: {
        packageOption() {
            return replaceLink(
                this.packageDetail.sub_name
                    ? `${this.packageDetail.sub_name}\n${this.packageDetail.package_option}`
                    : this.packageDetail.package_option,
            );
        },

        howToUse() {
            return replaceLink(this.packageDetail.how_to_use_render_str);
        },
        confirmationDetail() {
            return replaceLink(this.packageDetail.policy);
        },
        guideLines() {
            return replaceLink(
                this.packageDetail.guide_lines.confirmation_details,
            );
        },
    },
};
</script>

<style lang="scss">
.package-sku-detail {
    margin-top: 25px;
    background: #f1f6fd;
    border-radius: 4px;
    padding: 25px;
    position: relative;

    .triangle-up {
        position: absolute;
        top: -10px;
        left: 40px;
        width: 0;
        height: 0;
        border-left: 15px solid transparent;
        border-right: 15px solid transparent;
        border-bottom: 12px solid #f1f6fd;
    }

    .klk-section-title-header {
        margin-bottom: 6px;
    }

    .feature-detail {
        .feature-icons {
            li {
                display: inline-block;
                margin-right: 60px;
                margin-bottom: 20px;
            }
        }
    }

    .divider {
        margin: 20px 0;
    }
}
</style>
