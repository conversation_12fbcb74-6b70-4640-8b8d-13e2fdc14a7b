<template>
    <div class="activity-what-to-expect">
        <klk-section-title
            size="h2"
            class="what-to-expect-title"
            decorative-line
        >
            {{ $t('activity.v2.label.activity_desc') }}
        </klk-section-title>
        <klk-markdown :content="whatWeLove"></klk-markdown>
        <div class="content-activity-image">
            <div
                class="content-activity-image-item"
                v-for="(item, index) in imageData"
                :key="index"
            >
                <div
                    v-if="item.ifLongImage"
                    class="content-activity-long-image-item"
                >
                    <img
                        v-for="(img, index) in item.longIamges"
                        class="activity-img"
                        :src="img.image_url"
                        :key="index"
                    />
                </div>
                <img v-else class="activity-img" :src="item.image_url" />
                <div v-if="item.image_desc" class="desc">
                    <span class="dot"></span>
                    {{ item.image_desc }}
                </div>
            </div>
        </div>
        <template v-if="activityInfo.use_section">
            <section-render
                :sections="
                    activityInfo.section_content &&
                        activityInfo.section_content.sections
                "
            ></section-render>
        </template>
        <!--        地图-->
        <klk-section-title
            size="h2"
            class="what-to-expect-title"
            decorative-line
        >
            {{ $t('location') }}
        </klk-section-title>
        <div class="content-activity-map" id="activityMap">
            <img
                class="image"
                @click="goMap"
                :src="activityInfo.map_box_image_url"
                alt="map"
            />
        </div>
    </div>
</template>

<script>
import SectionRender from '@/pages/activity/components/section-render.vue';
import { handleImages, replaceLink } from './activity';

export default {
    name: 'TicketActivityWhatToExpect',
    components: {
        SectionRender,
    },
    props: {
        activityInfo: {
            required: true,
            default: () => {},
        },
    },
    computed: {
        whatWeLove() {
            return replaceLink(this.activityInfo.what_we_love || '');
        },
        imageData() {
            const images = this.activityInfo.images || [];
            return handleImages(images || []);
        },
    },
    methods: {
        goMap() {
            const address = this.activityInfo.address.split(',');
            window.open(
                `https://maps.google.com/maps?daddr=${address[0]},${address[1]}&amp;ll=?&klookNewPage=true`,
            );
        },
    },
};
</script>

<style lang="scss">
.activity-what-to-expect {
    .what-to-expect-title {
        margin: 40px 0 20px;
    }

    .content-activity-image {
        .content-activity-image-item {
            margin: 30px 0;

            .content-activity-long-image-item {
                font-size: 0;
                img {
                    margin: 0;
                    height: auto;
                }
            }

            .activity-img {
                width: 760px;
            }

            .desc {
                margin-top: 10px;
                display: flex;

                .dot {
                    margin: 8px 10px 0 0;
                    display: inline-block;
                    width: 4px;
                    height: 4px;
                    border-radius: 50%;
                    background: #999999;
                }
            }
        }
    }

    .content-activity-map {
        margin: 20px 0 40px;

        .image {
            cursor: pointer;
            width: 100%;
        }
    }
}
</style>
