import markdown from 'marked';
import { escape, offset, formatLongImagesObj } from '@/pages/activity/util';

const handleImages = (images, width, height) => {
    return images.map(item => {
        const curtWidth = width || item.width;
        const curtHeight = height || item.height;
        const ifLongImageItem = item.images && item.images.length > 0;
        return {
            image_desc: item.image_desc,
            width: curtWidth,
            height: curtHeight,
            image_url: ifLongImageItem
                ? ''
                : handleImageUrl(
                      item.image_url || item.image_url_host,
                      curtWidth,
                      curtHeight,
                  ),
            ifLongImage: ifLongImageItem,
            longIamges: ifLongImageItem
                ? item.images.map(img => formatLongImagesObj(img))
                : null,
        };
    });
};

const handleImageUrl = (
    imgUrl,
    width = 1295,
    height = 720,
    title = '',
    hasWater = true,
) => {
    if (!imgUrl) {
        return '';
    }
    const imgReg = /(.*)(\.jpg|\.jpeg|\.png)$/i;
    const matches = imgReg.exec(imgUrl);
    const imageName = matches ? matches[1] : String(imgUrl).split('.')[0];
    if (!imageName) return '';
    return `${klook.getImageUrl(width, height, hasWater)}${klook.escape(
        imageName,
    )}/${escape(title || arguments[1])}`;
};

const replaceLink = (htmlStr = '') => {
    return markdown(htmlStr)
        .split('</a>')
        .map(str => {
            if (~str.indexOf('<a')) {
                return `${str.replace('<a', '<span')}</span>`;
            }
            return `${str}</a>`;
        })
        .join('');
};

const elmScrollTop = (elId, gap = 30) => {
    // 默认滚动到距离头部30px处
    const elm = document.getElementById(elId);
    if (!elm) return;
    window.scrollTo({
        top:
            offset(elm).top -
            (document.getElementById('headerContainer').clientHeight + gap),
        behavior: 'smooth',
    });
};

export { handleImages, replaceLink, elmScrollTop, handleImageUrl };
