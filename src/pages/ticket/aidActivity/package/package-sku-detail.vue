<template>
    <div class="package-sku-detail">
        <!-- <div class="triangle-up"></div>
        <div class="feature-detail">
            <feature-icons
                :multi_language_icons="packageDetail.multi_language_icons"
            ></feature-icons>
        </div> -->
        <section-render
            :hasGroupName="false"
            :isPackageInfo="true"
            :section-info="packageDetail.section_info"
        ></section-render>
    </div>
</template>

<script>
import SectionRender from '../component/section-info-render.vue';
import FeatureIcons from '../../../activity/components/feature-icons.vue';

export default {
    name: 'TicketActivityPackageDetail',
    components: {
        FeatureIcons,
        SectionRender,
    },
    props: {
        packageDetail: {
            required: true,
            default: () => {},
        },
    },
};
</script>

<style lang="scss">
.package-sku-detail {
    margin-top: 25px;
    background: #f1f6fd;
    border-radius: 4px;
    padding: 25px;
    position: relative;

    .triangle-up {
        position: absolute;
        top: -10px;
        left: 40px;
        width: 0;
        height: 0;
        border-left: 15px solid transparent;
        border-right: 15px solid transparent;
        border-bottom: 12px solid #f1f6fd;
    }

    .klk-section-title-header {
        margin-bottom: 6px;
    }

    .feature-detail {
        .feature-icons {
            li {
                display: inline-block;
                margin-right: 60px;
                margin-bottom: 20px;
            }
        }
    }

    .divider {
        margin: 20px 0;
    }
}
</style>
