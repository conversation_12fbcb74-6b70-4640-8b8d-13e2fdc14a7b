<template>
    <div class="aid-activity-package-index">
        <div class="package-title">
            <span v-if="!isTours">
                {{ packageInfo.package_name }}
            </span>
            <div>
                <span
                    class="sub-package-name"
                    v-for="item in sectionNames"
                    :key="item"
                >
                    {{ item }}
                </span>
            </div>
        </div>

        <div
            v-for="(sku, index) in packageInfo.sku_list || []"
            :key="sku.sku_id"
        >
            <div
                v-show="index < defaultShowNum || showAll"
                class="sku-wrapper"
                :class="{ 'show-detail': showSkuDetails.includes(sku.sku_id) }"
            >
                <div class="sku-name">
                    {{ sku.title }}
                </div>

                <div class="sku-detail">
                    <div class="left-content">
                        <p>
                            <klk-icon
                                size="14"
                                type="icon_time_calendar"
                                color="#36B37E"
                            ></klk-icon>
                            <span class="gray">
                                {{ $t('available_from') }}:
                            </span>
                            <span v-if="hasStocks">
                                {{ sku.available_date }}
                            </span>
                        </p>
                        <p>
                            <klk-icon
                                size="14"
                                type="icon_time_time"
                                color="#FFAB00"
                            ></klk-icon>
                            <span class="gray">
                                {{ $t('cut_off_time') }}:
                            </span>
                            <span v-if="hasStocks">
                                {{ cutOffTimeText }}
                            </span>
                        </p>
                    </div>
                    <div
                        v-if="!isTours"
                        class="right-content"
                        name="ticketActivityReadDetail"
                        @click="toggleSkuDetail(sku.sku_id)"
                    >
                        {{
                            showSkuDetails.includes(sku.sku_id)
                                ? $t('fold_up')
                                : $t('detail')
                        }}
                        <klk-icon
                            :class="{ up: showSkuDetails.includes(sku.sku_id) }"
                            class="detail-icon"
                            size="12"
                            type="detail"
                        ></klk-icon>
                    </div>
                </div>
                <!-- <div class="flash-sale">
                    <div class="flash-sale-icon" v-if="sku.flash_sale">
                        {{ $t('flash_sale') }}
                    </div>
                </div> -->
                <div class="price-content">
                    <p class="price">
                        <span v-html="getPriceText(sku)"></span>
                        <del
                            class="gray"
                            v-if="isMarketPriceGreaterThanSellingPrice(sku)"
                            >{{ sku.market_price }}
                        </del>
                    </p>
                    <!--          MSP-->
                    <package-sku-msp
                        :sku="sku"
                        :activity-info="activityInfo"
                    ></package-sku-msp>
                </div>
                <div class="calendar-content">
                    <!--    价格日历-->
                    <package-sku-calendar
                        :activity-info="activityInfo"
                        :package-id="packageInfo.package_id"
                        :sku-id="sku.sku_id"
                        :reset-date="true"
                        :departure="isTours"
                        @change-date="changeDate"
                    ></package-sku-calendar>
                </div>
                <div class="btn-content">
                    <klk-button
                        :disabled="!hasStocks"
                        name="ticketActivityBookingNow"
                        @click="bookingNow(packageInfo.package_id, sku.sku_id)"
                    >
                        <span v-if="hasStocks"> {{ $t('booking_now') }}</span>
                        <span v-else>
                            {{ $t('activity.v2.btn.sold_out') }}</span
                        >
                    </klk-button>
                </div>
            </div>
            <!--    套餐详情-->
            <package-sku-detail
                :package-detail="packageInfo"
                v-show="showSkuDetails.includes(sku.sku_id)"
            ></package-sku-detail>
        </div>
        <div
            class="show-all"
            v-show="
                packageInfo.sku_list &&
                    packageInfo.sku_list.length > defaultShowNum
            "
            @click="showAll = !showAll"
        >
            {{ showAll ? $t('fold_up') : $t('detail') }}
            <klk-icon
                :class="{ up: showAll }"
                class="detail-icon"
                size="12"
                type="detail"
            ></klk-icon>
        </div>
    </div>
</template>

<script>
import { differenceInDays, format } from 'date-fns';
import PackageSkuCalendar from '@/pages/ticket/activity/package/package-sku-calendar.vue';
import PackageSkuDetail from './package-sku-detail.vue';
import PackageSkuMsp from './package-sku-msp.vue';

export default {
    name: 'AidActivityPackageIndex',
    props: {
        activityInfo: {
            type: Object,
            required: true,
            default: () => {},
        },
        packageInfo: {
            required: true,
            default: () => {},
        },
        showSkuDetails: {
            default: () => [],
        },
    },
    components: {
        PackageSkuDetail,
        PackageSkuCalendar,
        PackageSkuMsp,
    },
    data() {
        return {
            defaultShowNum: 5,
            showAll: false,
        };
    },
    computed: {
        isTours() {
            return (
                this.activityInfo.category_info &&
                this.activityInfo.category_info.sub_category_id === 2
            );
        },
        currency() {
            return this.activityInfo.currency;
        },
        sectionNames() {
            const tags = [];
            const icons = this.packageInfo?.icons || {};
            if (this.packageInfo?.instant === 1) {
                tags.push(icons['confirmation_type']);
            } else {
                tags.push(icons['confirmation_type']);
            }

            if (this.packageInfo?.cancellation_type_multilang) {
                tags.push(this.packageInfo.cancellation_type_multilang);
            }

            if (!this.isTours) {
                if (this.packageInfo?.voucher_usage_multilang) {
                    tags.push(this.packageInfo.voucher_usage_multilang);
                }

                if (this.packageInfo?.is_open_date === true) {
                    tags.push(icons['ticket_type']);
                } else {
                    tags.push(icons['ticket_type']);
                }
            }

            // 去重并过滤空值
            return [...new Set(tags.filter(tag => tag && tag.trim()))];
        },
        hasStocks() {
            return this.packageInfo.has_stocks !== undefined
                ? this.packageInfo.has_stocks
                : this.packageInfo.sku_list &&
                      this.packageInfo.sku_list.length > 0;
        },
        cutOffTimeText() {
            // 从第一个sku获取时间信息
            const firstSku =
                this.packageInfo.sku_list && this.packageInfo.sku_list[0];
            if (
                !firstSku ||
                !firstSku.available_date ||
                !firstSku.block_out_time_utc
            ) {
                return '';
            }

            let { available_date, block_out_time_utc } = firstSku;
            const timezone = this.packageInfo.time_zone;

            // 将UTC时间字符串转换为Date对象
            const blockOutTime = new Date(
                block_out_time_utc.replace(/-/g, '/'),
            );

            const blockOutTimeInTimezone = new Date(
                blockOutTime.toLocaleString('en-US', { timeZone: timezone }),
            );

            // 提取时间部分
            let time = format(blockOutTimeInTimezone, 'HH:mm:ss');

            // 格式化日期为 YYYY-MM-DD
            const availableDate = new Date(available_date.replace(/-/g, '/'));
            const blockOutDate = format(blockOutTimeInTimezone, 'YYYY-MM-DD');

            // 计算天数差异
            let day = differenceInDays(availableDate, new Date(blockOutDate));

            if (!day && time === '00:00:00') {
                time = '23:59:00';
                day = 1;
            }

            day = day
                ? this.$t('before_travel', [day])
                : this.$t('day_of_travel');

            return `${day} ${time}`;
        },
    },
    methods: {
        isMarketPriceGreaterThanSellingPrice(sku) {
            return (
                sku.market_price &&
                +sku.market_price > +(sku.selling_price || 0)
            );
        },
        toggleSkuDetail(skuId) {
            // If this SKU is already open, close it
            if (this.showSkuDetails.includes(skuId)) {
                this.$emit('toggle-sku-detail', skuId);
            } else {
                // Close all SKUs and open only the current one
                const currentOpenSkus = [...this.showSkuDetails];
                currentOpenSkus.forEach(id => {
                    this.$emit('toggle-sku-detail', id);
                });
                this.$emit('toggle-sku-detail', skuId);
            }
        },
        getPriceText(sku) {
            return this.$t('price_from', [
                this.formatPrice(
                    sku.selling_price,
                    sku.currency || this.currency,
                ),
            ]);
        },
        changeDate(data) {
            window.open(
                this.$router.resolve({
                    name: 'payTicket',
                    params: {
                        package_id: data.packageId,
                        sku_id: data.skuId,
                    },
                    query: {
                        date: data.date,
                    },
                }).href,
            );
        },
        formatPrice(value, currencyCode) {
            return `${currencyCode} ${this.formatPriceThousands(value)}`;
        },
        formatPriceThousands(value) {
            if (!value) return '0';
            return value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
        },
        bookingNow(package_id, sku_id) {
            window.open(
                this.$router.resolve({
                    name: 'payTicket',
                    params: {
                        package_id,
                        sku_id,
                    },
                }).href,
            );
        },
    },
};
</script>

<style lang="scss">
.aid-activity-package-index {
    margin-bottom: 20px;
    position: relative;

    .detail-icon {
        color: #4d87e5;
        cursor: pointer;
        transform: rotate(90deg);

        &.up {
            transform: rotate(-90deg);
        }
    }

    .package-title {
        padding: 20px;
        background: #f5f5f5;
        border-radius: 4px;
        font-weight: 600;
        font-size: 16px;
        line-height: 19px;

        .sub-package-name {
            display: inline-block;
            font-weight: normal;
            font-size: 12px;
            margin-top: 8px;
            margin-right: 8px;
            padding: 2px 8px;
            background: #e9e9e9;
            border-radius: 4px;
        }
    }

    .sku-wrapper {
        display: flex;
        align-items: center;
        flex-direction: inherit;
        justify-content: flex-end;
        border: 1px solid #f5f5f5;
        border-radius: 4px;
        margin-top: 8px;

        &:hover,
        &.show-detail {
            background: #f1f6fd;
        }

        .sku-name {
            padding: 0 60px 0 20px;
            flex: 1 1 auto;
            min-width: 200px;
            font-size: 16px;
            font-weight: 600;
            // overflow: hidden;
            // text-overflow: ellipsis;
            // display: -webkit-box;
            // -webkit-box-orient: vertical;
            // -webkit-line-clamp: 2;
        }

        .sku-detail {
            margin: 20px 0;
            flex: 0 0 351px;
            padding: 5px 10px;
            font-size: 12px;
            background: #f1f6fd;
            border-radius: 4px;
            display: flex;
            justify-content: space-between;
            align-items: center;

            .right-content {
                font-size: 14px;
                cursor: pointer;
                color: #4d87e5;
            }
        }

        .flash-sale {
            flex: 0 0 140px;
            padding-left: 48px;

            .flash-sale-icon {
                width: 96px;
                border-radius: 2px;
            }
        }

        .price-content {
            flex: 0 0 236px;
            text-align: right;

            .price {
                margin: 0 4px;
                font-size: 18px;

                del {
                    margin-left: 8px;
                }
            }
        }

        .calendar-content {
            flex: 0 0 72px;
            text-align: right;

            .calendar {
                cursor: pointer;
                height: 40px;
                width: 40px;
            }
        }

        .btn-content {
            padding-right: 6px;
            min-width: max-content;
            text-align: right;
        }
    }

    .show-all {
        color: #4d87e5;
        text-align: center;
        line-height: 24px;
        margin-top: 3px;
        border: 1px solid #f5f5f5;
    }
}
</style>
