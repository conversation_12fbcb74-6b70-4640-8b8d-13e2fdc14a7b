<template>
    <div class="activity-intro">
        <div class="left-wrapper">
            <p class="title">{{ activityInfo.title }}</p>
            <p class="subtitle">
                {{ activityInfo.subtitle }}
            </p>
            <announcement-component
                v-if="activityInfo.klook_insider"
                :klook_insider="activityInfo.klook_insider"
            ></announcement-component>
            <div
                class="icon-wrapper"
                v-for="item in icons"
                :key="item.icon"
                v-show="item.text"
            >
                <span
                    style="cursor: pointer"
                    @click="goMap"
                    v-if="item.icon === 'map'"
                >
                    <klk-icon :type="item.icon" size="20"></klk-icon>
                    {{ item.text }}
                </span>
            </div>
        </div>
        <div class="right-wrapper">
            <span
                class="favorites"
                v-if="isWish"
                @click="activityCollect(false)"
            >
                <klk-icon
                    color="red"
                    type="icon_social_add_to_wishlist_fill"
                    size="16"
                ></klk-icon>
                {{ $t('wish_list_added') }}
            </span>
            <span class="favorites" v-else @click="activityCollect(true)">
                <klk-icon type="icon_social_add_to_wishlist" size="16">
                </klk-icon>
                {{ $t('Add_to_wish_list') }}
            </span>

            <div class="flash-sale" v-if="activityInfo.is_promotion || isEnded">
                <p v-if="!isEnded">{{ $t('flash_sale_within') }}</p>
                <count-down
                    class="flash-sale-time"
                    type="flash_sale"
                    :start-time="activityInfo.start_promotion_time"
                    :end-time="activityInfo.end_promotion_time"
                    :finished-data="$t('promotion_finished_data')"
                    @finishCallback="finishCallback"
                >
                </count-down>
            </div>
        </div>
    </div>
</template>

<script>
import urlObj from '@/common/url';
import CountDown from '@/pages/tpl/countdown.vue';
import announcementComponent from '@/pages/activity/components/activity-announcement.vue';
import { elmScrollTop } from './activity';

export default {
    name: 'AidActivityIntro',
    components: {
        CountDown,
        announcementComponent,
    },
    props: {
        activityInfo: {
            required: true,
            default: () => {},
        },
    },
    data() {
        return {
            isEnded: false,
            localIsWish: false,
            isCollecting: false,
        };
    },
    computed: {
        icons() {
            return [
                {
                    icon: 'icon_time_time',
                    text:
                        this.activityInfo.usages &&
                        this.activityInfo.usages.open_hour,
                },
                {
                    icon: 'map',
                    text: this.activityInfo.address_desc,
                },
            ];
        },
        isWish() {
            return this.localIsWish !== null
                ? this.localIsWish
                : this.activityInfo.is_wish;
        },
    },
    watch: {
        'activityInfo.is_wish'(newVal) {
            this.localIsWish = newVal;
        },
    },
    methods: {
        goMap() {
            elmScrollTop('activityMap');
        },
        finishCallback() {
            this.isEnded = true;
            this.$emit('update-activity');
        },
        activityCollect(flag) {
            if (this.isCollecting) return;
            this.isCollecting = true;
            const url = flag
                ? urlObj.add_activity_to_collection
                : urlObj.remove_activity_from_collection;

            klook.ajaxPost(url, { id: this.activityInfo.activity_id }, resp => {
                this.isCollecting = false;
                if (resp.success) {
                    this.localIsWish = flag;
                }
            });
        },
    },
};
</script>

<style lang="scss">
.activity-intro {
    display: flex;
    margin-top: 20px;
    padding-bottom: 40px;

    .left-wrapper {
        flex: 0 0 883px;
        font-size: 16px;

        .title {
            font-size: 32px;
            font-weight: 700;
            line-height: 1;
        }

        .subtitle {
            margin-top: 12px;
        }

        .icon-wrapper {
            margin-top: 10px;
        }
    }

    .right-wrapper {
        flex: 1 1 auto;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        align-items: flex-end;

        .favorites {
            cursor: pointer;
        }

        .flash-sale {
            font-size: 12px;
            margin-bottom: 8px;

            .flash-sale-time {
                .simplify-time-item {
                    font-size: 22px;
                    font-weight: 700;
                }

                .day {
                    margin-right: 10px;
                }
            }
        }
    }
}
</style>
