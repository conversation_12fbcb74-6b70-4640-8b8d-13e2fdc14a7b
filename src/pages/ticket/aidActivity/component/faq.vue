<template>
    <div class="faq-container" v-if="faq && faq.length">
        <klk-section-title size="h2" class="faq-title" decorative-line>
            {{ $t('FAQ') }}
        </klk-section-title>
        <div class="faq-list">
            <div v-for="(item, index) in faq" :key="index" class="faq-item">
                <div class="faq-question">{{ item.question }}</div>
                <div class="faq-answer">
                    <klk-markdown :content="item.answer"></klk-markdown>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    name: 'ActivityFaq',
    props: {
        faq: {
            type: Array,
            default: () => [],
        },
    },
};
</script>

<style lang="scss" scoped>
.faq-container {
    margin-bottom: 30px;

    .faq-title {
        margin-bottom: 20px;
    }

    .faq-list {
        .faq-item {
            margin-bottom: 20px;

            .faq-question {
                font-weight: bold;
                margin-bottom: 8px;
                color: #333;
            }

            .faq-answer {
                color: #666;
                line-height: 1.5;
            }
        }
    }
}
</style>
