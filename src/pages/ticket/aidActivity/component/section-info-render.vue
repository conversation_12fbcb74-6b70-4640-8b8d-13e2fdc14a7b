<template>
    <div class="section-info-render">
        <div
            v-for="section in filteredSections"
            class="section-container"
            :id="section.section_name"
            :key="section.section_name"
        >
            <klk-section-title size="h2" class="section-title" decorative-line>
                {{ section.section_name }}
            </klk-section-title>
            <div
                v-for="(group, groupIndex) in section.filteredGroups"
                class="group-container"
                :key="groupIndex"
            >
                <div class="group-name" v-if="group.group_name">
                    {{ group.group_name }}
                </div>
                <klk-markdown
                    :content="group.content"
                    v-if="group.content"
                ></klk-markdown>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    name: 'SectionInfoRender',
    props: {
        sectionInfo: {
            type: Array,
            default: () => [],
        },
    },
    computed: {
        filteredSections() {
            if (!this.sectionInfo || !this.sectionInfo.length) {
                return [];
            }

            return this.sectionInfo
                .map(section => {
                    const filteredGroups = (section.groups || []).filter(
                        group =>
                            group &&
                            (group.content ||
                                (group.group_name && group.items?.length)),
                    );

                    if (filteredGroups.length > 0) {
                        return {
                            ...section,
                            filteredGroups,
                        };
                    }

                    return null;
                })
                .filter(section => section !== null);
        },
    },
};
</script>

<style lang="scss">
.section-info-render {
    width: 100%;

    .section-container {
        margin-bottom: 30px;

        .section-title {
            margin-bottom: 20px;
        }
    }

    .group-container {
        margin-bottom: 20px;

        .group-name {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 10px;
            color: #424242;
        }
    }
}
</style>
