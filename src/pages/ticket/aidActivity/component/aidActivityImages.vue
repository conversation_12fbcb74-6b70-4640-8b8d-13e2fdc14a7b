<template>
    <div class="content-activity-image">
        <div
            class="content-activity-image-item"
            v-for="(item, index) in list"
            :key="index"
        >
            <div
                v-if="item.ifLongImage"
                class="content-activity-long-image-item"
            >
                <img
                    v-for="(img, index) in item.longIamges"
                    class="activity-img"
                    :src="img.image_url"
                    :key="index"
                />
            </div>
            <img v-else class="activity-img" :src="item.image_url" />
            <div v-if="item.image_desc" class="desc">
                <span class="dot"></span>
                {{ item.image_desc }}
            </div>
        </div>
    </div>
</template>

<script>
import { processImagesWithGroupId } from '../utils/imageUtils';

export default {
    name: 'AidActivityImages',
    props: {
        images: {
            type: Array,
            required: true,
            default: () => [],
        },
    },
    computed: {
        list() {
            // const filteredImages = (this.images || [])
            //     .filter(item => item.image_type === 'IMAGE')
            //     .map(item => {
            //         // 处理图片URL，添加尺寸和水印参数
            //         const processedImageUrl = this.processImageUrl(
            //             item.image_url_host,
            //             item.width,
            //             item.height,
            //         );

            //         return {
            //             ...item,
            //             image_url: processedImageUrl,
            //             image_desc: item.image_alt,
            //             // 处理长图的情况
            //             ifLongImage: item.images && item.images.length > 0,
            //             longIamges:
            //                 item.images && item.images.length > 0
            //                     ? item.images.map(img =>
            //                           this.processLongImage(img),
            //                       )
            //                     : null,
            //         };
            //     });
            return processImagesWithGroupId(this.images);
        },
    },
    methods: {
        processImageUrl(url, width, height) {
            if (!url) return '';

            // 如果URL已经是完整的CDN URL，则直接处理参数
            if (url.includes('res.klook.com')) {
                // 使用图片自身的宽高，如果没有则使用默认值
                width = width || 760;
                height = height || 0;

                // 提取图片ID
                const urlParts = url.split('/');
                const imageId = urlParts[urlParts.length - 1].split('.')[0];

                // 构建新的URL
                return `https://res.klook.com/images/fl_lossy.progressive,q_65/c_fill,w_${width}${
                    height ? `,h_${height}` : ''
                }/activities/${imageId}`;
            }

            return url;
        },
        processLongImage(img) {
            return {
                ...img,
                image_url: this.processImageUrl(
                    img.image_url || img.image_url_host,
                    img.width,
                    img.height,
                ),
            };
        },
    },
};
</script>

<style lang="scss">
.content-activity-image {
    .content-activity-image-item {
        margin: 30px 0;

        .content-activity-long-image-item {
            font-size: 0;
            img {
                margin: 0;
                height: auto;
            }
        }

        .activity-img {
            width: 760px;
        }

        .desc {
            margin-top: 10px;
            display: flex;

            .dot {
                margin: 8px 10px 0 0;
                display: inline-block;
                width: 4px;
                height: 4px;
                border-radius: 50%;
                background: #999999;
            }
        }
    }
}

.content-activity-map {
    margin: 20px 0 40px;

    .image {
        cursor: pointer;
        width: 100%;
    }
}
</style>
