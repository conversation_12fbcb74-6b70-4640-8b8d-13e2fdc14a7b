/**
 * 图片处理工具函数
 */

/**
 * 处理图片URL，添加尺寸和水印参数
 * @param {string} url - 原始图片URL
 * @param {number} width - 图片宽度
 * @param {number} height - 图片高度
 * @returns {string} 处理后的图片URL
 */
export const processImageUrl = (url, width, height) => {
    if (!url) return '';

    // 如果URL已经是完整的CDN URL
    if (url.includes('res.klook.com')) {
        // 如果URL已经包含处理参数（如c_crop），直接返回
        if (
            url.includes('/c_crop,') ||
            url.includes('/c_fill,') ||
            url.includes('/fl_lossy')
        ) {
            return url;
        }

        // 使用图片自身的宽高，如果没有则使用默认值
        width = width || 760;
        height = height || 0;

        // 提取图片ID（不包含扩展名）
        const urlParts = url.split('/');
        const imageFileName = urlParts[urlParts.length - 1];
        const imageId = imageFileName.split('.')[0];

        // 构建新的URL，保持正确的路径结构
        return `https://res.klook.com/image/upload/fl_lossy.progressive,q_65/c_fill,w_${width}${
            height ? `,h_${height}` : ''
        }/activities/${imageId}.jpg`;
    }

    return url;
};

/**
 * 处理长图中的单张图片
 * @param {object} img - 图片对象
 * @returns {object} 处理后的图片对象
 */
export const processLongImage = img => {
    return {
        ...img,
        image_url: processImageUrl(
            img.image_url || img.image_url_host,
            img.width,
            img.height,
        ),
    };
};

/**
 * 根据group_id处理图片列表，支持分组长图
 * @param {Array} images - 原始图片数组
 * @returns {Array} 处理后的图片数组
 */
export const processImagesWithGroupId = images => {
    const filteredImages = (images || []).filter(
        item => item.image_type === 'IMAGE',
    );

    // 按group_id分组处理
    const groupedImages = {};
    const singleImages = [];

    filteredImages.forEach(item => {
        if (item.group_id && item.group_id.trim() !== '') {
            if (!groupedImages[item.group_id]) {
                groupedImages[item.group_id] = [];
            }
            groupedImages[item.group_id].push(item);
        } else {
            singleImages.push(item);
        }
    });

    const result = [];

    // 处理单图
    singleImages.forEach(item => {
        const processedImageUrl = processImageUrl(
            item.image_url_host,
            item.width,
            item.height,
        );

        result.push({
            ...item,
            image_url: processedImageUrl,
            image_desc: item.image_alt || item.image_desc,
            ifLongImage: false,
            longIamges: null,
        });
    });

    // 处理分组图片（长图）
    Object.keys(groupedImages).forEach(groupId => {
        const groupItems = groupedImages[groupId];
        if (groupItems.length > 0) {
            // 使用第一张图片的基本信息作为长图的主要信息
            const firstItem = groupItems[0];

            result.push({
                ...firstItem,
                image_url: '', // 长图不显示主图URL
                image_desc: firstItem.image_alt || firstItem.image_desc,
                ifLongImage: true,
                longIamges: groupItems.map(img => ({
                    ...img,
                    image_url: processImageUrl(
                        img.image_url_host,
                        img.width,
                        img.height,
                    ),
                    image_desc: img.image_alt || img.image_desc,
                })),
            });
        }
    });

    return result;
};

/**
 * 处理轮播图片，优化加载
 * @param {Array} images - 图片数组
 * @param {number} carouselWidth - 轮播容器宽度
 * @param {number} carouselHeight - 轮播容器高度
 * @returns {Array} 处理后的轮播图片数组
 */
export const processCarouselImages = (
    images,
    carouselWidth = 1290,
    carouselHeight = 460,
) => {
    return (images || []).map(item => {
        return {
            ...item,
            optimized_url: processImageUrl(
                item.image_url_host || item.image_url,
                carouselWidth,
                carouselHeight,
            ),
        };
    });
};
