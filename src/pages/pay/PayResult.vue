<template>
    <div class="pay-result-page">
        <div class="page-main">
            <div class="result-content">
                <div class="result-icon">
                    <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="64"
                        height="64"
                        viewBox="0 0 64 64"
                        v-if="isSuccess"
                    >
                        <path
                            fill="#46B925"
                            fill-rule="nonzero"
                            d="M32 60C16.536 60 4 47.464 4 32S16.536 4 32 4s28 12.536 28 28-12.536 28-28 28zm0-2c14.36 0 26-11.64 26-26S46.36 6 32 6 6 17.64 6 32s11.64 26 26 26zm-3.686-18.24l17.222-17.224a1.25 1.25 0 0 1 1.768 1.768l-18.99 18.99L17.51 32.49a1.25 1.25 0 1 1 1.767-1.767l9.037 9.036z"
                        />
                    </svg>
                    <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="64"
                        height="64"
                        viewBox="0 0 64 64"
                        v-else
                    >
                        <path
                            fill="#e64340"
                            fill-rule="evenodd"
                            d="M32 .75C49.259.75 63.25 14.741 63.25 32S49.259 63.25 32 63.25.75 49.259.75 32 14.741.75 32 .75zm0 2.5C16.122 3.25 3.25 16.122 3.25 32S16.122 60.75 32 60.75 60.75 47.878 60.75 32 47.878 3.25 32 3.25zM32 14a1.5 1.5 0 0 1 1.5 1.5v23a1.5 1.5 0 0 1-3 0v-23A1.5 1.5 0 0 1 32 14zm0 36a2.5 2.5 0 1 1 0-5 2.5 2.5 0 0 1 0 5z"
                        />
                    </svg>
                </div>
                <div
                    :class="['result-message', isSuccess ? 'success' : 'fail']"
                >
                    {{ isSuccess ? $t('pay_complete') : $t('pay_failed') }}
                </div>
                <div class="result-order-info">
                    <p>{{ $t('order_number') }}:{{ bookingNumber }}</p>
                    <!-- <p>{{ $t('booking_date') }}:{{ bookingDate }} </p> -->
                    <p v-if="!isSuccess" class="fail">{{ errorInfo }}</p>
                </div>
                <div class="result-footer" v-if="isSuccess">
                    <span class="view-bookings" @click="viewBookings">{{
                        $t('view_bookings')
                    }}</span>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import urlObj from '@/common/url';
import { TEMPLATE_VERTICAL_MAP } from '../../common/const_data';

export default {
    name: 'PayResultPage',
    data() {
        return {
            // 埋点新增变量
            bookingType: '',
            orderID: '',
            activityIDList: [],
            verticalTypeList: [],
        };
    },
    created() {
        const { orderid } = this.$route.query;
        klook.ajaxGet(urlObj.order_detail, { order_id: orderid }, res => {
            if (res.success && res.result) {
                let result = res.result;
                this.bookingType = result.booking_type;
                this.orderID = result.order_guid;
                result.act_details.map(item => {
                    this.activityIDList.push(item.act_id);
                    this.verticalTypeList.push(
                        TEMPLATE_VERTICAL_MAP[item.template_id],
                    );
                });
            }
        });
    },
    computed: {
        bookingNumber() {
            const { orderid, cm } = this.$route.query;
            let orderId = orderid || cm;
            // return typeof orderId === 'string' ? orderId.substring(0, orderId.indexOf('-') || 10) : '';
            return (orderId + '').substring(0, 10);
        },
        bookingDate() {
            return new Date();
        },
        errorInfo() {
            const { errorMsg, co, st } = this.$route.query;
            if ((st && st === 'Completed') || st === 'Pending') {
                return '';
            }
            return errorMsg || co || '';
        },
        isSuccess() {
            return !this.errorInfo;
        },
    },
    methods: {
        viewBookings() {
            this.$router.push({ name: 'bookings' });
        },
    },
};
</script>

<style lang="scss">
.pay-result-page {
    background: #f5f5f5;
    outline: 9999px solid #f5f5f5;
    clip: 9999px 0 9999px 0;

    .page-main {
        width: 948px;
        background: #fff;
        margin: 100px auto 60px;

        .result-content {
            text-align: center;
            padding: 80px 0;
        }

        .result-message {
            margin: 16px 0 24px;
            line-height: 29px;
            font-size: 24px;
            font-weight: 600;
        }
        .success {
            color: #52c130;
        }
        .fail {
            color: #e64340;
        }

        .result-order-info {
            padding: 20px 0;
            background: #f5f5f5;
            border-radius: 2px;
            font-size: 14px;
            line-height: 22px;
            width: 600px;
            margin: 0 auto;
        }

        .result-footer {
            margin-top: 24px;
        }

        .view-bookings {
            padding: 10px 40px;
            background: var(--primary-color);
            border-radius: 2px;
            font-size: 16px;
            font-weight: 600;
            color: #fff;
            display: inline-block;
            cursor: pointer;
        }
    }
}
</style>
