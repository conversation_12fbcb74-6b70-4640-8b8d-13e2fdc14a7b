import { openOrderCheckoutVertical } from '@/common/verticalStandaloneCheckout';

export default {
    mounted() {
        const {
            order_no,
            settlement_type,
            booking_type,
            entrance,
            upgrade,
            redirect_url,
        } = this.$route.query;
        openOrderCheckoutVertical({
            orderNo: order_no,
            bookingType: booking_type,
            settlementType: settlement_type,
            entrance,
            upgrade,
            middleRedirectUrl: redirect_url,
        });
    },
    render() {
        return <div v-loading="true" style="margin-top:300px;"></div>;
    },
};
