<template>
    <div class="section extrainfo-section" v-if="isSectionVisible">
        <div class="section-header">3.&nbsp;{{ $t('extra_info') }}</div>
        <div class="section-content">
            <div class="extrainfo-list">
                <div
                    v-for="(item, index) in shoppingCartItems"
                    :key="index"
                    v-if="isVisible(item)"
                    class="extrainfo-list-item"
                >
                    <div class="extrainfo-title">
                        <div class="title activity-name">
                            {{ item.activity_name }}
                        </div>
                        <div class="tip package-name">
                            {{ item.package_name }}
                        </div>
                    </div>
                    <div class="extrainfo-general">
                        <div class="extrainfo-general-item">
                            <div class="input-row field-list">
                                <custom-input
                                    v-for="(field,
                                    index) in item.general_other_infos"
                                    :key="index"
                                    :ready-to-pay="readyToPay"
                                    :type-hint="field.type_hint"
                                    v-model="field.content"
                                    select-value-name="value"
                                    select-key-name="key"
                                    :options="getSelectInputOptions(field)"
                                    :type="mapCustomInputType(field.type_flag)"
                                    :required="!!field.required"
                                    :label="field.type_name"
                                    :other-info="field.special_request"
                                    :placeholder="getPlaceholder(field)"
                                >
                                </custom-input>
                            </div>
                        </div>
                    </div>
                    <div class="extrainfo-individual">
                        <div
                            class="extrainfo-individual-item"
                            v-for="(individual,
                            index) in item.individual_other_infos"
                            :key="index"
                        >
                            <div class="title individual-name">
                                {{ $t('pay_traveler') }}{{ index + 1 }}
                            </div>
                            <div class="input-row field-list">
                                <custom-input
                                    v-for="(field,
                                    index) in individual.other_infos"
                                    :key="index"
                                    v-model="field.content"
                                    select-value-name="value"
                                    select-key-name="key"
                                    :ready-to-pay="readyToPay"
                                    :options="getSelectInputOptions(field)"
                                    :type="mapCustomInputType(field.type_flag)"
                                    :required="!!field.required"
                                    :label="field.type_name"
                                    :other-info="field.special_request"
                                    :placeholder="getPlaceholder(field)"
                                >
                                </custom-input>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
/**
 *  根据接口展示动态ui
 *  数据格式：
 * {
        "id": -1,
        "content": "",
        "desc": "",
        "required": true,
        "traveler_no": 0,
        "type_flag": 0,
        "type_hint": "Any special requests?",
        "type_id": 14,
        "type_name": "Dietary Requirements",
        "special_request": {
            "date_range": 0,
            "hint_date": ""
        }
    }
    关于校验问题，目前所有的extarinfo字段只校验是否为空（原因是当前的字段只有ui类型划分，没有字段含义划分），不会有具体的格式校验
 */

import CustomInput from './CustomInput.vue';

const mapInputType = {
    0: 'text',
    1: 'date',
    2: 'select',
    3: 'time',
    5: 'select',
};

export default {
    name: 'ExtraInfoSection',
    props: {
        shoppingCartItems: { type: Array, default: () => [] },
        readyToPay: { type: Boolean, default: false },
    },
    components: {
        CustomInput,
    },
    computed: {
        isSectionVisible() {
            return this.shoppingCartItems.some(this.isVisible);
        },
    },
    methods: {
        mapCustomInputType(flag) {
            return mapInputType[flag];
        },
        getSelectInputOptions(field) {
            let options;
            if (field.type_flag === 5) {
                options = [this.$t('no'), this.$t('yes')];
            } else {
                options =
                    typeof field.type_hint === 'string'
                        ? field.type_hint.split(',')
                        : [];
                if (field.required) options.splice(0, 1);
            }

            return options.map((option, index) => {
                return {
                    key: index.toString(),
                    value: option,
                };
            });
        },
        getPlaceholder(field) {
            return field.type_flag === 0 ? field.type_hint : 'Please Select';
        },
        isVisible(item) {
            return (
                item &&
                ((item.general_other_infos || []).length ||
                    (item.individual_other_infos || []).length)
            );
        },
    },
};
</script>

<style lang="scss">
.activity-name {
    margin-bottom: 8px;
}

.extrainfo-list {
    &-item {
        margin-bottom: 32px;

        &:last-child {
            margin-bottom: 0;
        }

        .extrainfo-title {
            padding: 16px;
            background: #f1f6fd;
            margin-bottom: 24px;
        }

        .individual-name {
            color: var(--primary-color);
            margin-bottom: -16px;
        }

        .extrainfo-individual {
            &-item {
                margin-bottom: 32px;

                &:last-child {
                    margin-bottom: 0;
                }
            }
        }

        .extrainfo-general-item {
            margin: -32px 0 32px;
        }

        .field-list {
            flex-wrap: wrap;

            .extrainfo-desc {
                margin-top: 5px;
                background-color: #f3f3f3;
                line-height: 22px;
                padding: 0 10px;
                max-height: 130px;
                max-width: 560px;
                overflow-y: auto;

                p {
                    margin: 1em 0;
                    word-break: break-word;
                }
            }

            .input-container {
                flex: 1 1 228px;
                margin-top: 32px;
                max-width: 238px;

                &:nth-child(3n) {
                    margin-right: 0;
                }
            }
        }

        .single-date-picker .input-container {
            margin-top: 0;
            margin-right: 0;
        }
    }
}
</style>
