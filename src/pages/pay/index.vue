<template>
    <div>
        <div class="payment-container" v-loading.fixed="isFetching">
            <div class="content-left">
                <div class="section">
                    <div class="section-header">
                        1.&nbsp;{{ $t('travel_agent_info') }}
                    </div>
                    <div class="section-content">
                        <div class="input-row">
                            <div class="input-container input-required">
                                <label>{{ $t('title') }}</label>
                                <klk-select
                                    :disabled="true"
                                    :value="travelAgentInfo.title"
                                >
                                    <klk-option
                                        v-for="item in ['MR', 'MRS']"
                                        :key="item"
                                        :value="item"
                                        :label="item"
                                    >
                                    </klk-option>
                                </klk-select>
                            </div>
                            <div class="input-container input-required">
                                <label>{{ $t('first_name') }}</label>
                                <klk-input
                                    class="input-text"
                                    :value="travelAgentInfo.firstName"
                                    disabled="disabled"
                                >
                                </klk-input>
                            </div>
                            <div class="input-container input-required">
                                <label>{{ $t('family_name') }}</label>
                                <klk-input
                                    class="input-text"
                                    :value="travelAgentInfo.familyName"
                                    disabled="disabled"
                                >
                                </klk-input>
                            </div>
                        </div>
                        <div class="input-row">
                            <div class="input-container input-required">
                                <label>{{ $t('country_code') }}</label>
                                <klk-select
                                    :disabled="true"
                                    :value="travelAgentInfo.country"
                                >
                                    <klk-option
                                        v-for="country in countries"
                                        :key="country.country_code"
                                        :value="country.country_code"
                                        :label="country.country_name"
                                    >
                                    </klk-option>
                                </klk-select>
                            </div>
                            <div class="input-container input-required">
                                <label>{{ $t('mobile_number') }}</label>
                                <klk-input
                                    disabled="disabled"
                                    class="input-text"
                                    :value="travelAgentInfo.mobile"
                                >
                                </klk-input>
                            </div>
                            <div class="input-container input-required">
                                <label>{{ $t('email_address') }}</label>
                                <klk-input
                                    disabled="disabled"
                                    class="input-text"
                                    :value="travelAgentInfo.travellerEmail"
                                >
                                </klk-input>
                            </div>
                        </div>
                    </div>
                </div>
                <traveller-info-section
                    v-if="!isBatchOrder"
                    :traveller-info="travellerInfo"
                    :countries="countries"
                    :ready-to-pay="readyToPay"
                >
                </traveller-info-section>
                <extra-info-section
                    :shopping-cart-items="shoppingCartItems"
                    :ready-to-pay="readyToPay"
                >
                </extra-info-section>
                <div class="pay-now">
                    <span class="btn" @click="genaretaOrder">{{
                        $t('pay_btn')
                    }}</span>
                </div>
            </div>
            <div
                class="content-right"
                v-sucktop="{
                    suck_top: 100,
                    normal_top: 40,
                    reference: '.payment-container .content-left',
                }"
            >
                <div class="carts-content">
                    <div class="carts-list">
                        <div
                            class="carts-list-item"
                            v-for="(item, index) in shoppingCartItems"
                            :key="index"
                        >
                            <div class="activity-title">
                                {{ item.activity_name }}
                            </div>
                            <div class="activity-subtitle">
                                {{ item.package_name }}
                            </div>
                            <div class="activity-info">
                                <div class="activity-info-item">
                                    <div class="item-title">
                                        {{ $t('global.date') }}
                                    </div>
                                    <div class="item-content">
                                        {{ item.selected_time }}
                                    </div>
                                </div>
                                <div class="activity-info-item">
                                    <div class="item-title">
                                        {{ $t('units') }}
                                    </div>
                                    <div class="item-content">
                                        {{ getUnitsInfo(item) }}
                                    </div>
                                </div>
                                <div class="activity-info-item activity-total">
                                    <div class="item-title">
                                        {{ $t('total') }}
                                    </div>
                                    <div class="item-content">
                                        {{
                                            formatPrice(
                                                item.ticket_sell_price,
                                                settlementInfo.currency,
                                            )
                                        }}
                                    </div>
                                    <span
                                        class="item-subcontent"
                                        v-show="getTotalSaving(item) > 0"
                                    >
                                        {{ $t('total_saving') }}:
                                        {{
                                            formatPrice(
                                                getTotalSaving(item),
                                                settlementInfo.currency,
                                            )
                                        }}
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="carts-total">
                        <div class="title">{{ $t('payment_amount') }}</div>
                        <div class="total-price">
                            {{
                                formatPrice(
                                    +settlementInfo.totalPrice,
                                    settlementInfo.currency,
                                )
                            }}
                        </div>
                        <span
                            class="item-subcontent"
                            v-if="settlementInfo.totalSaving > 0"
                        >
                            {{ $t('total_saving') }}:
                            {{
                                formatPrice(
                                    settlementInfo.totalSaving,
                                    settlementInfo.currency,
                                )
                            }}
                        </span>

                        <div class="carts-total-tip">
                            {{ $t('pay_total_tip') }}
                        </div>
                    </div>
                </div>
                <div class="data-security-tip">
                    <p class="title">{{ $t('pay_data_security') }}</p>
                    <p>{{ $t('pay_security_tip1') }}</p>
                    <p>{{ $t('pay_security_tip2') }}</p>
                    <p>
                        <a :href="privacyStatementUrl" target="_blank">{{
                            $t('pay_privacy_statement')
                        }}</a>
                    </p>
                </div>
            </div>
            <div class="page-mask" v-if="showMask !== 0">
                <div class="mask-content">
                    <div class="title" v-show="showMask === 2">
                        {{ $t('payment_failed') }}
                    </div>
                    <div class="mask-content-loading" v-if="showMask === 1">
                        <div style="height:100%" v-loading="true"></div>
                    </div>
                    <div
                        :class="{
                            'mask-content-message': true,
                            'mask-content-message-center': showMask === 1,
                        }"
                    >
                        <div>{{ showMaskMessage }}</div>
                        <div v-if="generateBatchOrderTips">
                            {{ $t('generate_batch_order_tips') }}
                        </div>
                    </div>
                    <div class="mask-footer" v-if="showMask !== 1">
                        <span class="btn-ok" @click="showMask = 0">{{
                            $t('login.ok')
                        }}</span>
                    </div>
                </div>
            </div>
        </div>
        <!-- 提示存在过期促销活动弹窗 -->
        <klk-modal
            :open.sync="showPromotionExpired"
            :title="$t('global.warning')"
            class="show-promotion-expired-modal"
        >
            <p class="show-promotion-expired-modal-container">
                {{
                    $t('promotion_expired_tip', [
                        formatPrice(+oldTotalPrice, settlementInfo.currency),
                        formatPrice(
                            +settlementInfo.totalPrice,
                            settlementInfo.currency,
                        ),
                    ])
                }}
            </p>
            <div class="modal-footer" slot="footer">
                <div @click="showPromotionExpired = false">
                    {{ $t('promotion_expired_tip_back') }}
                </div>
                <div @click="genaretaOrder">
                    {{ $t('promotion_expired_tip_continue') }}
                </div>
            </div>
        </klk-modal>
    </div>
</template>

<script>
import sucktop from '@/directives/sucktop';
import urlObj from '@/common/url';
import { openOrderCheckout } from '@/common/standaloneCheckout';
import { countOriginalSellPrice } from '@/pages/activity/util';
import TravellerInfoSection from './TravellerInfoSection.vue';
import ExtraInfoSection from './ExtraInfoSection.vue';

export default {
    name: 'PayPage',
    directives: { sucktop },
    data() {
        return {
            travelAgentInfo: {},
            travellerInfo: {
                title: 'MR',
                first_name: '',
                family_name: '',
                countryCode: '',
                mobile: '',
                voucher_language: '',
                voucher_to_traveler: false,
                email: '',
                trade_order_number: '',
            },
            settlementInfo: {},
            shoppingCartItems: [],
            savedCards: [],
            readyToPay: false,
            showMask: 0,
            showMaskMessage: false,
            cardInfo: 'balance',
            countries: [],
            isFetching: false,
            showWechatPayDialog: false,
            wechatPayTotal: '',
            wechatPayValidTime: '',
            orderValid: false,
            wechatPayIsAllowPolling: false, // 是否轮询去获取微信支付结果
            setIntervalId: '',
            setTimeOutId: '',
            // 保存刷新前的总价格
            oldTotalPrice: '',
            showPromotionExpired: false,
            gateway: '',
            generateBatchOrderTips: false,
        };
    },
    components: {
        TravellerInfoSection,
        ExtraInfoSection,
    },
    methods: {
        commonPayErrorHandler(message) {
            this.showMask = 2;
            this.showMaskMessage = message;
        },
        fetchSettlementInfo(gateway, updatePrice) {
            let param = {};
            let url = urlObj.booking_settlement; // 普通订单
            if (this.isBatchOrder) {
                //大宗订单
                url = urlObj.batch_booking_settlement;
                param.batch_order_id = +this.$route.query.batchOrderId;
            }
            return new Promise(resolve => {
                klook.ajaxPostJSON(url, param, res => {
                    if (!res.success) {
                        window.alert(res.error && res.error.message);
                        this.$router.push({ name: 'agent_index' });
                        return;
                    }
                    const result = res.result || {};

                    resolve(result);
                    if (!gateway) {
                        this.savedCards = result.credit_cards || [];
                        this.travelAgentInfo = result.pay_contact || {};
                        // 只局部更新sku中的价格
                        if (updatePrice && result.shoppingcart_items) {
                            this.shoppingCartItems = this.updateSkuPrice(
                                result.shoppingcart_items,
                            );
                        } else {
                            this.shoppingCartItems =
                                result.shoppingcart_items || [];
                        }

                        this.isFetching = false;
                    }
                    const {
                        total_price: totalPrice,
                        total_saving: totalSaving,
                    } = result;

                    this.settlementInfo = Object.assign(
                        {},
                        this.settlementInfo,
                        {
                            currency: result.currency,
                            totalPrice,
                            totalSaving,
                        },
                    );
                });
            });
        },
        updateSkuPrice(newItems) {
            return this.shoppingCartItems.map(item => {
                let freshItem = newItems.filter(
                    newItem => item.activity_id === newItem.activity_id,
                );
                if (freshItem.length <= 0) return;

                let {
                    original_sell_price,
                    ticket_market_price,
                    ticket_sell_price,
                    price_items,
                } = freshItem[0];

                item.original_sell_price = original_sell_price;
                item.ticket_market_price = ticket_market_price;
                item.ticket_sell_price = ticket_sell_price;
                item.price_items = price_items;

                return item;
            });
        },

        fetchCountries() {
            klook.ajaxGet(urlObj.agent_account_countries, null, res => {
                if (res.success && res.result) {
                    this.countries = (res.result.main || [])
                        .concat(res.result.others || [])
                        .sort((a, b) =>
                            String(a.country_name).localeCompare(
                                b.country_name,
                            ),
                        );
                }
            });
        },
        formatPrice(price, currencyCode) {
            return +price >= 0
                ? `${this.getCurrencySymbolByCurrencyCode(
                      currencyCode,
                  )} ${klook.formatPriceThousands(price)}`
                : '';
        },
        getUnitsInfo(item) {
            return (item.price_items || [])
                .map(item => `${item.count} x ${item.name}`)
                .join(',');
        },
        getTotalSaving(item) {
            let market_price;
            // 活动促销时：与B端原价比较，B端原价 > 市场价，选择B端原价，否则市场价
            // 活动没促销：按照原来逻辑使用市场价
            market_price = item.is_promotion
                ? countOriginalSellPrice({
                      market_price: item.ticket_market_price,
                      is_promotion: item.is_promotion,
                      original_sell_price: item.original_sell_price,
                  })
                : item.ticket_market_price;

            return klook.accAdd(market_price, -item.ticket_sell_price);
        },
        generateOrderSuccessCallback(result) {
            let order_no = result.order_no;
            if (this.isBatchOrder) {
                order_no = result.order_guid;
            }

            if (
                result.payment_status === 'finished' ||
                +result.payment_status === 4
            ) {
                window.location = `${
                    window.KLK_LANG_PATH
                }/pay/result?orderid=${encodeURIComponent(order_no)}`;
                return;
            }

            openOrderCheckout(order_no);
        },
        generateOrderFailCallback(res) {
            // 存在促销过期的活动，需要重新计算价格
            if (res.error.code.toString() === '01009002001') {
                this.oldTotalPrice = this.settlementInfo.totalPrice;
                this.fetchSettlementInfo('', true).then(() => {
                    this.showMask = 0;
                    this.showPromotionExpired = true;
                });

                return;
            }
            // 提示生成订单失败
            this.commonPayErrorHandler(
                (res.error && res.error.message) || 'Failed to generate order!',
            );

            this.generateBatchOrderTips = false;
        },
        pollingCheckoutBatchOrderStatus() {
            this.generateBatchOrderTips = true;
            const batchOrderId = +this.$route.query.batchOrderId;
            klook.ajaxGet(
                urlObj.check_batch_order_status(batchOrderId),
                {},
                resp => {
                    if (resp.success && resp.result) {
                        const result = resp.result;
                        // order_status: 0 创建中（item和total_price均为空），1 创建成功
                        if (result.order_status === 1) {
                            this.generateOrderSuccessCallback(result);
                        } else {
                            setTimeout(() => {
                                this.pollingCheckoutBatchOrderStatus();
                            }, 1000);
                        }
                    } else {
                        this.generateOrderFailCallback(resp);
                    }
                },
            );
        },
        genaretaOrder() {
            this.readyToPay = true;
            this.showPromotionExpired = false;
            this.$nextTick(() => {
                const el = this.$el.querySelector('.show-error');
                if (el) {
                    el.parentElement.scrollIntoView();
                    return;
                }

                this.showMaskMessage = this.$t('payment_processing_tip');
                this.showMask = 1;

                const { shoppingCartItems, travellerInfo } = this;

                let mobileCountry = (this.countries || []).find(
                    c => c.country_code === travellerInfo.countryCode,
                );
                if (mobileCountry) {
                    mobileCountry = /[(（)]?\+(\d+)[)）]?/.exec(
                        mobileCountry.country_name,
                    );
                    mobileCountry = mobileCountry ? mobileCountry[1] : '';
                }

                const sendData = {
                    payment_info: {
                        user_total_pay: this.settlementInfo.totalPrice,
                    },
                    traveller_info: {
                        ...travellerInfo,
                        mobile: `${mobileCountry}-${travellerInfo.mobile}`,
                    },
                    shoppingcart_ticket_infos: shoppingCartItems.map(item => {
                        const temp = { shoppingcart_id: item.shoppingcart_id };
                        const {
                            general_other_infos,
                            individual_other_infos,
                        } = item;
                        const other_infos = [].concat(
                            ...(general_other_infos || []),
                        );
                        (individual_other_infos || []).forEach(item => {
                            other_infos.push(...item.other_infos);
                        });
                        temp.other_infos = other_infos.map(info => ({
                            content: info.content,
                            traveler_no: info.traveler_no,
                            type_id: info.type_id,
                            id: info.id,
                            version: info.version,
                        }));
                        return temp;
                    }),
                };

                let url;
                let param = {};

                if (!this.isBatchOrder) {
                    // 普通订单
                    url = urlObj.generate_order;
                    param = sendData;
                } else {
                    // 大宗订单
                    url = urlObj.batch_generate_order;
                    param = {
                        batch_order_id: +this.$route.query.batchOrderId,
                        total_price: this.settlementInfo.totalPrice,
                    };
                    param.items = this.shoppingCartItems.map(item => {
                        return {
                            activity_id: item.activity_id,
                            arrangement_id: item.arrangement_id,
                            package_id: item.package_id,
                            ticket_sell_price: item.ticket_sell_price,
                            ticket_market_price: item.ticket_market_price,
                            price_items: (item.price_items || []).map(
                                price_item => {
                                    return {
                                        count: price_item.count,
                                        sku_id: price_item.id,
                                        sell_price: price_item.sell_price,
                                    };
                                },
                            ),
                        };
                    });
                }
                klook.ajaxPostJSON(url, param, res => {
                    if (res.success && res.result) {
                        const result = res.result || {};
                        if (this.isBatchOrder && result.order_status === 0) {
                            this.pollingCheckoutBatchOrderStatus();
                        } else {
                            this.generateOrderSuccessCallback(result);
                        }
                    } else {
                        this.generateOrderFailCallback(res);
                    }
                });
            });
        },
    },
    computed: {
        isBatchOrder() {
            return this.$route.query.batchOrderId !== undefined;
        },
        privacyStatementUrl() {
            return klook.isCNSite
                ? 'https://www.klook.cn/zh-CN/policy/'
                : '/conditions.html';
        },
    },
    created() {
        this.isFetching = true;
        this.fetchCountries();
        this.fetchSettlementInfo('');
    },
};
</script>

<style lang="scss">
.page-mask {
    background: rgba(0, 0, 0, 0.58);
    position: fixed;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    text-align: center;
    z-index: 101;

    &::before {
        content: '';
        display: inline-block;
        height: 100%;
        vertical-align: middle;
    }

    .mask-content {
        background: #fff;
        width: 450px;
        display: inline-block;
        border-radius: 2px;
        vertical-align: middle;
        padding: 24px;
        box-sizing: border-box;
        line-height: 1.5;
        text-align: left;

        .title {
            color: #424242;
            font-weight: 500;
            margin-bottom: 12px;
            font-size: 16px;
        }

        &-message {
            color: #888;
            font-size: 14px;
            word-wrap: break-word;
            max-height: 600px;
            overflow-y: scroll;

            &-center {
                text-align: center;
            }
        }

        &-loading {
            height: 45px;
        }
    }

    .mask-footer {
        text-align: right;
        margin: 24px 0 0;
    }

    .btn-ok {
        padding: 8px 20px;
        background: var(--primary-color);
        border-radius: 2px;
        color: #fff;
        cursor: pointer;
    }
}

.payment-container {
    width: 1160px;
    margin: 40px auto 20px;
    display: flex;
    align-items: flex-start;

    .tips {
        font-size: 0;
        display: inline-block;
        position: relative;
        margin-left: 3px;
        vertical-align: middle;

        svg {
            font-size: 14px;
            color: #999;
        }

        &:hover {
            .tips-content {
                display: block;
            }
        }

        .klk-icon-tips {
            vertical-align: middle;
        }

        &-content {
            // position: absolute;
            width: 300px;
            font-size: 12px;
            border: 1px solid #eee;
            border-radius: 3px;
            padding: 16px;
            z-index: 1;
            background: #fff;
            left: 95%;
            transform: translateX(-50%);
            margin-top: 12px;
            color: #333;
            font-weight: normal;
            box-shadow: 0 4px 12px 0 rgba(0, 0, 0, 0.12);
            display: none;

            &:before {
                position: absolute;
                width: 10px;
                height: 10px;
                border: 1px solid #e0e0e0;
                border-bottom: none;
                border-right: none;
                transform: rotate(45deg);
                content: '';
                top: -6.2px;
                background: #fff;
                z-index: -1;
                left: 50%;
                margin-left: -7px;
            }

            &-header {
                font-weight: 600;
                margin-bottom: 4px;
                line-height: 1.2;
            }

            &-line {
                margin-bottom: 12px;
                line-height: 1.58;

                &:last-child {
                    margin-bottom: 0;
                }
            }
        }
    }

    .content-left {
        flex: 1 1;
        margin-right: 20px;
        max-width: 810px;

        .section {
            border-radius: 0 0 2px 2px;
            background: #fff;
            margin-bottom: 20px;

            &-header {
                background: var(--primary-color);
                border-radius: 2px 2px 0 0;
                height: 48px;
                line-height: 48px;
                overflow: hidden;
                font-size: 18px;
                padding: 0 16px;
                color: #fff;
                font-weight: 600;
            }

            &-content {
                padding: 32px 32px 40px;
            }
        }
    }

    .content-right {
        max-height: 695px;
        overflow-y: scroll;
        width: 330px;
        flex-basis: 330px;
    }

    .pay-now {
        padding: 24px 32px;
        background: #fff;
        display: flex;
        justify-content: flex-end;
        align-items: center;

        .description {
            font-size: 14px;
            line-height: 1.5;
            flex: 1 1;
            margin-right: 150px;

            a {
                text-decoration: underline;
            }
        }

        .btn {
            background: var(--primary-color);
            padding: 0 24px;
            color: #fff;
            border-radius: 2px;
            cursor: pointer;
            line-height: 42px;
            white-space: nowrap;
        }
    }

    .input-row {
        display: flex;
        align-items: flex-start;
        margin-bottom: 32px;

        &:last-child {
            margin-bottom: 0;
        }
    }

    .input-container {
        margin-right: 20px;
        flex: 1 1 232px;

        label {
            display: block;
            font-size: 12px;
            margin-bottom: 8px;
            overflow: hidden;
        }

        input.error {
            border-color: #e64340;
        }

        .input-error {
            font-size: 12px;
            color: #e64340;
            margin-top: 6px;
        }
    }

    .input-container:last-child {
        margin-right: 0;
    }

    .input-required label {
        &::after {
            content: '*';
            color: #e64340;
        }
    }

    .tip {
        font-size: 14px;
        line-height: 16px;
    }

    .carts-content {
        background: #fff;
        border-radius: 2px;
        box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.08);

        .item-subcontent {
            font-size: 12px;
            line-height: 20px;
            background: #f5f5f5;
            display: inline-block;
            margin-top: 6px;
        }

        .carts-list {
            padding: 0 24px;
        }

        .carts-list-item {
            border-bottom: 1px solid #e0e0e0;
            padding: 24px 0;

            &:last-child {
                border-bottom: none;
            }

            .activity-title {
                font-weight: 600;
                font-size: 16px;
            }

            .activity-subtitle {
                font-size: 14px;
                margin: 6px 0 24px;
            }

            .activity-info-item {
                margin-top: 12px;
                font-size: 14px;

                .item-title {
                    color: #999;
                    margin-bottom: 6px;
                }
            }

            .activity-total {
                margin-top: 24px;
            }
        }

        .carts-total {
            border-top: 1px solid #e0e0e0;
            padding: 24px;

            .total-price {
                font-size: 16px;
                color: var(--primary-color);
                font-weight: 600;
                margin: 6px 0 0;
            }

            .handling-fee {
                font-size: 14px;
                color: #333333;
                font-weight: 600;
                margin-top: 8px;
                margin-bottom: 24px;
            }

            &-tip {
                margin-top: 10px;
                line-height: 1.42;

                span {
                    color: var(--primary-color);
                }
            }
        }
    }

    .title {
        font-weight: 600;
        font-size: 14px;
    }

    .data-security-tip {
        padding: 20px;
        background: #ebebeb;
        margin-top: 20px;
        border-radius: 2px;

        .title {
            margin-bottom: 12px;
        }

        p,
        a {
            color: #666;
            font-size: 12px;
            line-height: 1.42 !important;
        }

        p {
            margin-bottom: 8px;
        }

        a {
            text-decoration: underline;
        }
    }

    .arrow-down,
    .arrow-up {
        display: inline-block;
        width: 8px;
        height: 8px;
        border-left: 1px solid #666;
        border-bottom: 1px solid #666;
        transition: transform 0.3s ease-in-out;
    }

    .arrow-down {
        transform: rotate(315deg);
    }

    .arrow-up {
        transform: rotate(135deg);
    }
}

.wechat-pay-dialog {
    width: 1000px;

    .klk_dialog {
        position: relative;
        width: 1000px;

        .header-wrapper {
            margin: 16px 0;
        }

        .pay-wrapper {
            width: 300px;
            margin: 0 auto;

            .title {
                text-align: center;
                font-size: 24px;
            }

            .price {
                text-align: center;
                margin: 16px 0 40px;
                color: #ff5722;
                font-size: 32px;
            }

            .valid-time {
                font-size: 14px;
                margin-bottom: 8px;
            }

            .canvas-wrapper {
                text-align: center;
                border: solid 1px #e0e0e0;
                padding: 20px;

                .canvas {
                    width: 238px !important;
                    height: 238px !important;
                }

                .error-mask {
                    width: 238px;
                    height: 238px;
                    margin: 0 auto;
                    box-sizing: border-box;
                    background-color: #f5f5f5;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                }
            }

            .scan-info {
                padding-top: 10px;
                display: flex;
                align-items: center;

                .scan-tips {
                    vertical-align: bottom;
                    font-size: 14px;
                    line-height: 1.2;
                    color: #424242;
                }

                .scan-icon {
                    vertical-align: top;
                    padding-right: 10px;
                }
            }

            .pay-tips {
                display: flex;
                align-items: center;
                font-size: 12px;
                color: #767676;
                padding: 10px 0 20px;
            }

            .guid-img {
                position: absolute;
                right: 72px;
                top: 123px;
                width: 230px;
                height: 230px;
            }
        }
    }
}

.show-promotion-expired-modal {
    .klk_modal {
        text-align: center;
        width: 325px;

        h3 {
            line-height: 30px;
            font-size: 28px;
            margin: 20px 0 25px 0;
        }

        .modal-footer {
            width: 100%;
            display: flex;
            color: #4d87e5;
            border-top: 1px solid #dedede;
            line-height: 40px;

            & > div {
                flex: 1;
                text-align: center;
                cursor: pointer;

                &:first-child {
                    color: #999;
                    border-right: 1px solid #dddddd;
                }
            }
        }
    }

    &-container {
        color: #999;
        line-height: 1.5;
        font-size: 14px;
        margin: 0 0 25px 0;
        padding: 0 15px;
    }
}
</style>
