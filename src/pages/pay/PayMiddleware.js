import Cookies from 'js-cookie';
import urlObj from '@/common/url';
import { format } from 'date-fns';

export default {
    mounted() {
        // 1: 支付成功 2: 支付失败 3: 用户登陆态失效 4: 收银台用户返回上一页  5: 订单失效
        const { order_no, action_type } = this.$route.query;
        if (+action_type === 1) {
            this.$router.replace({
                name: 'pay_result',
                query: {
                    orderid: order_no,
                },
            });
        }
        if (+action_type === 3) {
            Cookies.remove('cookie1');
            this.$router.replace({
                name: 'signin',
                query: {
                    order_no,
                    referrer: document.referrer,
                },
            });
        }
        if (+action_type === 2 || +action_type === 4 || +action_type === 5) {
            klook.ajaxGet(urlObj.ticket.pay.order_info(order_no), {}, res => {
                const result = res.result;
                if (res.success && result && result.amp_order_process === 1) {
                    const itemInfo = (result.item_info_list || [])[0] || {};
                    const skuInfo = (itemInfo.item_list || [])[0] || {};
                    this.$router.push({
                        name: 'payTicket',
                        params: {
                            package_id: itemInfo.package_id,
                            sku_id: skuInfo.sku_id,
                        },
                        query: {
                            date: format(itemInfo.start_time, 'YYYY-MM-DD'),
                        },
                    });
                } else {
                    let query = {};
                    // 从大宗订单从收银台后退时，需要把batchOrderId带上
                    if (result && +result.batch_order_id > 0) {
                        query.batchOrderId = result.batch_order_id;
                    }
                    this.$router.replace({
                        name: 'pay',
                        query,
                    });
                }
            });
        }
    },
    render() {
        return <div v-loading="true" style="margin-top:300px;"></div>;
    },
};
