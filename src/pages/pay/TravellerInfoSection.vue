<template>
    <div class="section">
        <div class="section-header">2.&nbsp;{{ $t('traveler_info') }}</div>
        <div class="section-content">
            <div class="input-row">
                <custom-input
                    v-model="travellerInfo.title"
                    :label="$t('title')"
                    :required="true"
                    name="title"
                    type="select"
                    :options="['MR', 'MRS', 'MISS']"
                >
                </custom-input>
                <custom-input
                    v-model.trim="travellerInfo.first_name"
                    :label="$t('first_name')"
                    :required="true"
                    :ready-to-pay="readyToPay"
                    placeholder="As shown on passport"
                    name="firstName"
                    type="text"
                >
                </custom-input>
                <custom-input
                    v-model.trim="travellerInfo.family_name"
                    :label="$t('family_name')"
                    :required="true"
                    :ready-to-pay="readyToPay"
                    name="familyName"
                    placeholder="As shown on passport"
                    type="text"
                >
                </custom-input>
            </div>
            <div class="input-row">
                <custom-input
                    v-model="travellerInfo.countryCode"
                    :label="$t('country_code')"
                    :required="true"
                    :ready-to-pay="readyToPay"
                    name="countryCode"
                    select-key-name="country_code"
                    select-value-name="country_name"
                    placeholder="Please Select"
                    :filterable="true"
                    type="select"
                    :options="countries"
                >
                </custom-input>
                <custom-input
                    v-model.trim="travellerInfo.mobile"
                    :label="$t('mobile_number')"
                    :required="true"
                    :ready-to-pay="readyToPay"
                    name="mobileNumber"
                    type="text"
                >
                </custom-input>
                <custom-input
                    v-model="travellerInfo.voucher_language"
                    :label="$t('voucher_language')"
                    :required="true"
                    :ready-to-pay="readyToPay"
                    name="voucherLanguage"
                    type="select"
                    select-key-name="name"
                    select-value-name="label"
                    :options="voucherLanguages"
                >
                </custom-input>
            </div>
            <div class="input-row">
                <custom-input
                    name="emailAddress"
                    v-model.trim="travellerInfo.email"
                    :required="travellerInfo.voucher_to_traveler"
                    :ready-to-pay="readyToPay"
                    :disabled="!travellerInfo.voucher_to_traveler"
                >
                    <template slot="label">
                        <klk-checkbox
                            v-model="travellerInfo.voucher_to_traveler"
                        >
                            {{ $t('email_address') }}
                        </klk-checkbox>
                    </template>
                </custom-input>
                <custom-input
                    v-model="travellerInfo.trade_order_number"
                    :label="$t('agent_booking_ref_no')"
                    name="agentBookingRefNo"
                    :ready-to-pay="readyToPay"
                    type="text"
                >
                </custom-input>
                <div class="input-container"></div>
            </div>
        </div>
    </div>
</template>

<script>
import CustomInput from './CustomInput.vue';

export default {
    name: 'TravellerInfoSection',
    props: ['travellerInfo', 'countries', 'readyToPay'],
    components: {
        CustomInput,
    },
    computed: {
        voucherLanguages() {
            return [
                { name: 'en_US', label: 'English' },
                { name: 'zh_CN', label: '简体中文' },
                { name: 'zh_TW', label: '繁體中文' },
                { name: 'ko_KR', label: '한국어' },
                { name: 'id_ID', label: 'Bahasa Indonesia' },
            ];
        },
    },
};
</script>
