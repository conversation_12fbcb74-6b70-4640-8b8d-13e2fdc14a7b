<template>
    <div class="time-input">
        <klk-select
            width="120"
            :disabled="disabled"
            :value="hour"
            :placeholder="placeholder"
            @change="val => handleInput(val, 'hour')"
        >
            <klk-option
                v-for="(item, index) in hourOptions"
                :key="`${item}${index}`"
                :value="item"
                :label="item"
            >
            </klk-option>
        </klk-select>
        <span class="separator">:</span>
        <klk-select
            width="120"
            :disabled="disabled"
            :value="minute"
            :placeholder="placeholder"
            @change="val => handleInput(val, 'minute')"
        >
            <klk-option
                v-for="(item, index) in minuteOptions"
                :key="`${item}${index}`"
                :value="item"
                :label="item"
            >
            </klk-option>
        </klk-select>
    </div>
</template>

<script>
export default {
    name: 'TimeInputComponent',
    props: ['value', 'disabled', 'placeholder'],
    computed: {
        hour() {
            return typeof this.value === 'string'
                ? this.value.split(':')[0]
                : '';
        },
        minute() {
            return typeof this.value === 'string'
                ? this.value.split(':')[1]
                : '';
        },
        hourOptions() {
            const hourOptions = [];
            for (let i = 0; i < 24; i++) {
                hourOptions.push(i < 10 ? `0${i}` : i.toString());
            }
            return hourOptions;
        },
        minuteOptions() {
            const minuteOptions = [];
            for (let i = 0; i < 12; i++) {
                minuteOptions.push(i * 5 < 10 ? `0${i * 5}` : (i * 5).toString());
            }
            return minuteOptions;
        },
    },
    methods: {
        handleInput(val, type) {
            const value =
                type === 'hour'
                    ? `${val}:${this.minute || '00'}`
                    : `${this.hour || '00'}:${val}`;
            this.$emit('input', value);
        },
    },
};
</script>

<style lang="scss">
.time-input {
    display: flex;
    align-items: center;

    .separator {
        padding: 5px;
    }

    .select-component {
        flex: 1 1;
        .select-list {
            width: 110%;
        }
    }
}
</style>
