<template>
    <div :class="{ 'input-container': true, 'input-required': required }">
        <slot name="label">
            <label>{{ label }} </label>
        </slot>
        <klk-input
            v-if="type === 'text'"
            type="text"
            class="input-text"
            :value="value"
            :placeholder="placeholder"
            @input="handleInput"
            :disabled="disabled"
        >
        </klk-input>
        <template v-else-if="type === 'select'">
            <klk-select
                :disabled="disabled"
                :value="value"
                :placeholder="placeholder"
                :filterable="filterable"
                @change="handleInput"
            >
                <klk-option
                    v-for="option in options"
                    :key="option[selectValueName] || option"
                    :value="option[selectKeyName] || option"
                    :label="option[selectValueName] || option"
                >
                </klk-option>
            </klk-select>

            <div class="extrainfo-desc" v-if="typeHint">
                <p>{{ typeHint }}</p>
            </div>
        </template>

        <time-component
            :disabled="disabled"
            :value="value"
            :placeholder="placeholder"
            @input="handleInput"
            v-else-if="type === 'time'"
        >
        </time-component>
        <klk-single-date-picker
            format="YYYY-MM-DD"
            :placeholder="placeholder"
            :value="value"
            :isDayBlocked="isDayBlocked"
            @input="handleInput"
            v-else-if="type === 'date'"
        >
        </klk-single-date-picker>
        <div
            :class="{ 'input-error': true, 'show-error': showError }"
            v-show="showError"
        >
            {{ errorText }}
        </div>
    </div>
</template>

<script>
import { parse, isValid, isAfter, subDays } from 'date-fns';
import TimeComponent from './TimeInput.vue';

export default {
    name: 'CustomInputComponent',
    props: {
        type: { type: String, default: 'text' },
        required: { type: Boolean, default: false },
        disabled: { type: Boolean, default: false },
        label: { type: String, default: '' },
        options: { type: Array, default: () => [] },
        selectValueName: { type: String, default: '' },
        selectKeyName: { type: String, default: '' },
        placeholder: { type: String, default: '' },
        name: { type: String, default: '' },
        readyToPay: { type: Boolean, default: false },
        otherInfo: { type: Object, default: () => {} },
        value: {},
        typeHint: { type: String, default: '' },
        filterable: { type: Boolean, default: false },
    },
    components: {
        TimeComponent,
    },
    data() {
        return {
            inputed: false,
        };
    },
    computed: {
        showError() {
            let isValid = this.value === 0 ? true : this.value;

            switch (this.name) {
                case 'mobileNumber':
                    isValid = /^\d{6,17}$/.test(this.value);
                    break;
                case 'emailAddress':
                    isValid = klook.checkEmail(this.value);
                    break;
                case 'firstName':
                case 'familyName':
                    isValid = /^[a-zA-Z\s]+$/.test(this.value);
                    break;
            }

            return (
                this.required &&
                !this.disabled &&
                (this.readyToPay || this.inputed) &&
                !isValid
            );
        },
        errorText() {
            switch (this.name) {
                case 'mobileNumber':
                    return this.$t('order_phone_number_validate');
                case 'emailAddress':
                    return this.$t('global_validate_plzEnterValidEmail');
                case 'firstName':
                case 'familyName':
                    return this.$t('global_validate_lettersonly');
            }

            return this.type === 'text'
                ? this.$t('pay_required_text_field')
                : this.$t('pay_required_select_field');
        },
    },
    methods: {
        handleInput(val) {
            this.$emit('input', val);
            this.inputed = true;
        },
        /**
         * date_range: 0 无限制，1 hint_date前，2 hint_date后
         */
        isDayBlocked(day) {
            if (!this.otherInfo.date_range) {
                return false;
            }
            const date = parse(this.otherInfo.hint_date);

            if (!isValid(date)) {
                return false;
            }

            return this.otherInfo.date_range === 1
                ? isAfter(day, subDays(date, 1))
                : isAfter(date, day);
        },
    },
};
</script>
