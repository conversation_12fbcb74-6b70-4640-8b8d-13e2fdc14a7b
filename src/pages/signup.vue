<template lang="html">
    <div class="agent_signup">
        <div class="signup_header">
            <div class="title">{{ $t('agent_sign_up') }}</div>
            <div class="login_potal">
                <span class="tip">
                    {{ $t('signup.label.have_account_and_login') }}
                    <span class="nav_a" @click="goStep">
                        {{ $t('login.login.header') }}
                    </span>
                </span>
            </div>
        </div>
        <!-- step -->
        <div class="step-wrapper">
            <div
                class="step-item-wrapper"
                :class="{ active: currentStep >= step }"
                v-for="step in stepArr"
                :key="step"
            >
                <!-- finished status -->
                <span class="step-item" v-if="currentStep > step">
                    <klk-icon
                        size="30"
                        type="icon_feedback_success_fill"
                        color="var(--primary-color)"
                    ></klk-icon>
                </span>
                <!-- doing status -->
                <span
                    class="step-item dot"
                    v-else-if="currentStep === step"
                ></span>
                <!-- will do status -->
                <span class="step-item text" v-else>
                    {{ step }}
                </span>
            </div>
        </div>
        <router-view class="form"></router-view>
    </div>
</template>

<script>
export default {
    name: 'AgentForgetPwd',
    data() {
        return {
            agreementData: {
                agreeUsageTerm: false,
            },
            passValidation: true,
            stepArr: [1, 2, 3],
        };
    },
    methods: {
        goStep() {
            this.$router.push({
                name: 'signin',
            });
        },
    },
    computed: {
        currentStep() {
            return this.$route.meta.step;
        },
    },
};
</script>

<style lang="scss">
.agent_signup {
    width: 1000px;
    margin: 0 auto;
    margin-top: 90px;
    background: #fff;
    padding: 0 100px 64px;
    margin-bottom: 60px;

    .klk_input_disabled {
        cursor: not-allowed;
    }

    .theme_color_text {
        color: var(--primary-color);
    }
    .sub_title {
        margin-top: 24px;
        border-left: 4px solid var(--primary-color);
        padding-left: 8px;
        margin-bottom: 8px;
        color: #333;
        font-weight: bold;
        font-size: 16px;
    }
    .signup_header {
        line-height: 108px;
        height: 108px;
        border-bottom: 1px solid #e0e0e0;
        .title {
            float: left;
            width: 50%;
            font-size: 24px;
            font-weight: bold;
            color: #333;
        }
        .login_potal {
            float: left;
            width: 50%;
            text-align: right;
            .tip {
                font-size: 14px;
                text-align: left;
                color: #999999;
            }
        }
    }
    .signup_applicant_information_details {
        .first_name {
            width: 238px;
            margin-right: 16px;
        }
        .last_name {
            width: 235px;
        }
        .job_description {
            margin-right: 8px;
        }
    }
    .signup_travel_agent_details {
        .email_address {
            width: 100%;
        }
        .country_or_regional_code {
            margin-right: 8px;
        }
        .pwd {
            width: 314px;
            margin-right: 8px;
        }
        .repeat_pwd {
            width: 314px;
        }
        .language {
            width: 200px;
            margin-right: 16px;
        }
        .currency_code {
            width: 276px;
        }
    }

    .signup_company_details {
        .cooperation_channels {
            .template_wrapper {
                width: 314px;
                float: left;
            }
        }
        .upload_license_form_item {
            display: inline-block;
            width: 205px;
            margin-right: 37px;
        }
        .license_upload_wrapper {
            display: inline-block;
            margin: 6px 0;
            position: relative;
            .upload_area,
            .business_license_img_wrapper {
                width: 168px;
                height: 100px;
                background-color: #fff;
                display: table-cell;
                text-align: center;
                vertical-align: middle;
                color: #b2b2b2;
                font-size: 14px;
                border: solid 1px #e0e0e0;

                cursor: pointer;
                img {
                    width: 168px;
                    height: 100px;
                }
            }
            &:hover {
                .upload_area {
                    color: var(--primary-color);
                    text-decoration: underline;
                    border: solid 1px var(--primary-color);
                }
            }
            .business_license_img_wrapper {
                background: #ccc;
                position: relative;
                overflow: hidden;
                .license_operation_group {
                    height: 26px;
                    background: var(--primary-color);
                    width: 100%;
                    position: absolute;
                    bottom: -26px;
                    transition: 0.3s all;
                    color: #fff;
                    font-size: 12px;
                    .license_operation {
                        text-align: center;
                        line-height: 26px;
                        width: 46%;
                        display: inline-block;
                        &:hover {
                            font-weight: 500;
                            text-decoration: underline;
                        }
                    }
                }
                &:hover {
                    .license_operation_group {
                        bottom: 0;
                    }
                }
            }
        }
        .error {
            .upload_area {
                border: solid 1px #fd5252;
            }
        }
    }
    .signup_business_detail {
        .monthly_sales {
            width: 220px;
            margin-right: 8px;
        }
        .monthly_independent {
            width: 256px;
        }
    }
    .submit_btn {
        margin-top: 16px;
        font-weight: bold;
        border: none;
        width: 240px;
    }

    .step-wrapper {
        margin: 32px 0;
        .step-item-wrapper {
            display: inline-block;
            width: 30px;
            height: 30px;
            position: relative;
            margin-right: 70px;
            vertical-align: middle;

            &:after {
                content: '';
                display: inline-block;
                position: absolute;
                width: 50px;
                height: 2px;
                left: 40px;
                top: 15px;
                background-color: #c0ccda;
            }

            &.active:after {
                background-color: var(--primary-color);
            }

            &:last-child:after {
                display: none;
            }

            .step-item {
                display: block;
                text-align: center;
                line-height: 27px;
                width: 100%;
                height: 100%;
                border-radius: 50%;
                box-sizing: border-box;
                -webkit-box-sizing: border-box;
                color: #c0ccda;

                &.dot,
                &.text {
                    border: solid 2px #c0ccda;
                }
                &.dot {
                    line-height: 30px;
                }

                &.dot:before {
                    content: '';
                    display: inline-block;
                    width: 14px;
                    height: 14px;
                    border-radius: 50%;
                    background-color: var(--primary-color);
                }
            }
        }
    }

    .klk_form {
        width: 540px;
        height: auto;
        border-radius: 3px;
        box-shadow: 0 4px 6px 1px rgba(0, 0, 0, 0.06),
            0 0 1px 1px rgba(0, 0, 0, 0.05);
        padding: 1px 24px 24px;
        margin-bottom: 24px;
        box-sizing: border-box;
        -webkit-box-sizing: border-box;

        .klk-form-item {
            &.klk-form-item-no-required-mark {
                .klk-form-item-label {
                    &::after {
                        content: '*';
                        color: #ff5722;
                        padding-right: 3px;
                    }
                }
            }
            .klk_checkbox {
                margin: 6px 10px 6px 0;
            }
        }

        .signup_administrator_registration .klk-form-item {
            width: 340px;
        }
        .first_name,
        .last_name,
        .monthly_independent,
        .monthly_sales,
        .language,
        .currency_code {
            display: inline-block;
        }
    }
    .form_wrapper {
        width: 100%;
        position: relative;
        .form_tip {
            position: absolute;
            right: 0;
            width: 243px;
            height: auto;
            line-height: 1.3;
            border: solid 1px #d3dce6;
            background-color: #ffffff;
            color: #475669;
            font-size: 16px;
            padding: 16px;
            box-shadow: 0 0 6px 0 rgba(0, 0, 0, 0.04),
                0 2px 4px 0 rgba(0, 0, 0, 0.12);

            &:before {
                content: '';
                display: block;
                position: absolute;
                left: -8px;
                width: 12px;
                height: 12px;
                border-left: solid 1px #d3dce6;
                border-top: solid 1px #d3dce6;
                transform: rotate3d(0, 0, -1, 45deg);
                background-color: #ffffff;
            }

            &.step1_form_tip {
                top: 80px;
            }

            &.company_informatio_form_tip {
                bottom: 70px;
            }

            &.travel_agency_form_tip {
                top: 480px;
            }

            &.currency_form_tip {
                top: 240px;
            }
        }
    }
    .nav_a {
        cursor: pointer;
        color: var(--primary-color);
    }
}
</style>
