<template>
    <div class="customized-header">
        <ul>
            <li v-for="(item, index) in verticalList" :key="index">
                <a
                    :class="{ active: curTab === item.value }"
                    :href="item.url"
                    @click="curTab = item.value"
                >
                    {{ item.label }}
                </a>
            </li>
        </ul>
    </div>
</template>

<script>
export default {
    name: 'CustomizedHeader',
    data() {
        return {
            curTab: 1,
        };
    },
    props: {
        verticalList: {
            type: Array,
            defualt: () => [],
        },
    },
    mounted() {
        const appDom = document.querySelector('#app');
        appDom.style['padding-top'] = '120px';
    },
};
</script>

<style lang="scss">
.customized-header {
    width: 100%;
    height: 60px;
    line-height: 60px;
    padding: 0 30px;
    ul {
        display: flex;
        justify-content: center;
        align-items: center;
        li {
            a {
                padding: 0 15px;
                border-right: 1px solid #999;
                font-weight: 600;
                color: #333;
                &.active {
                    color: var(--primary-color);
                }
            }
            &:first-child a {
                border-left: 1px solid #999;
            }
        }
    }
}
</style>
