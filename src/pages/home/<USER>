<template>
    <div class="carousel">
        <div
            class="list"
            ref="carouselList"
            :style="transformStyle">
            <slot></slot>
        </div>
        <div class="indicator-list">
            <div
                v-for="item in indicatorCounts"
                :key="item"
                :class="{ 'indicator-list-item': true, 'indicator-list-item__active': item - 1 === currentIndex }"
                @click="setActive(item - 1)">
                <slot name="indicator">
                    <div role="button" class="indicator-list-item-btn"></div>
                </slot>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    name: 'CarouselComponent',
    data() {
        return {
            counts: 0,
            currentIndex: 0,
            isAnimation: true
        }
    },
    props: {
        initialIndex: { type: Number, default: 0 },
        autoInterval: { type: Number, default: 4000 }
    },
    computed: {
        transformStyle() {
            return {
                transform: `translate3d(${-(this.currentIndex + 1) * this.width}px, 0px, 0px)`,
                width: `${this.width * this.counts}px`,
                transition: this.isAnimation ? 'transform 500ms ease-in-out 0s' : 'none'
            };
        },
        indicatorCounts() {
            return this.counts > 3 ? this.counts - 2 : 0;
        }
    },
    mounted() {
        this.width = this.$el.offsetWidth;
        const carouselList = this.$refs.carouselList;
        const children = Array.from(carouselList.children);
        children.forEach(item => {
            item.style.display = 'inline-block';
            item.style.width = `${this.width}px`;
            item.style.height= "100%";
        });
        if (children.length <= 1) {
            return ;
        }
        this.counts = children.length + 2;
        carouselList.appendChild(children[0].cloneNode(true));
        carouselList.insertBefore(children[children.length - 1].cloneNode(true), children[0]);
        if (this.autoInterval > 0 && this.counts > 2) {
            this.autoPlay();
        }
        carouselList.addEventListener('transitionend', this.onTransitionEnd);
        this.$el.addEventListener('mouseenter', this.onMouseEnter);
        this.$el.addEventListener('mouseleave', this.onMouseLeave);
    },
    beforeDestroy() {
        this.$refs.carouselList.removeEventListener('transitionend', this.onTransitionEnd);
        this.$el.removeEventListener('mouseenter', this.onMouseEnter);
        this.$el.removeEventListener('mouseleave', this.onMouseLeave);
        clearTimeout(this.timer);
    },
    methods: {
        setActive(index) {
            if (this.isAnimation === false ) {
                this.isAnimation  = true;
            }
            if (index > this.counts - 2) {
                this.currentIndex = this.counts - 2;
            } else if (index < -1) {
                this.currentIndex = -1;
            } else {
                this.currentIndex = index;
            }
        },
        autoPlay() {
            this.timer = setTimeout(() => {
                if (this.autoInterval <= 0 || this.counts <= 3) {
                    clearTimeout(this.timer);
                }
                this.next();
                this.autoPlay();
            }, this.autoInterval);
        },
        next() {
            this.setActive(this.currentIndex + 1);
        },
        prev() {
            this.setActive(this.currentIndex - 1);
        },
        onTransitionEnd() {
            if (this.currentIndex === this.counts - 2) {
                this.isAnimation = false;
                this.currentIndex = 0;
            } else if (this.currentIndex === -1) {
                this.isAnimation = false;
                this.currentIndex = this.counts - 3;
            }
        },
        onMouseEnter() {
            clearTimeout(this.timer);
        },
        onMouseLeave() {
            this.autoPlay();
        }
    }
}
</script>

<style lang="scss">

.carousel {
    overflow: hidden;
    position: relative;

    .list {
        height: 100%;
        transition: transform 500ms ease-in-out 0s;
    }

    .indicator-list {
        text-align: center;
        position: absolute;
        left: 0;
        bottom: 16px;
        text-align: center;
        right: 0;

        &-item {
            display: inline-block;
            padding: 5px 0;
            cursor: pointer;

            &-btn {
                width: 16px;
                height: 2px;
                background: rgba(255, 255, 255, 0.2);
            }

            &:hover .indicator-list-item-btn, &__active .indicator-list-item-btn {
                background: #fff;
            }
        }

    }
}
</style>

