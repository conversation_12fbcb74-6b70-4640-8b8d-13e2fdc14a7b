<template>
    <div class="home-wrapper">
        <div class="home-header">
            <div class="container">
                <carousel :key="carouselDataKey">
                    <router-link
                        target="_blank"
                        v-for="(item, index) in carouselData"
                        :key="index"
                        :class="{
                            'carousel-item': true,
                            'carousel-item-empty': !item.activity_id,
                        }"
                        :to="{
                            name: 'activity_detail',
                            params: { id: item.activity_id },
                        }"
                    >
                        <div class="info" v-if="item.activity_id">
                            <div class="info-title">
                                {{ item.activity_name }}
                            </div>
                            <div class="info-type">
                                <span>{{
                                    $t(
                                        `activity_tag_${String(
                                            item.activity_type,
                                        ).toLowerCase()}`,
                                    )
                                }}</span>
                                <span
                                    v-if="item.is_promotion"
                                    class="info-flash-sale flash-sale-icon"
                                >
                                    {{ $t('flash_sale') }}
                                </span>
                            </div>
                            <div class="info-subtitle">{{ item.words }}</div>
                            <div class="info-price">
                                <span
                                    class="sell-price"
                                    v-html="
                                        $t('price_from', [
                                            formatPrice(
                                                item.sell_price,
                                                item.currency,
                                            ),
                                        ])
                                    "
                                >
                                </span>
                                <del
                                    v-if="+item.market_price > +item.sell_price"
                                >
                                    {{
                                        formatPrice(
                                            item.market_price,
                                            item.currency,
                                        )
                                    }}
                                </del>
                            </div>
                        </div>
                        <div class="carousel-item-empty-title" v-else>
                            {{ item.words }}
                        </div>
                        <img :src="getImageUrl(item.image_url)" />
                    </router-link>
                </carousel>
                <div class="balance-wrapper">
                    <div class="my-balance">
                        <div class="header">
                            <span class="left title">{{
                                $t('my_balance')
                            }}</span>
                            <router-link
                                :to="{
                                    name: 'credits',
                                    query: { topup: true },
                                }"
                                v-if="isMasterAgent"
                                class="right"
                            >
                                {{ $t('topup') }}
                            </router-link>
                        </div>
                        <div class="content">
                            <div
                                class="item"
                                v-for="(item, index) in balanceSummary"
                                :key="index"
                            >
                                <div class="item-title">{{ item.title }}</div>
                                <div class="item-content">{{ item.value }}</div>
                            </div>
                        </div>
                    </div>
                    <div class="my-bookings">
                        <div class="header">
                            <span class="left title">
                                {{ $t('my_bookings') }}
                                <span class="tips">
                                    <klk-poptip
                                        placement="bottom"
                                        :max-height="500"
                                        :width="300"
                                    >
                                        <div
                                            class="tips-content"
                                            slot="content"
                                        >
                                            <div class="tips-content-line">
                                                <div
                                                    class="tips-content-header"
                                                >
                                                    {{ $t('monthly') }}
                                                </div>
                                                {{
                                                    $t(
                                                        'my_bookings_monthly_desc_pop_content',
                                                    )
                                                }}
                                            </div>
                                            <div class="tips-content-line">
                                                <div
                                                    class="tips-content-header"
                                                >
                                                    {{ $t('total') }}
                                                </div>
                                                {{
                                                    $t(
                                                        'my_bookings_total_desc_pop_content',
                                                    )
                                                }}
                                            </div>
                                        </div>
                                        <klk-icon
                                            type="icon_tips_tips"
                                            size="14"
                                        ></klk-icon>
                                    </klk-poptip>
                                </span>
                            </span>
                            <router-link
                                :to="{ name: 'bookings' }"
                                class="right"
                            >
                                {{ $t('theme.see_more') }}
                            </router-link>
                        </div>
                        <div class="content">
                            <div
                                class="item"
                                v-for="(item, index) in bookingSummary"
                                :key="index"
                            >
                                <div class="item-title">{{ item.title }}</div>
                                <div class="item-content">{{ item.value }}</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="home-content">
            <div class="container">
                <div class="main">
                    <div class="card-list" v-loading="isFetchingTrending">
                        <!-- add promotion list -->
                        <promotion-list
                            pageType="HomePage"
                            @flash-sale-finished="loadData"
                        ></promotion-list>
                        <div class="main-title">{{ $t('trending_now') }}</div>
                        <div
                            class="card-list-container"
                            v-if="trendingNow.length"
                        >
                            <activity-card
                                :class="{ disabled_card: activity.is_blocked }"
                                v-for="(activity, index) in trendingNow"
                                :key="index"
                                @clickWish="clickWish(activity.is_blocked)"
                                :activity="activity"
                            ></activity-card>
                        </div>
                        <no-data
                            v-if="!trendingNow.length && !isFetchingTrending"
                            class="card-list-empty"
                            no_data_text_prop="No trending Activitiy now!"
                        >
                        </no-data>
                    </div>
                    <div class="recent-booked">
                        <div class="main-title">
                            {{ $t('recently_booked') }}
                        </div>
                        <div class="list">
                            <router-link
                                target="_blank"
                                v-for="(item, index) in recentBooked"
                                :key="index"
                                class="list-item"
                                :to="{
                                    name: 'activity_detail',
                                    params: { id: item.id },
                                }"
                            >
                                <div class="list-item-title">
                                    {{ item.title }}
                                </div>
                                <div
                                    class="list-item-extra"
                                    v-if="item.subtitle"
                                >
                                    {{ item.subtitle }}
                                </div>
                                <div
                                    class="list-item-original-price"
                                    v-if="+item.market_price > +item.sell_price"
                                >
                                    <del
                                        >{{
                                            formatPrice(
                                                item.market_price,
                                                item.currency,
                                            )
                                        }}
                                    </del>
                                </div>
                                <div class="list-item-discount-price">
                                    <span
                                        v-html="
                                            $t('price_from', [
                                                formatPrice(
                                                    item.sell_price,
                                                    item.currency,
                                                ),
                                            ])
                                        "
                                    >
                                    </span>
                                </div>
                                <div
                                    :class="[
                                        'list-item-avaliable',
                                        `list-item-avaliable-${item.startDate[0]}`,
                                    ]"
                                >
                                    {{ item.startDate[1] }}
                                </div>
                                <span
                                    v-if="item.is_promotion"
                                    class="list-item-flash-sale flash-sale-icon"
                                >
                                    {{ $t('flash_sale') }}
                                </span>
                            </router-link>
                            <div class="list-empty" v-if="!recentBooked.length">
                                {{ $t('no_recented_buy') }}
                            </div>
                        </div>
                        <div class="freeze-tip">
                            {{ $t('account_inactive_tip') }}
                        </div>
                        <div class="contact">
                            <div>{{ $t('help_tip') }}</div>
                            <a
                                class="contact-email"
                                :href="`mailto:${accountEmail}`"
                            >
                                <klk-icon
                                    type="icon_communication_email"
                                    size="16"
                                ></klk-icon>
                                {{ $t('contact_account_manager') }}</a
                            >
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import ActivityCard from '@/pages/tpl/ActivityCard.vue';
import NoData from '@/components/no_data/NoData.vue';
import urlObj from '@/common/url';
import { filterBlockData } from '@/common/util';
import { isToday, isTomorrow, format } from 'date-fns';
import { countOriginalSellPrice } from '@/pages/activity/util';
import CountDown from '@/pages/tpl/countdown.vue';
import PromotionList from '@/pages/tpl/promotion_list.vue';
import { acceptLanguage } from '@/common/const_data';
import Carousel from './Carousel.vue';

export default {
    name: 'HomePage',
    data() {
        return {
            carouselData: [],
            trendingNow: [],
            recentBooked: [],
            bookingSummary: {},
            balanceSummary: [],
            isFetchingTrending: false,
            routerMap: {},
            promotionObj: {
                isFinished: true,
                isPromotion: false,
                startTime: '',
                endTime: '',
                list: [],
            },
        };
    },
    components: {
        Carousel,
        ActivityCard,
        CountDown,
        PromotionList,
        NoData,
    },
    created() {
        this.fetchBalanceData();
        this.loadData();
    },
    computed: {
        isMasterAgent() {
            return (window.KLK_USER_INFO || {}).agent_type === 0;
        },
        carouselDataKey() {
            return (
                this.carouselData.map(item => item.activity_id).join(',') ||
                'default'
            );
        },
        accountEmail() {
            return window.KLK_JOINT_VENTRUES.contact_email || '';
        },
    },
    methods: {
        filterBlockData,
        loadData() {
            this.initBookedActivities();
            this.initBookingSummary();
            this.fetchMarketData('ads', 'carouselData', activities => {
                activities.forEach(activity => {
                    if (activity.is_promotion) {
                        activity.market_price = countOriginalSellPrice(
                            activity,
                        );
                    }
                });
                if (activities.length === 0) {
                    return [
                        {
                            image_url:
                                'https://res.klook.com/image/upload/fl_lossy.progressive,q_90/c_fill/v1543221847/Agent_banner_jizhst.jpg',
                            words: this.$t('signup_cooperation_platform'),
                        },
                    ];
                }
                return activities;
            });
            this.fetchMarketData('trending', 'trendingNow', activities => {
                return this.formatActivities(activities);
            });
        },
        formatActivities(activities) {
            return activities.map(activity => {
                activity.currencySymbol = this.getCurrencySymbolByCurrencyCode(
                    activity.currency,
                );
                activity.title = activity.activity_name || activity.title; // will do
                activity.showWords = true;
                activity.showDate = true;
                activity.activity_type = activity.activity_type || '';
                activity.id = activity.activity_id || activity.id; // will do
                return activity;
            });
        },
        fetchMarketData(type, fieldName, cb) {
            if (fieldName === 'trendingNow') {
                this.isFetchingTrending = true;
            }

            const category = String(
                (window.KLK_USER_INFO || {}).agent_category,
            ).toLowerCase();

            klook.ajaxGet(
                urlObj.agent_marketing_view(type),
                { category },
                res => {
                    if (fieldName === 'trendingNow') {
                        this.isFetchingTrending = false;
                    }
                    if (res.success && Array.isArray(res.result)) {
                        res.result.sort((a, b) => b.seq_no - a.seq_no);
                    }
                    // 过滤is_block
                    const _data = res.success
                        ? this.filterBlockData(res.result || [])
                        : [];
                    this[fieldName] =
                        typeof cb === 'function' ? cb(_data) : _data;
                },
                acceptLanguage,
            );
        },
        clickWish(is_blocked) {
            if (is_blocked) {
                return;
            }
        },
        initBookedActivities() {
            klook.ajaxGet(urlObj.agent_home_booked_activities, null, res => {
                if (res.success && res.result) {
                    const items = this.filterBlockData(res.result || []);
                    this.recentBooked = items.map(activity => {
                        activity.startDate = this.getAvailableDay(
                            activity.start_time,
                        );
                        if (activity.is_promotion) {
                            activity.market_price = countOriginalSellPrice(
                                activity,
                            );
                        }
                        return activity;
                    });
                }
            });
        },
        initBookingSummary() {
            klook.ajaxGet(urlObj.agent_home_booking_summary, null, res => {
                if (res && res.success && res.result) {
                    console.log(res.result);
                    const {
                        currency,
                        yesterday_amount,
                        yesterday_bookings,
                        last_seven_days_total_amount,
                        last_seven_days_total_bookings,
                    } = res.result || {};
                    const currencySymbol = this.getCurrencySymbolByCurrencyCode(
                        currency,
                    );
                    this.bookingSummary = [
                        {
                            title: this.$t('yesterday_amount'),
                            value: `${currencySymbol} ${klook.formatPriceThousands(
                                yesterday_amount,
                            )}`,
                        },
                        {
                            title: this.$t('yesterday_bookings'),
                            value: `${klook.formatPriceThousands(
                                yesterday_bookings,
                            )}`,
                        },
                        {
                            title: this.$t('last_seven_days_total_amount'), //total_amount_of_history
                            value: `${currencySymbol} ${klook.formatPriceThousands(
                                last_seven_days_total_amount,
                            )}`,
                        },
                        {
                            title: this.$t('last_seven_days_total_bookings'), //total_bookings
                            value: `${klook.formatPriceThousands(
                                last_seven_days_total_bookings,
                            )}`,
                        },
                    ];
                }
            });
        },
        fetchBalanceData() {
            const { permissions, agent_type } = window.KLK_USER_INFO || {};

            if (
                agent_type === 0 ||
                (permissions && permissions.balance_lookup)
            ) {
                klook.ajaxGet(urlObj.agent_account_balance, null, res => {
                    if (res.success && res.result) {
                        const {
                            availableBalance,
                            creditLimit,
                            settlementDue,
                            currencyCode,
                        } = res.result || {};
                        const currencySymbol = this.getCurrencySymbolByCurrencyCode(
                            currencyCode,
                        );
                        this.balanceSummary = [
                            {
                                title: this.$t('available_balance'),
                                value: `${currencySymbol} ${klook.formatPriceThousands(
                                    availableBalance,
                                )}`,
                            },
                            {
                                title: this.$t('credit_limit'),
                                value: `${currencySymbol} ${klook.formatPriceThousands(
                                    creditLimit,
                                )}`,
                            },
                            {
                                title: this.$t('settlement_due'),
                                value: `${currencySymbol} ${klook.formatPriceThousands(
                                    settlementDue,
                                )}`,
                            },
                        ];
                    }
                });
            } else {
                this.balanceSummary = [
                    { title: this.$t('available_balance'), value: '-' },
                    { title: this.$t('credit_limit'), value: '-' },
                    { title: this.$t('settlement_due'), value: '-' },
                ];
            }
        },
        formatPrice(value, currencyCode) {
            return `${this.getCurrencySymbolByCurrencyCode(
                currencyCode,
            )} ${klook.formatPriceThousands(value)}`;
        },
        getAvailableDay(date) {
            if (isToday(date)) {
                return ['green', this.$t('activity.v2.label.today_available')];
            }

            if (isTomorrow(date)) {
                return ['green', this.$t('book.tomorrow')];
            }
            return date
                ? [
                      'gray',
                      `${this.$t(
                          'activity.v2.label.earliest_available',
                      )}${format(date, 'YYYY-MM-DD')}`,
                  ]
                : ['', ''];
        },
        getImageUrl(imageUrl) {
            if (/^https?/.test(imageUrl)) {
                return imageUrl;
            }
            return `https://res.klook.com/image/upload/fl_lossy.progressive,q_90/c_fill,w_1920,h_563/activities/${imageUrl}`;
        },
    },
};
</script>

<style lang="scss">
.home-wrapper {
    min-width: 1200px;

    .container {
        width: 1160px;
        margin: 0 auto;
    }

    .left {
        float: left;
    }

    .right {
        float: right;
    }

    .tips {
        font-size: 0;
        display: inline-block;
        margin-left: 3px;
        vertical-align: middle;

        .tips-content {
            font-size: 12px;

            .tips-content-line {
                margin-bottom: 12px;
                line-height: 1.58;
            }

            .tips-content-header {
                font-weight: 600;
                margin-bottom: 4px;
                line-height: 1.2;
            }
        }
    }

    .home-header {
        background: #fff;
        padding: 20px 0 28px;

        .carousel {
            height: 340px;
            border-radius: 2px;
            line-height: 1.2;

            &-item {
                position: relative;
                display: block;

                img {
                    width: 1160px;
                    height: 340px;
                }

                .info {
                    position: absolute;
                    left: 16px;
                    top: 16px;
                    bottom: 16px;
                    background-color: rgba(0, 0, 0, 0.4);
                    width: 300px;
                    padding: 20px;
                    color: #fff;

                    &-title {
                        font-size: 28px;
                        font-weight: bold;
                        max-height: 134.4px;
                        overflow: hidden;
                        line-height: 1.2;
                        display: -webkit-box;
                        -webkit-line-clamp: 4;
                        -webkit-box-orient: vertical;
                        text-overflow: ellipsis;
                    }

                    &-type {
                        margin: 20px 0 6px;

                        span {
                            display: inline-block;
                            padding: 4px 6px;
                            border-radius: 2px;
                            background-color: #ff5722;
                            font-size: 14px;
                            font-weight: bold;
                            margin-right: 5px;
                        }
                    }

                    &-subtitle {
                        font-size: 16px;
                        word-break: break-word;
                        font-weight: 500;
                    }

                    &-price {
                        margin-top: 4px;
                        color: #ccc;
                    }

                    &-flash-sale {
                        background-color: transparent !important;
                        height: 24px;
                        line-height: inherit;
                    }

                    .sell-price {
                        font-size: 22px;
                        font-weight: bold;
                        color: #fff;
                    }
                }

                &-empty {
                    cursor: default;
                }

                &-empty-title {
                    font-size: 48px;
                    font-weight: bold;
                    color: #fff;
                    position: absolute;
                    text-align: center;
                    width: 100%;
                    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
                    top: 141px;
                }
            }
        }

        .balance-wrapper {
            margin-top: 28px;
            color: #333;
            font-size: 14px;

            .my-balance {
                padding-right: 40px;
                border-right: 1px solid #f5f5f5;
                display: inline-block;
                width: 42.8%;
                box-sizing: border-box;
                vertical-align: top;
            }

            .my-bookings {
                padding-left: 40px;
                display: inline-block;
                width: 57.2%;
                box-sizing: border-box;
                vertical-align: top;
            }

            .header {
                margin-bottom: 12px;
                line-height: 1.2;
                display: flex;
                justify-content: space-between;

                .title {
                    font-weight: 600;
                }

                .klk_poptip {
                    vertical-align: middle;
                    color: #333;
                }
            }

            a:link,
            a:hover,
            a:active,
            a:visited {
                color: var(--primary-color);
                font-size: 14px;
                font-weight: 600;
            }

            a:hover,
            a:active,
            a:visited {
                text-decoration: underline;
            }

            .content {
                display: flex;

                .item {
                    padding-right: 20px;
                    flex: 1;
                    min-width: 0;

                    &:last-child {
                        padding-right: 0;
                    }
                }

                .item-title {
                    margin-bottom: 7px;
                    color: #666;
                    font-size: 12px;
                }

                .item-content {
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                }
            }
        }
    }

    .home-content {
        background: #f9f9f9;
        padding-top: 36px;
        padding-bottom: 30px;

        a:link,
        a:hover,
        a:active,
        a:visited {
            color: var(--primary-color);
            font-size: 14px;
            font-weight: 600;
        }

        .main {
            display: flex;
            flex-direction: row;

            &-title {
                font-size: 20px;
                line-height: 24px;
                color: #333;
                font-weight: 600;
                margin-bottom: 24px;
                background: transparent;
            }

            .card-list {
                flex: 1 1;

                &-container {
                    display: flex;
                    flex-wrap: wrap;
                }

                .activity-card {
                    width: 275px;
                    margin: 0 20px 20px 0;
                    background: #fff;

                    &:nth-child(3n + 3) {
                        margin-right: 0;
                    }

                    .image-wrapper {
                        height: 154px;
                    }

                    .card-content {
                        height: 142px;
                    }
                }
            }

            .promotion-wrapper {
                margin-bottom: 20px;
            }

            .recent-booked {
                width: 275px;
                margin-left: 20px;

                .list,
                .contact,
                .freeze-tip {
                    border-radius: 2px;
                    border-radius: 2px;
                    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.06);
                    background: #fff;
                }

                .list {
                    border-top: 2px solid var(--primary-color);
                    background: #fff;

                    &-empty {
                        padding: 14px 16px 16px;
                        color: #333;
                        font-weight: 500;
                    }

                    &-item {
                        padding: 16px;
                        cursor: pointer;
                        display: block;
                        position: relative;

                        &:hover {
                            background: #fafafa;
                        }

                        &-title {
                            color: #333;
                            max-height: 32px;
                            font-size: 14px;
                            line-height: 16px;
                            overflow: hidden;
                            text-overflow: ellipsis;
                            display: -webkit-box;
                            -webkit-box-orient: vertical;
                            -webkit-line-clamp: 2;
                            margin-bottom: 6px;
                        }

                        &-extra,
                        &-original-price,
                        &-discount-price,
                        &-avaliable {
                            height: 14px;
                            line-height: 14px;
                            font-size: 12px;
                            overflow: hidden;
                            white-space: nowrap;
                            text-overflow: ellipsis;
                        }

                        &-flash-sale {
                            position: absolute;
                            right: 10px;
                            bottom: 30px;
                            transform: scale(0.8);
                            -webkit-transform: scale(0.8);
                        }

                        &-extra {
                            color: #666;
                            margin-bottom: 12px;
                        }

                        &-original-price {
                            color: #999;
                        }

                        &-discount-price {
                            font-size: 14px;
                            line-height: 16px;
                            height: 16px;
                            color: #333;
                            margin-bottom: 6px;
                        }

                        &-avaliable {
                            color: #999;

                            &-green {
                                color: #24b985;
                            }
                        }
                    }
                }

                .contact {
                    font-size: 12px;
                    line-height: 1.42;
                    color: #333;

                    & > div {
                        padding: 12px 16px;
                    }

                    &-email {
                        color: #666;
                        box-shadow: inset 0 1px 0 0 #eeeeee;
                        display: block;
                        padding: 12px 16px;

                        svg {
                            fill: #666;
                            vertical-align: middle;
                            margin-right: 6px;
                            color: #666;
                        }

                        &:hover,
                        &:hover svg {
                            color: var(--primary-color);
                        }
                    }
                }

                .freeze-tip {
                    line-height: 1.42;
                    padding: 12px 16px;
                    font-size: 12px;
                    margin: 20px 0 10px;
                }
            }
        }
    }

    .disabled_card {
        .card-content {
            color: grey;
            cursor: not-allowed;

            .card-content-title,
            .sell-price,
            .klk-icon-fast {
                color: grey;
            }
        }

        .image-wrapper {
            cursor: not-allowed;
        }
    }
}
</style>
