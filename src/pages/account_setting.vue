<template lang="html">
    <div class="account_setting">
        <div class="title">{{ $t('settings') }}</div>
        <div class="content">
            <div class="action_title">{{ $t('change_password') }}</div>
            <div class="pwd_tip">{{ $t('global_password_validate') }}</div>
            <klk-form :model="form" :rules="rules" ref="form">
                <klk-form-item
                    :label="$t('current_password')"
                    prop="presentPwd"
                >
                    <klk-input type="password" v-model="form.presentPwd">
                    </klk-input>
                </klk-form-item>
                <klk-form-item :label="$t('new_password')" prop="pwd">
                    <klk-input type="password" v-model="form.pwd"> </klk-input>
                </klk-form-item>
                <klk-form-item :label="$t('confirm_password')" prop="repeatPwd">
                    <klk-input type="password" v-model="form.repeatPwd">
                    </klk-input>
                </klk-form-item>
            </klk-form>
            <klk-button class="agent_btn_base" @click="submitForm('form')"
                >{{ $t('save') }}
            </klk-button>
        </div>
    </div>
</template>

<script>
import Cookies from 'js-cookie';
import urlObj from '@/common/url';

export default {
    name: 'Home',
    data() {
        const validatePwd = (rule, value, callback) => {
            if (!klook.checkPwd(value)) {
                callback(new Error(this.$t('global_password_validate')));
            }
            callback();
        };

        const validateRepeatPwd = (rule, value, callback) => {
            if (value !== '' && value !== this.form.pwd) {
                callback(new Error(this.$t('global_password_not_match')));
            } else {
                callback();
            }
        };

        return {
            form: {
                presentPwd: '',
                pwd: '',
                repeatPwd: '',
            },
            errorMsgObj: {
                presentPwd: '',
                pwd: '',
                repeatPwd: '',
            },
            rules: {
                presentPwd: [
                    {
                        required: true,
                        message: this.$t('cannot_be_empty'),
                        trigger: 'blur',
                    },
                ],
                pwd: [
                    {
                        required: true,
                        message: this.$t('cannot_be_empty'),
                        trigger: 'blur',
                    },
                    { validator: validatePwd, trigger: 'blur' },
                ],
                repeatPwd: [
                    {
                        required: true,
                        message: this.$t('cannot_be_empty'),
                        trigger: 'blur',
                    },
                    { validator: validateRepeatPwd, trigger: 'blur' },
                ],
            },
        };
    },
    methods: {
        showDialogSuccess(tip) {
            this.$alert(tip || 'success').then(({ result }) => {
                if (result) {
                    this.goToLogin();
                }
            });
        },
        showDialogFail(tip) {
            this.$message({
                type: 'error',
                message: tip || 'failed',
            });
        },
        submitForm(formName) {
            this.$refs[formName].validate(valid => {
                if (valid) {
                    this.resetAccountPwd();
                } else {
                    return false;
                }
            });
        },
        resetAccountPwd() {
            klook.ajaxPostJSON(
                urlObj.agent_account_reset_password,
                {
                    currentPassword: klook.md5(this.form.presentPwd),
                    newPassword: klook.md5(this.form.pwd),
                },
                resp => {
                    if (resp.success) {
                        this.showDialogSuccess(
                            this.$t('submit') + ' ' + this.$t('success'),
                        );
                        this.presentPwd = '';
                        this.pwd = '';
                        this.repeatPwd = '';
                        if (Cookies.get('cookie1')) {
                            Cookies.remove('cookie1');
                        }
                    } else {
                        // 重置密码错误 (旧密码错误)
                        // ERR0009 string = `030009`
                        if (resp.error.code === '030009') {
                            this.showDialogFail(
                                this.$t('old_password_incorrect'),
                            );
                        } else {
                            this.showDialogFail(resp.error.message);
                        }
                    }
                },
            );
        },
        goToLogin() {
            this.hideAlldialogs();
            this.$router && this.$router.push({ name: 'signin' });
        },
    },
};
</script>

<style lang="scss">
.account_setting {
    .title {
        width: 100%;
        height: 52px;
        font-size: 24px;
        color: #333333;
        border-bottom: solid 1px #e0e0e0;
    }

    .content {
        padding-top: 24px;

        .pwd_tip {
            font-size: 14px;
            color: #888888;
            margin: 10px 0 24px 0;
        }

        .klk-input {
            width: 300px;
        }

        button {
            margin-top: 24px;
        }
    }
}
</style>
