<template lang="html">
    <div class="agent_currency_payment_channel_wrapper">
        <h3>{{ topupChannelTitle }}</h3>
        <div class="bank_account_info">
            <!--      CNY : 招商银行虚拟户-->
            <div v-if="currency === 'CNY'">
                <h4>
                    <klk-icon
                        type="credit-card"
                        size="16"
                        color="#009685"
                        class="va-top"
                    ></klk-icon>
                    <span class="icon_related_text">
                        {{ cnyBankInfo.name }}
                    </span>

                    <b style="margin-left: 8px;font-size: 10px">{{
                        $t('bank_top_up_tips')
                    }}</b>
                </h4>
                <ul class="info_list">
                    <li
                        v-for="(item, index) in [
                            'bankName',
                            'accountName',
                            'accountNumber',
                        ]"
                        :key="index"
                    >
                        {{ getBankAccountNameTitle(item) }} :
                        {{ cnyBankInfo[item] }}
                    </li>
                </ul>
            </div>
            <div
                v-for="(topupChannel, index) in topupChannelInfos"
                :key="index"
                class="bank_info_list--item"
            >
                <template
                    v-if="
                        (topupChannel.name === aliPayName &&
                            alipay.accountNumber &&
                            alipay.accountName) ||
                            topupChannel.name !== 'Alipay'
                    "
                >
                    <h4>
                        <klk-icon
                            type="credit-card"
                            size="16"
                            color="#009685"
                            class="va-top"
                        ></klk-icon>
                        <span class="icon_related_text">{{
                            topupChannel.name
                        }}</span>
                    </h4>
                    <ul class="info_list">
                        <li
                            v-for="item in Object.keys(topupChannel)"
                            :key="item"
                        >
                            <template v-if="item !== 'name'">
                                {{ getBankAccountNameTitle(item) }} :
                                <span v-if="topupChannel.name === aliPayName">
                                    {{ alipay[item] }}
                                </span>
                                <span v-else>
                                    {{ topupChannel[item] }}
                                </span>
                            </template>
                        </li>
                    </ul>
                </template>
            </div>
        </div>
        <div class="bank_operation_info">
            <p>{{ $t('bank_info_tip1') }}</p>
            <p>{{ $t('bank_info_tip2') }}</p>
            <p>{{ $t('bank_info_tip3') }}</p>
        </div>
    </div>
</template>

<script>
import getBankList from '@/common/bankList';
import urlObj from '@/common/url';
import { JV_NAME } from '~/jv_config';

export default {
    name: 'AgentCurrencyPaymentChannel',
    data() {
        return {
            cnyBankInfo: {
                name: '',
                bankName: '',
                accountName: '',
                accountNumber: '',
            },
            alipay: {
                accountName: '',
                accountNumber: '',
            },
        };
    },
    computed: {
        aliPayName() {
            return this.$t('alipay');
        },
        currency() {
            return (window.KLK_USER_INFO || {}).currency || '';
        },
        topupChannelTitle() {
            return `${this.$t('var_account_number', [
                JV_NAME[window.KLK_JV_NAME],
            ])}:`;
        },
        topupChannelInfos() {
            return getBankList(this.currency) || [];
        },
    },
    methods: {
        getBankAccountNameTitle(item) {
            if (item === 'accountNumber') {
                if (['Paypal', this.$t('alipay')].includes(item.name)) {
                    return `${this.$t('account')}`;
                }
                return this.$t(
                    `${String(this.currency).toLowerCase()}_account_number`,
                );
            }
            return this.$t(item.replace(/([A-Z])/g, '_$1').toLowerCase());
        },
    },
    mounted() {
        if (this.currency === 'CNY') {
            klook.ajaxGet(urlObj.agent_account_info, resp => {
                if (resp.success) {
                    const result = resp.result || {};
                    this.cnyBankInfo.accountNumber =
                        result.cmb_virtual_account_id;
                    this.cnyBankInfo.name = result.bank_name;
                    this.cnyBankInfo.bankName = result.bank_branch_name;
                    this.cnyBankInfo.accountName = result.bank_company_name;
                    this.alipay.accountName = result.alipay_name;
                    this.alipay.accountNumber = result.alipay_account;
                }
            });
        }
    },
};
</script>
<style lang="scss">
.agent_currency_payment_channel_wrapper {
    overflow: hidden;
    margin-top: 40px;

    h3 {
        position: relative;
        font-size: 18px;
        font-weight: 600;
        line-height: 21px;
        color: #333;
        padding-left: 12px;

        &:before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            width: 4px;
            height: 16px;
            border-radius: 2px;
            background-color: var(--primary-color);
            transform: translate(0, -50%);
        }
    }

    h4 {
        margin-top: 30px;
        margin-bottom: 16px;
        font-size: 14px;
        line-height: 16px;

        i {
            font-size: 16px;
            color: #009685;
        }
    }

    .bank_account_info {
        padding-left: 12px;
        margin-bottom: 30px;

        .icon_related_text {
            margin-left: 10px;
            color: #009685;
        }

        .info_list {
            li {
                font-size: 14px !important;
                color: #333 !important;
                margin-bottom: 12px !important;
                line-height: 1 !important;
            }
        }
    }

    .bank_operation_info {
        padding: 24px 0 24px 24px;
        margin: 24px 0 24px 0;
        border-radius: 2px;
        background-color: #fafafa;

        p {
            line-height: 20px;
        }
    }
}
</style>
