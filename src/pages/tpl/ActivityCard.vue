<template>
    <div class="activity-card">
        <div @click="goActivity">
            <div class="image-wrapper" ref="imageWrapper">
                <img
                    :src="getThumbnailUrl(activity)"
                    class="image-thumbnail"
                    :alt="activity.title"
                />
                <img
                    :data-src="getImageUrl(activity)"
                    class="image-progressive"
                    :alt="activity.title"
                />
                <span
                    :class="[
                        'activity-card-type',
                        activity.activity_type === 'hot'
                            ? 'activity-card-type-red'
                            : 'activity-card-type-blue',
                    ]"
                    v-if="activity.activity_type"
                >
                    {{
                        $t(
                            `activity_tag_${String(
                                activity.activity_type,
                            ).toLowerCase()}`,
                        )
                    }}
                </span>
                <span v-if="activity.is_promotion" class="flash-sale-icon">
                    {{ $t('flash_sale') }}
                </span>
                <div v-if="showWish" class="image-shadow" @click.stop>
                    <klk-icon
                        @mouseenter.native="isHover = true"
                        @mouseleave.native="isHover = false"
                        class="is-collect-icon"
                        size="24"
                        :color="
                            activity.is_wish || isHover ? '#ec6337' : '#fff'
                        "
                        :type="
                            activity.is_wish
                                ? 'collect-fill'
                                : 'icon_social_favorite'
                        "
                        @click="clickWish"
                        v-if="showCollectIcon"
                    >
                    </klk-icon>
                </div>
            </div>
            <div class="card-content">
                <div class="card-content-top">
                    <div class="card-content-title">{{ activity.title }}</div>
                    <div class="card-content-keyword" v-if="activity.showWords">
                        {{ activity.words }}
                    </div>
                </div>
                <div class="card-content-bottom">
                    <div
                        v-if="+activity.market_price > +activity.sell_price"
                        class="market-price"
                    >
                        {{ activity.currencySymbol }}&nbsp;{{
                            formatPriceThousands(activity.market_price)
                        }}
                    </div>
                    <div class="sell-price">
                        <span
                            v-html="
                                $t('price_from', [
                                    `${
                                        activity.currencySymbol
                                    } ${formatPriceThousands(
                                        activity.sell_price,
                                    )}`,
                                ])
                            "
                        >
                        </span>

                        <klk-icon
                            type="fast"
                            size="12"
                            v-show="activity.is_instance === 1"
                        ></klk-icon>
                    </div>
                    <div
                        :class="[
                            'card-content-available-date',
                            `card-content-available-date-${startDate[0]}`,
                        ]"
                        v-if="activity.showDate"
                    >
                        {{ startDate[1] }}
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import { openPage } from '@/common/util';
import { countOriginalSellPrice } from '@/pages/activity/util';
import { isToday, isTomorrow, format } from 'date-fns';
import collectMixin from '@/mixins/collect';

const subscriber =
    window.IntersectionObserver &&
    new IntersectionObserver(
        entries => {
            entries.forEach(entry => {
                if (entry.intersectionRatio < 0.5) {
                    return;
                }
                const el = entry.target;
                const progressiveEl = el.children[1];
                const imageSrc = progressiveEl.dataset['src'];
                if (imageSrc && !progressiveEl.src) {
                    const image = new Image();
                    image.src = imageSrc;
                    image.onload = () => {
                        progressiveEl.src = imageSrc;
                        // el.children[0].style.display = 'none';
                        setTimeout(
                            () => (progressiveEl.style.opacity = 1),
                            100,
                        );
                        // progressiveEl.style.visibility = 'visible';
                        subscriber.unobserve(el);
                    };
                    image.onerror = () => subscriber.unobserve(el);
                }
            });
        },
        { threshold: 0.5 },
    );

export default {
    name: 'ActivityCard',
    props: {
        activity: { type: Object, default: () => {} },
        showCollectIcon: { type: Boolean, default: true },
    },
    mixins: [collectMixin],
    data() {
        return {
            isHover: false,
        };
    },
    mounted() {
        if (subscriber) {
            subscriber.observe(this.$refs.imageWrapper);
        }
        if (this.activity && this.activity.is_promotion) {
            this.activity.market_price = countOriginalSellPrice(this.activity);
        }
    },
    computed: {
        startDate() {
            const startTime = this.activity.start_time;
            if (!startTime) {
                return ['normal', ''];
            }

            if (isToday(startTime)) {
                return ['green', this.$t('activity.v2.label.today_available')];
            }

            if (isTomorrow(startTime)) {
                return ['green', this.$t('book.tomorrow')];
            }

            return [
                'gray',
                `${this.$t('activity.v2.label.earliest_available')}${format(
                    startTime,
                    'YYYY-MM-DD',
                )}`,
            ];
        },
        showWish() {
            return !this.activity.is_vertical;
        },
    },
    methods: {
        clickWish() {
            if (this.activity.is_blocked) {
                return;
            }
            this.collect();
        },
        goActivity() {
            if (this.activity.is_blocked) {
                return;
            }
            const id = this.activity.id;
            if (
                this.activity.vertical_type &&
                this.activity.vertical_type == 102
            ) {
                //用deeplink跳转
                let url = this.activity.deep_link || '';
                if (!url.includes('detail')) {
                    url = url.replace('/hotels/', '/hotels/detail/');
                }
                if (url.includes('http')) {
                    url = url.replace(
                        /^http(s)?:\/\/(.*?)\//,
                        `${window.location.protocol}//${window.location.host}/`,
                    );
                    openPage({ url });
                    return;
                }
                const urlPath = `${window.location.origin}${window.KLK_LANG_PATH}/${url}`;
                openPage({ url: urlPath });
                return;
            }

            const href = this.$router.resolve({
                name: 'activity_detail',
                params: { id },
            }).href;
            openPage({ url: href });
        },
        getThumbnailUrl(activity) {
            const { image_url, vertical_type } = activity;
            if (!subscriber) {
                return this.getImageUrl(activity);
            }
            if (vertical_type && vertical_type !== 100) {
                return image_url;
            }

            return `https://res.klook.com/image/upload/c_thumb,w_64,h_37/activities/${this.getImageId(
                image_url,
            )}`;
        },
        getImageUrl(activity) {
            const { image_url, vertical_type } = activity;
            if (vertical_type && vertical_type !== 100) {
                return image_url;
            }
            return `https://res.klook.com/image/upload/fl_lossy.progressive,q_65/c_fill,w_640,h_368/activities/${this.getImageId(
                image_url,
            )}`;
        },
        getImageId(url) {
            if (/^http|https/.test(url)) {
                url = url.substr(url.lastIndexOf('/') + 1);
            }
            return url;
        },
        formatPriceThousands(price) {
            return klook.formatPriceThousands(price);
        },
    },
};
</script>

<style lang="scss">
.activity-card {
    cursor: pointer;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.06);
    transition: all 0.3s ease;
    border-radius: 2px;
    width: 320px;
    min-height: 270px;

    &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px 0 rgba(0, 0, 0, 0.08);
    }

    .image-shadow {
        position: absolute;
        top: 0px;
        height: 28%;
        width: 100%;
        padding: 8px 12px;
        background: linear-gradient(
            to bottom,
            rgba(0, 0, 0, 0.35),
            rgba(0, 0, 0, 0)
        );
    }

    .image-wrapper {
        position: relative;
        height: 185px;
        background: #ccc;

        img {
            position: absolute;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            border-radius: 2px 2px 0 0;
            transition: opacity 1s linear;
        }

        .image-progressive {
            opacity: 0;
        }

        .flash-sale-icon {
            position: absolute;
            left: 12px;
            top: 12px;
            z-index: 2;
        }
        .activity-card-type {
            position: absolute;
            left: 12px;
            bottom: 8px;
            color: #fff;
            font-size: 12px;
            font-weight: bold;
            padding: 5px 8px;
            border-radius: 2px;
        }

        .activity-card-type-blue {
            background: #24b985;
        }

        .activity-card-type-red {
            background: #ff5722;
        }
    }

    .card-content {
        padding: 8px 12px 12px;
        text-align: left;
        // position: relative;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        height: 108px;

        &-title {
            line-height: normal;
            max-height: 39px;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 2;
            font-size: 14px;
            font-weight: 600;
            color: #333;
            margin-bottom: 6px;
            padding-right: 15px;
        }

        &-keyword {
            font-size: 12px;
            color: #ff7f57;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            margin-bottom: 12px;
        }

        .market-price {
            font-size: 12px;
            color: #999;
            height: 14px;
            margin-bottom: 2px;
            text-decoration: line-through;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .sell-price {
            font-size: 14px;
            font-weight: 500;
            color: #333;
        }

        &-available-date {
            font-size: 12px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            margin-top: 8px;
            height: 14px;
            line-height: 14px;

            &-gray {
                color: #999;
            }

            &-green {
                color: #24b985;
            }
        }
    }

    .klk-icon-fast {
        color: var(--primary-color);
        position: relative;
        font-size: 12px;
        margin-left: 2px;
    }

    .is-collect-icon {
        position: absolute;
        top: 12px;
        right: 12px;
    }

    .klk-icon-collect {
        color: #ff5722;
    }
}
</style>
