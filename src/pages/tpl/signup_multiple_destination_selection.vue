<template>
    <div class="signup_multiple_destination_selection">
        <div
            class="selected_showcase"
            v-if="multiple_destination_select"
            @click="destination_dropdown_show = true"
        >
            <ul>
                <li
                    class="selected_item_wrapper"
                    v-for="selected_city in selected_city_list"
                    :key="selected_city.city_id"
                >
                    <span>{{ selected_city.city_name }}</span>
                    <label>
                        <klk-icon
                            size="14"
                            type="icon_other_plus_xs"
                            class="remove_tag"
                            @click="removeCity(selected_city.city_id)"
                        ></klk-icon>
                    </label>
                </li>
            </ul>
        </div>

        <div class="destination_dropdown" v-show="destination_dropdown_show">
            <klk-icon
                class="close_icon"
                size="20"
                type="icon_navigation_close"
                @click="destination_dropdown_show = false"
            ></klk-icon>
            <ul class="side_menu">
                <li
                    v-for="(destination, index) in destination_list"
                    :key="index"
                    :class="{
                        destination_wrapper: true,
                        destination_selected: index === selected_index,
                    }"
                    @mouseenter="selected_index = index"
                >
                    <p class="item_desc">{{ destination.range_name }}</p>

                    <div
                        class="destination_wrapper_border"
                        v-show="index === selected_index"
                    ></div>
                </li>
            </ul>
            <ul class="sub_list" v-if="destination_list[selected_index]">
                <li
                    class="sub_list_content"
                    v-for="country in destination_list[selected_index]
                        .countries"
                    :key="country.country_id"
                >
                    <div class="country_name_wrapper">
                        {{ country.country_name }}
                    </div>
                    <ul class="city_list">
                        <klk-checkbox-group v-model="selected_city_ids">
                            <li
                                class="city_wrapper"
                                :key="city.city_id"
                                v-for="city in country.cities"
                            >
                                <label
                                    class="destination"
                                    :class="{
                                        selected: selected_city_ids.includes(
                                            city.city_id,
                                        ),
                                    }"
                                >
                                    <klk-checkbox
                                        :key="city.city_id"
                                        :group-value="city.city_id"
                                        v-show="false"
                                        type="checkbox"
                                    >
                                    </klk-checkbox>
                                    <span
                                        @click="
                                            onCityClick(
                                                Object.assign({}, city, {
                                                    type: 'city',
                                                }),
                                            )
                                        "
                                        >{{ city.city_name }}</span
                                    >
                                </label>
                            </li>
                        </klk-checkbox-group>
                    </ul>
                </li>
            </ul>
        </div>
    </div>
</template>

<script>
export default {
    name: 'SignupMultipleDestinationSelection',
    props: {
        value: {
            default: () => [],
        },
        multiple_destination_select: {
            default: () => false,
        },
    },
    data() {
        return {
            destination_dropdown_show: true,
            search_input_tip: this.$t('top.search.destination'),
            destination_list: [],
            id_city_map: {},
            selected_city_ids: [],
            selected_city_list: [],
            selected_index: 0,
        };
    },
    watch: {
        selected_city_ids: function(newVal) {
            this.generateSelectedCityList();
            this.$emit('input', newVal);
        },
    },
    created() {
        this.multiple_destination_select &&
            (this.destination_dropdown_show = false);
        this.selected_city_ids = this.value.concat([]);
    },
    mounted() {
        this.loadData();
    },
    methods: {
        removeCity(cityId) {
            const index = this.selected_city_ids.findIndex(
                city => city === cityId,
            );
            this.selected_city_ids.splice(index, 1);
        },
        onCityClick(city) {
            this.$emit('on-city-click', city);
            if (!this.multiple_destination_select) {
                if (city.type === 'city') {
                    this.$router.push({
                        name: 'city_activity',
                        params: {
                            city_id: city.city_id + '',
                            from: 'search_bar',
                        },
                    });
                } else {
                    this.$router.push({
                        name: 'activity_detail',
                        params: {
                            id: city.id + '',
                        },
                    });
                }
            }
        },
        generateSelectedCityList() {
            let selected_city_list = [];
            this.selected_city_ids.forEach(city_id => {
                selected_city_list.push(this.id_city_map[city_id]);
            });
            this.selected_city_list = selected_city_list;
        },
        generateIdCityMap() {
            let id_city_map = {};
            this.destination_list.forEach(destination => {
                destination.countries.forEach(country => {
                    country.cities.forEach(city => {
                        id_city_map[city.city_id] = city;
                    });
                });
            });
            this.id_city_map = id_city_map;
        },
        loadData() {
            this.destination_list = window.KLK_CITY_LIST || [];
            this.generateIdCityMap();
        },
    },
};
</script>

<style lang="scss">
.signup_multiple_destination_selection {
    border-radius: 2px;
    background-color: #ffffff;
    z-index: 100;

    ul,
    li,
    p {
        margin: 0;
        padding: 0;
        list-style: none;
    }

    .selected_showcase {
        min-height: 42px;
        margin: 6px 0 0 0;
        width: 100%;
        border-radius: 2px;
        background: #fff;
        border: solid 1px #e0e0e0;
        padding: 3px;
        display: inline-block;

        .klk-icon-plus {
            vertical-align: middle;
            margin: 0 4px;
        }

        .selected_item_wrapper {
            float: left;
            line-height: 28px;
            padding: 0 0 0 8px;
            margin: 3px;
            border-radius: 2px;
            background: #e7f1fe;
            border: solid 1px #bbd8fd;
            color: var(--primary-color);

            .remove_tag {
                cursor: pointer;
                display: inline-block;
                width: 28px;
                text-align: center;
                color: var(--primary-color);
                transform: rotate(45deg);
            }
        }
    }

    .destination_dropdown {
        min-width: 800px;
        width: 100%;
        height: 100%;
        display: flex;
        overflow: hidden;
        box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.12);
        position: relative;
        .close_icon {
            cursor: pointer;
            position: absolute;
            right: 10px;
            top: 10px;
        }

        .side_menu {
            height: 100%;
            flex: 0 0 230px;
            border: solid 1px #e0e0e0;
            border-top: 0;
            position: relative;
            text-align: left;
            font-size: 16px;
            color: #333;
            box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.12);

            .destination_wrapper {
                background: #fff;
                border-bottom: solid 1px #e0e0e0;
                position: relative;

                .destination_wrapper_border {
                    z-index: 20;
                    position: absolute;
                    content: '';
                    background: var(--primary-color);
                    width: 16px;
                    height: 16px;
                    transform: rotate(45deg);
                    border-radius: 2px;
                    float: right;
                    top: 22px;
                    right: -8px;
                }

                .item_desc {
                    box-sizing: border-box;
                    height: 60px;
                    line-height: 60px;
                    padding-left: 14px;
                    position: relative;
                    overflow-x: hidden;
                }

                .selected {
                    color: var(--primary-color);
                }

                &:last-child {
                    border-bottom: none;
                }

                &.destination_selected {
                    color: #fff;
                    background: var(--primary-color);
                }
            }
        }

        .sub_list {
            max-height: 360px;
            overflow-y: auto;
            flex: 1 1 auto;
            background: #fff;
            /*!*border: solid 1px #d5d5d5;*!*/
            /*border-top: 0;*/
            padding: 10px 20px;
            color: #424242;
            line-height: 30px;
            /*box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.12);*/
            padding-bottom: 20px;

            .sub_list_content {
                display: flex;

                .country_name_wrapper {
                    flex: 0 0 134px;
                    color: #333;
                    font-size: 16px;
                    font-weight: 500;
                }

                .city_list {
                    flex: 1 1 auto;
                    font-size: 14px;
                    color: #999;

                    .city_wrapper {
                        display: inline-block;
                        padding: 0 10px;
                    }

                    .destination {
                        cursor: pointer;

                        &:hover,
                        &.selected {
                            color: var(--primary-color);
                        }
                    }
                }
            }
        }
    }
}
</style>
