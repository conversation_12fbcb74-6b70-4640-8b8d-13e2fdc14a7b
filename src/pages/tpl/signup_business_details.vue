<template lang="html">
    <klk-form
        class="klk_form"
        ref="signup_business_details_form"
        :rules="formRules"
        :model="signupBusinessDetailsData"
    >
        <div class="signup_business_detail">
            <div class="sub_title">
                {{ $t('business_detail') }}
            </div>
            <klk-form-item
                :label="$t('signup_company_selling_label')"
                prop="company_selling"
            >
                <klk-input
                    :maxlength="255"
                    v-model="signupBusinessDetailsData.company_selling"
                ></klk-input>
            </klk-form-item>

            <klk-form-item
                :label="$t('signup_top_destination_label')"
                prop="top_destinations"
            >
                <signup-multiple-destination-selection
                    :multiple_destination_select="true"
                    v-model="signupBusinessDetailsData.top_destinations"
                ></signup-multiple-destination-selection>
            </klk-form-item>

            <klk-form-item :label="$t('monthly_sales')" class="monthly_sales">
                <klk-select v-model="signupBusinessDetailsData.monthly_sales">
                    <klk-option
                        v-for="item in [
                            'USD 0 - USD 1,000,000',
                            'USD 1,000,000 - USD 2,000,000',
                            'USD 2,000,000 - USD 10,000,000',
                            'USD 10,000,000+',
                        ]"
                        :key="item"
                        :value="item"
                        :label="item"
                    >
                    </klk-option>
                </klk-select>
            </klk-form-item>
            <klk-form-item
                :label="$t('monthly_independent_visits_label')"
                class="monthly_independent"
                prop="monthly_visits"
            >
                <klk-input
                    :maxlength="255"
                    v-model="signupBusinessDetailsData.monthly_visits"
                ></klk-input>
            </klk-form-item>

            <klk-form-item :label="$t('know_way_label')" :required="true">
                <klk-select v-model="signupBusinessDetailsData.know_way">
                    <klk-option
                        v-for="item in signupBusinessDetailsData.know_way_list"
                        :key="item"
                        :value="$t(item)"
                        :label="$t(item)"
                    >
                    </klk-option>
                </klk-select>
            </klk-form-item>
        </div>
    </klk-form>
</template>
<script>
import formRules from '@/common/form_rules';
import SignupMultipleDestinationSelection from './signup_multiple_destination_selection.vue';

export default {
    name: 'SignupBusinessDetails',
    data() {
        return {
            signupBusinessDetailsData: {
                company_selling: '',
                top_destinations: [],
                monthly_sales: '$ 0 - $ 1,000,000',
                monthly_visits: '',
                know_way: this.$t('social_media'),
                know_way_list: [
                    'social_media',
                    'news',
                    'traveler_exhibition',
                    'app_store',
                    'friends',
                    'other_partner',
                    'offline_promotion',
                    'others',
                ],
            },
        };
    },
    created() {
        this.formRules = formRules.call(this);
    },
    methods: {
        getFormData() {
            return Object.assign({}, this.signupBusinessDetailsData);
        },
    },
    components: {
        SignupMultipleDestinationSelection,
    },
};
</script>
