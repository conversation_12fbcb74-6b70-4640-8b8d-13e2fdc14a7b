<template>
    <div class="count-down">
        <div v-if="!isFinished">
            <span class="pre-text" v-if="showText"> {{ showText }} </span>
            <div
                v-if="type === 'normal'"
                class="count-down-block count-down-normal normal"
            >
                <span v-for="(time, index) in countTimeArr" :key="index">
                    <span class="normal-time-item"> {{ time }} </span>
                    <span>{{ timeLang[index] }}</span>
                </span>
            </div>
            <div v-else class="count-down-block simplify">
                <span class="simplify-time-item"> {{ countTimeArr[0] }} </span>
                <span class="day">{{ timeLang[0] }}</span>
                <span class="simplify-time-item">{{ simplifyFormatTime }}</span>
            </div>
        </div>

        <div v-else>
            {{ finishedData }}
        </div>
    </div>
</template>
<script>
import { differenceInSeconds } from 'date-fns';

const DAY_BASE = 24 * 60 * 60,
    HOUR_BASE = 60 * 60,
    MINUTE_BASE = 60;
export default {
    name: 'CountDown',
    props: {
        endTime: {
            type: String,
            default: '',
        },
        finishedData: {
            type: String,
            default: '',
        },
        showText: {
            type: String,
            default: '',
        },
        type: {
            type: String,
            default: 'normal',
        },
    },
    computed: {
        countTimeArr() {
            let arr = [];
            arr.push(Math.floor(this.diffTime / DAY_BASE));
            arr.push(
                this.doubleNum(
                    Math.floor((this.diffTime % DAY_BASE) / HOUR_BASE),
                ),
            );
            arr.push(
                this.doubleNum(
                    Math.floor((this.diffTime % HOUR_BASE) / MINUTE_BASE),
                ),
            );
            arr.push(this.doubleNum(this.diffTime % MINUTE_BASE));
            return arr;
        },
        simplifyFormatTime() {
            if (this.type === 'normal') return;
            return this.countTimeArr.slice(1, 4).join(' : ');
        },
        isFinished() {
            return this.diffTime <= 0;
        },
    },
    data() {
        return {
            diffTime: -1,
            timeLang: [
                this.$t('day'),
                this.$t('hour'),
                this.$t('minute'),
                this.$t('second'),
            ],
        };
    },
    methods: {
        doubleNum(num) {
            if (num < 10) {
                return '0' + num;
            } else {
                return '' + num;
            }
        },
        countTime() {
            if (this.endTime) {
                this.diffTime = differenceInSeconds(
                    new Date(this.endTime),
                    new Date(),
                );
            } else {
                this.diffTime = -1;
            }
        },
        countDown() {
            this.countTime();
            let timeInterVal = setInterval(() => {
                if (this.diffTime < 0) {
                    clearInterval(timeInterVal);
                    this.$emit('finishCallback');
                }
                this.countTime();
            }, 1000);
        },
    },
    mounted() {
        this.countDown();
    },
};
</script>
<style lang="scss">
.count-down {
    color: #ff5722;
    font-size: 12px;

    .count-down-block {
        display: inline-block;
        vertical-align: middle;
    }
    .pre-text {
        display: inline-block;
        vertical-align: middle;
        color: #ff5722;
        margin-right: 5px;
    }
    .normal {
        span {
            span {
                margin-right: 5px;
            }
        }
        &-time-item {
            height: 28px;
            min-width: 28px;
            padding: 0 2px;
            font-size: 16px;
            font-weight: 500;
            line-height: 26px;
            text-align: center;
            display: inline-block;
            vertical-align: middle;
            border: 1px solid #ff5722;
            border-radius: 3px;
            box-sizing: border-box;
            -webkit-box-sizing: border-box;
        }
    }
    .simplify {
        font-weight: 600;
        span {
            margin-right: 5px;
        }
        .simplify-time-item {
            margin: 0;
        }
    }
}
</style>
