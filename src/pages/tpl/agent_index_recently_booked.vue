<template lang="html">
    <div class="agent_index_recently_booked">
        <div
            class="activity_card_wrapper"
            v-for="activity in activity_list"
            :key="activity.id"
        >
            <activity-card :activity="activity"></activity-card>
        </div>
        <slot></slot>
    </div>
</template>

<script>
import ActivityCard from './ActivityCard.vue';

export default {
    name: 'AgentIndexRecentlyBooked',
    props: ['prop_data'],
    data() {
        return {
            activity_list: [],
        };
    },
    components: {
        ActivityCard,
    },
    watch: {
        prop_data: function() {
            this.initData();
        },
    },
    mounted() {
        this.initData();
    },
    methods: {
        initData() {
            let currencySymbol = '';
            function processData(list) {
                list.forEach(activity => {
                    if (!currencySymbol) {
                        currencySymbol = this.getCurrencySymbolByCurrencyCode(
                            activity.currency,
                        );
                    }
                    activity.currencySymbol = currencySymbol;
                });
                return list;
            }
            this.activity_list = processData.call(
                this,
                this.prop_data.activity_list,
            );
        },
    },
};
</script>
<style lang="scss">
.agent_index_recently_booked {
    min-height: 320px;
    overflow: hidden;

    .activity_card_wrapper {
        margin: 0 20px 20px 0;
        width: 325px;
        float: left;
    }
}
</style>
