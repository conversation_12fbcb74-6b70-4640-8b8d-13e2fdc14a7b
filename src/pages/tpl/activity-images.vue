<template>
    <div class="activity-images">
        <image-swiper :stepLength="224" v-if="(images || []).length > 0">
            <image-swiper-slide
                class="swiper-slide"
                v-for="(image, index) in images"
                :key="index"
            >
                <img
                    width="100%"
                    :src="image.image_url_host || image.image_url"
                    :alt="image.image_alt"
                    @click="previewUsageImage(index)"
                />
            </image-swiper-slide>
        </image-swiper>
        <klk-modal
            size="large"
            :closable="true"
            :open.sync="visible"
            :show-default-footer="false"
            @on-close="visible = false"
        >
            <div class="klk-modal-content">
                <klk-carousel
                    :active.sync="activeIndex"
                    :controllers-outside="false"
                >
                    <klk-carousel-item
                        v-for="(image, index) in images"
                        :key="index"
                    >
                        <img
                            height="500px"
                            :src="image.image_url_host || image.image_url"
                        />
                    </klk-carousel-item>
                </klk-carousel>
            </div>
        </klk-modal>
    </div>
</template>

<script>
import {
    KlkSwiper,
    KlkSwiperSlide,
} from '@/pages/activity/components/slider/index';

export default {
    name: 'ActivityImages',
    components: {
        ImageSwiper: KlkSwiper,
        ImageSwiperSlide: KlkSwiperSlide,
    },
    props: {
        images: {
            required: true,
            default: () => [],
        },
    },
    data() {
        return {
            activeIndex: 0,
            visible: false,
        };
    },
    methods: {
        previewUsageImage(index) {
            this.activeIndex = index;
            this.visible = true;
        },
    },
};
</script>

<style lang="scss">
.activity-images {
    margin-top: 10px;
}
</style>
