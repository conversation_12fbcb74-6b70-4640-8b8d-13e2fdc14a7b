<template lang="html">
    <div class="form_wrapper">
        <klk-form
            class="klk_form"
            ref="signup_company_details_form"
            :rules="formRules"
            :model="signupCompanyDetailsData"
        >
            <div class="signup_company_details">
                <div class="sub_title">
                    {{ $t('company_details') }}
                </div>
                <!-- phone Number -->
                <klk-form-item
                    :label="$t('company_phone_number')"
                    prop="company_phone_number"
                >
                    <klk-input
                        :maxlength="255"
                        v-model="signupCompanyDetailsData.company_phone_number"
                    ></klk-input>
                </klk-form-item>
                <!-- eamil address -->
                <klk-form-item
                    :label="$t('company_email_address')"
                    prop="company_email_address"
                >
                    <klk-input
                        :maxlength="255"
                        v-model="signupCompanyDetailsData.company_email_address"
                    ></klk-input>
                </klk-form-item>
                <!-- number of employees -->
                <klk-form-item
                    :label="$t('number_of_employees')"
                    prop="num_of_employees"
                >
                    <klk-input
                        :maxlength="255"
                        v-model="signupCompanyDetailsData.num_of_employees"
                    ></klk-input>
                </klk-form-item>
                <!-- Date of Establishment -->
                <klk-form-item
                    :label="$t('date_of_establishment')"
                    prop="establishment_date"
                >
                    <klk-single-date-picker
                        class="datetime"
                        :placeholder="$t('global.select.palceholder')"
                        format="YYYY-MM-DD"
                        v-model="signupCompanyDetailsData.establishment_date"
                    >
                    </klk-single-date-picker>
                </klk-form-item>
                <klk-form-item
                    class="upload_license_form_item"
                    :label="$t('business_license')"
                    prop="business_license"
                >
                    <div class="license_upload_wrapper">
                        <klk-upload
                            :onProgress="onUploadBusinessLicenseProgress"
                            :onSuccess="onUploadBusinessLicenseSuccess"
                            :onError="onUploadBusinessLicenseError"
                            :accept="file_type"
                            :action="upload_url"
                            name="user_file"
                            :max-size="max_file_size"
                            :on-exceeded-size="handleMaxSize"
                        >
                            <div
                                v-show="
                                    !signupCompanyDetailsData.business_license
                                "
                                class="upload_trigger upload_area"
                                v-loading="uploading_business_license_img"
                            >
                                {{ $t('click_upload') }}
                            </div>
                        </klk-upload>
                        <div
                            v-show="signupCompanyDetailsData.business_license"
                            v-loading="uploading_business_license_img"
                            class="business_license_img_wrapper"
                        >
                            <a
                                :href="local_business_license_url"
                                class="img_wrapper_link"
                                target="_blank"
                            >
                                <img :src="local_business_license_url" alt="" />
                            </a>
                            <div class="license_operation_group">
                                <div
                                    class="update_license license_operation upload_trigger"
                                >
                                    <klk-upload
                                        :onProgress="
                                            onUploadBusinessLicenseProgress
                                        "
                                        :onSuccess="
                                            onUploadBusinessLicenseSuccess
                                        "
                                        :onError="onUploadBusinessLicenseError"
                                        :accept="file_type"
                                        :action="upload_url"
                                        name="user_file"
                                        :max-size="max_file_size"
                                        :on-exceeded-size="handleMaxSize"
                                    >
                                        {{ $t('upload') }}
                                    </klk-upload>
                                </div>
                                <div
                                    class="delete_license license_operation"
                                    @click="
                                        signupCompanyDetailsData.business_license =
                                            ''
                                    "
                                >
                                    {{ $t('delete') }}
                                </div>
                            </div>
                        </div>
                    </div>
                </klk-form-item>
                <klk-form-item
                    class="upload_license_form_item"
                    :label="$t('travel_agency_license')"
                    prop="agency_license"
                >
                    <div class="license_upload_wrapper">
                        <klk-upload
                            :onProgress="onUploadAgencyLicenseProgress"
                            :onSuccess="onUploadAgencyLicenseSuccess"
                            :onError="onUploadAgencyLicenseError"
                            :accept="file_type"
                            :action="upload_url"
                            name="user_file"
                            :max-size="max_file_size"
                            :on-exceeded-size="handleMaxSize"
                        >
                            <div
                                v-show="
                                    !signupCompanyDetailsData.agency_license
                                "
                                class="upload_trigger upload_area"
                                v-loading="uploading_agency_license_img"
                            >
                                {{ $t('click_upload') }}
                            </div>
                        </klk-upload>
                        <div
                            v-show="signupCompanyDetailsData.agency_license"
                            v-loading="uploading_agency_license_img"
                            class="business_license_img_wrapper"
                        >
                            <a
                                :href="local_agency_license_url"
                                class="img_wrapper_link"
                                target="_blank"
                            >
                                <img :src="local_agency_license_url" alt="" />
                            </a>
                            <div class="license_operation_group">
                                <div
                                    class="update_license license_operation upload_trigger"
                                >
                                    <klk-upload
                                        :onProgress="
                                            onUploadAgencyLicenseProgress
                                        "
                                        :onSuccess="
                                            onUploadAgencyLicenseSuccess
                                        "
                                        :onError="onUploadAgencyLicenseError"
                                        :accept="file_type"
                                        :action="upload_url"
                                        name="user_file"
                                        :max-size="max_file_size"
                                        :on-exceeded-size="handleMaxSize"
                                    >
                                        {{ $t('upload') }}
                                    </klk-upload>
                                </div>
                                <div
                                    class="delete_license license_operation"
                                    @click="
                                        signupCompanyDetailsData.agency_license =
                                            ''
                                    "
                                >
                                    {{ $t('delete') }}
                                </div>
                            </div>
                        </div>
                    </div>
                </klk-form-item>
                <!-- Travel Agency Insurance Policy -->
                <klk-form-item
                    class="upload_license_form_item"
                    :label="$t('travel_agency_insurance_policy')"
                    prop="travel_agency_insurance_policy"
                >
                    <div class="license_upload_wrapper">
                        <klk-upload
                            :onProgress="
                                onUploadTravelAgencyInsurancePolicyProgress
                            "
                            :onSuccess="
                                onUploadTravelAgencyInsurancePolicySuccess
                            "
                            :onError="onUploadTravelAgencyInsurancePolicyError"
                            :accept="file_type"
                            :action="upload_url"
                            name="user_file"
                            :max-size="max_file_size"
                            :on-exceeded-size="handleMaxSize"
                        >
                            <div
                                v-show="
                                    !signupCompanyDetailsData.travel_agency_insurance_policy
                                "
                                class="upload_trigger upload_area"
                                v-loading="
                                    uploading_travel_agency_insurance_policy_img
                                "
                            >
                                {{ $t('click_upload') }}
                            </div>
                        </klk-upload>
                        <div
                            v-show="
                                signupCompanyDetailsData.travel_agency_insurance_policy
                            "
                            v-loading="
                                uploading_travel_agency_insurance_policy_img
                            "
                            class="business_license_img_wrapper"
                        >
                            <a
                                :href="local_travel_agency_insurance_policy_url"
                                class="img_wrapper_link"
                                target="_blank"
                            >
                                <img
                                    :src="
                                        local_travel_agency_insurance_policy_url
                                    "
                                    alt=""
                                />
                            </a>
                            <div class="license_operation_group">
                                <div
                                    class="update_license license_operation upload_trigger"
                                >
                                    <klk-upload
                                        :onProgress="
                                            onUploadTravelAgencyInsurancePolicyProgress
                                        "
                                        :onSuccess="
                                            onUploadTravelAgencyInsurancePolicySuccess
                                        "
                                        :onError="
                                            onUploadTravelAgencyInsurancePolicyError
                                        "
                                        :accept="file_type"
                                        :action="upload_url"
                                        name="user_file"
                                        :max-size="max_file_size"
                                        :on-exceeded-size="handleMaxSize"
                                    >
                                        {{ $t('upload') }}
                                    </klk-upload>
                                </div>
                                <div
                                    class="delete_license license_operation"
                                    @click="
                                        signupCompanyDetailsData.travel_agency_insurance_policy =
                                            ''
                                    "
                                >
                                    {{ $t('delete') }}
                                </div>
                            </div>
                        </div>
                    </div>
                </klk-form-item>
                <!-- applicant‘s business card -->
                <klk-form-item
                    class="upload_license_form_item"
                    :label="$t('applicant_business_card')"
                    prop="business_card"
                >
                    <div class="license_upload_wrapper">
                        <klk-upload
                            :onProgress="onUploadBusinessCardProgress"
                            :onSuccess="onUploadBusinessCardSuccess"
                            :onError="onUploadBusinessCardError"
                            :accept="file_type"
                            :action="upload_business_card_url"
                            name="user_file"
                            :max-size="max_file_size"
                            :on-exceeded-size="handleMaxSize"
                        >
                            <div
                                v-show="!signupCompanyDetailsData.business_card"
                                class="upload_trigger upload_area"
                                v-loading="uploading_business_card_img"
                            >
                                {{ $t('click_upload') }}
                            </div>
                        </klk-upload>
                        <div
                            v-show="signupCompanyDetailsData.business_card"
                            v-loading="uploading_business_card_img"
                            class="business_license_img_wrapper"
                        >
                            <a
                                :href="local_business_card_url"
                                class="img_wrapper_link"
                                target="_blank"
                            >
                                <img :src="local_business_card_url" alt="" />
                            </a>
                            <div class="license_operation_group">
                                <div
                                    class="update_license license_operation upload_trigger"
                                >
                                    <klk-upload
                                        :onProgress="
                                            onUploadBusinessCardProgress
                                        "
                                        :onSuccess="onUploadBusinessCardSuccess"
                                        :onError="onUploadBusinessCardError"
                                        :accept="file_type"
                                        :action="upload_business_card_url"
                                        name="user_file"
                                        :max-size="max_file_size"
                                        :on-exceeded-size="handleMaxSize"
                                    >
                                        {{ $t('upload') }}
                                    </klk-upload>
                                </div>
                                <div
                                    class="delete_license license_operation"
                                    @click="
                                        signupCompanyDetailsData.business_card =
                                            ''
                                    "
                                >
                                    {{ $t('delete') }}
                                </div>
                            </div>
                        </div>
                    </div>
                </klk-form-item>

                <klk-form-item
                    :label="$t('signup_choose_cooperation_type_tip')"
                    prop="cooperation_type"
                >
                    <klk-checkbox-group
                        v-model="signupCompanyDetailsData.cooperation_type"
                    >
                        <klk-checkbox
                            v-for="item in cooperation_channels"
                            :key="item.id"
                            :group-value="item.id"
                            >{{ item.name }}</klk-checkbox
                        >
                    </klk-checkbox-group>
                </klk-form-item>

                <!-- Information about the company-->
                <klk-form-item
                    class="upload_license_form_item"
                    :label="$t('information_about_the_company')"
                    prop="company_information"
                >
                    <div class="license_upload_wrapper">
                        <klk-upload
                            :onProgress="onUploadCompanyInformationProgress"
                            :onSuccess="onUploadCompanyInformationSuccess"
                            :onError="onUploadCompanyInformationError"
                            :accept="file_type"
                            :action="upload_url"
                            name="user_file"
                            :max-size="max_file_size"
                            :on-exceeded-size="handleMaxSize"
                        >
                            <div
                                v-show="
                                    !signupCompanyDetailsData.company_information
                                "
                                class="upload_trigger upload_area"
                                v-loading="uploading_company_information_img"
                            >
                                {{ $t('click_upload') }}
                            </div>
                        </klk-upload>
                        <div
                            v-show="
                                signupCompanyDetailsData.company_information
                            "
                            v-loading="uploading_company_information_img"
                            class="business_license_img_wrapper"
                        >
                            <a
                                :href="local_company_information_url"
                                class="img_wrapper_link"
                                target="_blank"
                            >
                                <img
                                    :src="local_company_information_url"
                                    alt=""
                                />
                            </a>
                            <div class="license_operation_group">
                                <div
                                    class="update_license license_operation upload_trigger"
                                >
                                    <klk-upload
                                        :onProgress="
                                            onUploadCompanyInformationProgress
                                        "
                                        :onSuccess="
                                            onUploadCompanyInformationSuccess
                                        "
                                        :onError="
                                            onUploadCompanyInformationError
                                        "
                                        :accept="file_type"
                                        :action="upload_url"
                                        name="user_file"
                                        :max-size="max_file_size"
                                        :on-exceeded-size="handleMaxSize"
                                    >
                                        {{ $t('upload') }}
                                    </klk-upload>
                                </div>
                                <div
                                    class="delete_license license_operation"
                                    @click="
                                        signupCompanyDetailsData.company_information =
                                            ''
                                    "
                                >
                                    {{ $t('delete') }}
                                </div>
                            </div>
                        </div>
                    </div>
                </klk-form-item>
            </div>
        </klk-form>

        <div class="form_tip travel_agency_form_tip">
            {{ $t('signup_travel_agency_tip') }}
        </div>
        <div class="form_tip company_informatio_form_tip">
            {{ $t('signup_company_information_tip') }}
        </div>
    </div>
</template>

<script>
import formRules from '@/common/form_rules';
import urlObj from '@/common/url';

export default {
    name: 'SignupCompanyDetails',
    data() {
        return {
            signupCompanyDetailsData: {
                business_license: '',
                agency_license: '',
                cooperation_type: [],
                company_phone_number: '',
                company_email_address: '',
                num_of_employees: '',
                establishment_date: null,
                travel_agency_insurance_policy: '',
                business_card: '',
                company_information: '',
            },
            uploading_business_license_img: false,
            uploading_agency_license_img: false,
            uploading_travel_agency_insurance_policy_img: false,
            uploading_business_card_img: false,
            uploading_company_information_img: false,
            cooperation_channels: [
                {
                    id: 'platform',
                    name: this.$t('signup_cooperation_platform'),
                },
                {
                    id: 'api',
                    name: this.$t('signup_cooperation_api'),
                },
            ],

            local_business_license_url: '',
            local_agency_license_url: '',
            local_travel_agency_insurance_policy_url: '',
            local_business_card_url: '',
            local_company_information_url: '',
            max_file_size: 2000, //2000kb
            file_type: 'image/jpeg,image/png,application/pdf',
            upload_url: urlObj.upload_agent_license,
            upload_business_card_url: urlObj.upload_agent_license_business_card,
            selectedDate: '',
        };
    },
    created() {
        this.formRules = formRules.call(this);
    },
    methods: {
        // business_license
        onUploadBusinessLicenseProgress(e) {
            if (e.percent != 1) {
                this.uploading_business_license_img = true;
            }
        },
        onUploadBusinessLicenseSuccess(resp, file) {
            if (resp.success) {
                this.uploading_business_license_img = false;
                this.signupCompanyDetailsData['business_license'] = resp.result;
                this.local_business_license_url = file.local_file_url;
            } else {
                this.onUploadBusinessLicenseError({
                    msg: resp.error.message,
                });
            }
        },
        onUploadBusinessLicenseError(error) {
            this.uploading_business_license_img = false;
            window.alert(error.msg);
        },
        // agency_license
        onUploadAgencyLicenseProgress(e) {
            if (e.percent !== 1) {
                this.uploading_agency_license_img = true;
            }
        },
        onUploadAgencyLicenseSuccess(resp, file) {
            if (resp.success) {
                this.uploading_agency_license_img = false;
                this.signupCompanyDetailsData['agency_license'] = resp.result;
                this.local_agency_license_url = file.local_file_url;
            } else {
                this.onUploadAgencyLicenseError({
                    msg: resp.error.message,
                });
            }
        },
        onUploadAgencyLicenseError(error) {
            this.uploading_agency_license_img = false;
            window.alert(error.msg);
        },
        // agency insureace
        onUploadTravelAgencyInsurancePolicyProgress(e) {
            if (e.percent !== 1) {
                this.uploading_travel_agency_insurance_policy_img = true;
            }
        },
        onUploadTravelAgencyInsurancePolicySuccess(resp, file) {
            if (resp.success) {
                this.uploading_travel_agency_insurance_policy_img = false;
                this.signupCompanyDetailsData[
                    'travel_agency_insurance_policy'
                ] = resp.result;
                this.local_travel_agency_insurance_policy_url =
                    file.local_file_url;
            } else {
                this.onUploadTravelAgencyInsurancePolicyError({
                    msg: resp.error.message,
                });
            }
        },
        onUploadTravelAgencyInsurancePolicyError(error) {
            this.uploading_travel_agency_insurance_policy_img = false;
            window.alert(error.msg);
        },

        // business_card
        onUploadBusinessCardProgress(e) {
            if (e.percent !== 1) {
                this.uploading_business_card_img = true;
            }
        },
        onUploadBusinessCardSuccess(resp, file) {
            if (resp.success) {
                this.uploading_business_card_img = false;
                this.signupCompanyDetailsData['business_card'] = resp.result;
                this.local_business_card_url = file.local_file_url;
            } else {
                this.onUploadBusinessCardError({
                    msg: resp.error.message,
                });
            }
        },
        onUploadBusinessCardError(error) {
            this.uploading_business_card_img = false;
            window.alert(error.msg);
        },
        // company Information
        onUploadCompanyInformationProgress(e) {
            if (e.percent !== 1) {
                this.uploading_company_information_img = true;
            }
        },
        onUploadCompanyInformationSuccess(resp, file) {
            if (resp.success) {
                this.uploading_company_information_img = false;
                this.signupCompanyDetailsData['company_information'] =
                    resp.result;
                this.local_company_information_url = file.local_file_url;
            } else {
                this.onUploadCompanyInformationError({
                    msg: resp.error.message,
                });
            }
        },
        onUploadCompanyInformationError(error) {
            this.uploading_company_information_img = false;
            window.alert(error.msg);
        },

        handleMaxSize(file) {
            window.alert(
                'File  ' + file.name + ' is too large, no more than 2M.',
            );
        },

        getFormData() {
            return Object.assign({}, this.signupCompanyDetailsData);
        },
    },
};
</script>

<style lang="scss">
.signup_company_details {
    .upload_statement {
        margin-top: 20px;
        margin-bottom: -12px;
    }
    .klk_checkbox_wrapper {
        margin-right: 60px !important;
    }

    .img_wrapper_link {
        display: inline-block;
        width: 100%;
        height: 100%;
    }
}
</style>
