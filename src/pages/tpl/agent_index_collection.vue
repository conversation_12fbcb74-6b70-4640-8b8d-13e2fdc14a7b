<template lang="html">
    <div class="agent_index_collection" v-loading="loading_collection">
        <div
            class="activity_card_wrapper"
            v-for="(activity, index) in activity_list"
            :key="index"
        >
            <activity-card
                :class="{ disabled_card: activity.is_blocked }"
                :activity="activity"
                :key="activity.id"
                :mode="mode"
                @change="initData"
                @click.native="$emit('click-card', activity.id, index + 1)"
            >
            </activity-card>
        </div>
        <slot>
            <klk-no-data
                :no_data_text_prop="$t('you_have_not_collected_any_activity')"
                v-show="!activity_list.length && !loading_collection"
            ></klk-no-data>
        </slot>
        <div
            class="pagination_wrapper"
            v-show="mode === 'user_center_collection'"
        >
            <klk-pagination
                :total="pagination.total"
                :page-size="pagination.pageSize"
                :current.sync="pagination.curPage"
                @change="handleCurrentChange"
            ></klk-pagination>
        </div>
    </div>
</template>

<script>
import urlObj from '@/common/url';
import { filterBlockData } from '@/common/util';
import ActivityCard from './ActivityCard.vue';

export default {
    name: 'AgentIndexCollection',
    props: ['prop_data'],
    data() {
        return {
            activity_list: [],
            pagination: {
                total: 0,
                pageSize: 6,
                curPage: 1,
            },
            loading_collection: false,
            mode:
                this.prop_data && this.prop_data.mode
                    ? this.prop_data.mode
                    : 'default', // 1,user_center_collection
        };
    },
    components: {
        ActivityCard,
    },
    watch: {
        prop_data: function() {
            this.initData();
        },
    },
    mounted() {
        this.initData();
    },
    methods: {
        filterBlockData,
        handleCurrentChange(curPage) {
            this.pagination.curPage = curPage;
            this.loadData();
        },
        processData(list) {
            let currencySymbol = '';
            function processData(list) {
                list.forEach(activity => {
                    if (!currencySymbol) {
                        currencySymbol = this.getCurrencySymbolByCurrencyCode(
                            activity.currency,
                        );
                    }
                    activity.currencySymbol = currencySymbol;
                    this.$set(activity, 'is_wish', true); //因为是获取的收藏，所以肯定是被收藏了的
                });
                return list;
            }
            this.activity_list = processData.call(this, list);
        },
        initData() {
            if (!this.prop_data.activity_list) {
                this.loadData();
            } else {
                this.processData(this.prop_data.activity_list);
            }
        },
        loadData() {
            this.loading_collection = true;
            klook.ajaxGet(
                urlObj.agent_collection,
                {
                    page: this.pagination.curPage,
                    per_page: this.pagination.pageSize,
                },
                resp => {
                    this.loading_collection = false;

                    if (resp.success) {
                        this.pagination.total = resp.result.total;
                        this.processData(resp.result.list || []);
                    }
                },
            );
        },
    },
};
</script>
<style lang="scss">
.agent_index_collection {
    min-height: 320px;
    // overflow: hidden;

    .activity_card_wrapper {
        margin: 0 20px 20px 0;
        width: 350px;
        float: left;
    }
    .activity-card {
        width: 350px;
        min-height: 322px;
        .image-wrapper {
            width: 350px;
            height: 210px;
        }
    }
    .pagination_wrapper {
        width: 100%;
        float: right;
        margin: 0 0 30px 0;
    }
    .disabled_card {
        .card-content {
            color: grey;
            cursor: not-allowed;
            .card-content-title,
            .sell-price,
            .klk-icon-fast {
                color: grey;
            }
        }
        .image-wrapper {
            cursor: not-allowed;
        }
    }
}
</style>
