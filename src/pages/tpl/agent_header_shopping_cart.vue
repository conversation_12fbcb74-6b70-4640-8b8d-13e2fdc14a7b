<template lang="html">
    <div>
        <span
            class="nav--title"
            id="shopping_cart_trigger"
            @click="dropdown_show = true"
        >
            {{ $t('global.nav.shopping_cart') }}
            <span class="shopping_cart_flag" v-show="tableData.length">{{
                tableData.length
            }}</span>
            <klk-icon type="icon_shopping_shopping_cart" size="20"></klk-icon>
        </span>
        <klk-modal
            :open.sync="dropdown_show"
            size="large"
            class="agent_header_shopping_cart_dialog"
        >
            <div
                class="agent_header_shopping_cart"
                v-loading="loading_shopping_cart"
            >
                <div class="agent_header_shopping_cart_header">
                    <span>{{ $t('shoppingcart') }}</span>
                    <span
                        class="close_shopping_cart"
                        @click="dropdown_show = false"
                    >
                        <klk-icon
                            size="20"
                            type="icon_navigation_close"
                        ></klk-icon>
                    </span>
                </div>
                <div class="agent_header_shopping_cart_content_wrapper">
                    <shopping-cart-table
                        :cols="cols"
                        :table_data="tableData"
                        :transition_enable="true"
                    >
                        <template scope="scope">
                            <tr
                                :key="scope.row.shoppingcart_id"
                                :class="{ disabled_cart: scope.row.is_blocked }"
                            >
                                <td>
                                    <a
                                        @click="gotoActivityDetail(scope.row)"
                                        style="float:left;"
                                    >
                                        <img
                                            class="activity_image"
                                            :src="scope.row.activity_image_url"
                                        />
                                    </a>
                                    <div style="padding-left: 150px;">
                                        <p class="activity_name">
                                            {{ scope.row.activity_name }}
                                        </p>
                                        <p class="package_name">
                                            {{ scope.row.package_name }}
                                        </p>
                                        <p
                                            class="expired_or_unpublished"
                                            v-show="!scope.row.published"
                                        >
                                            {{ $t('activity.v2.btn.publish') }}
                                        </p>
                                        <p
                                            class="expired_or_unpublished"
                                            v-show="
                                                scope.row.published &&
                                                    scope.row.expired
                                            "
                                        >
                                            {{
                                                $t('promo_code.coupon.expired')
                                            }}
                                        </p>
                                    </div>
                                </td>
                                <td>{{ scope.row.selected_time }}</td>
                                <td>
                                    <p
                                        v-for="(item, index) in scope.row
                                            .price_items"
                                        :key="index"
                                    >
                                        {{ item.count }} x {{ item.name }}
                                    </p>
                                </td>
                                <td>
                                    {{ currencySymbol }}
                                    {{ scope.row.ticket_sell_price }}
                                    <CountDown
                                        v-if="scope.row.isPromotion"
                                        type="simplify"
                                        :key="
                                            `CountDown${scope.row.shoppingcart_id}`
                                        "
                                        :start-time="
                                            scope.row.start_promotion_time
                                        "
                                        :end-time="scope.row.end_promotion_time"
                                        :finished-data="
                                            $t('promotion_finished_data')
                                        "
                                        @finishCallback="
                                            loadData(false, 'renewal_price')
                                        "
                                    >
                                    </CountDown>
                                </td>
                                <td>
                                    <klk-icon
                                        type="icon_edit_delete"
                                        size="16"
                                        @click="
                                            removeFromCart(
                                                scope.row.shoppingcart_id,
                                            )
                                        "
                                    ></klk-icon>
                                </td>
                            </tr>
                        </template>
                    </shopping-cart-table>
                </div>
            </div>
            <div class="agent_header_shopping_cart_footer" slot="footer">
                <span class="total_price_wrapper g_v_mid"
                    >{{ currencySymbol }}{{ total_price }}</span
                >
                <klk-button :disabled="disablePayNow" @click="buyNow">
                    {{ $t('shoppingcart.paynow') }}
                </klk-button>
            </div>
        </klk-modal>
    </div>
</template>

<script>
import shoppingCartTable from '@/components/table/Table.vue';
import urlObj from '@/common/url';
import CountDown from '@/pages/tpl/countdown.vue';

export default {
    name: 'AgentHeaderShoppingCart',

    components: {
        CountDown,
        shoppingCartTable,
    },
    data() {
        return {
            dropdown_show: false,
            tableData: [],
            // currency:'',
            currencySymbol: '',
            total_price: '',
            loading_shopping_cart: false,
            disablePayNow: false,
        };
    },
    computed: {
        cols() {
            return [
                { label: this.$t('shoppingcart.table.activity.desc') },
                { label: this.$t('global.date'), width: '135px' },
                { label: this.$t('shoppingcart.table.units'), width: '160px' },
                { label: this.$t('subtotal'), width: '150px' },
                { label: this.$t('global.opt.delete'), width: '100px' },
            ];
        },
    },
    watch: {
        tableData: {
            handler: function() {
                this.setPayBtnStatus();
            },
            deep: true,
        },
        dropdown_show: {
            handler: function(newVal) {
                if (newVal) {
                    //购物车弹出的时候再次更新
                    this.loadData('dropdown_show');
                }
            },
        },
    },
    mounted() {
        this.loadData();
    },
    methods: {
        setPayBtnStatus() {
            let unpublishedOrExpired = [];
            if (this.tableData.length > 0) {
                unpublishedOrExpired = this.tableData.filter(
                    item => !item.published || item.expired,
                );
                this.disablePayNow = unpublishedOrExpired.length > 0;
            } else {
                this.disablePayNow = true;
            }
        },
        gotoActivityDetail(activity) {
            if (activity.is_blocked) {
                return;
            }
            this.$router.push({
                name: 'activity_detail',
                params: {
                    id: activity.activity_id,
                },
            });
            this.onPageChange();
        },
        onPageChange() {
            this.dropdown_show = false;
        },
        removeFromCart(shoppingcart_id) {
            klook.ajaxPostJSON(
                urlObj.delete_shopping_cart_item,
                {
                    items: [
                        {
                            shoppingcart_id: shoppingcart_id,
                        },
                    ],
                },
                resp => {
                    if (resp.success) {
                        this.loadData();
                    }
                },
            );
        },
        buyNow() {
            if (this.disablePayNow) {
                //购物车可能存在已经下架或者已经过期的活动
                return;
            }
            this.dropdown_show = false;
            // window.location = `${window.KLK_LANG_PATH}/payment/pay/`;
            this.$router.push({ name: 'pay' });
        },

        renewalPrice(result) {
            // 倒计时结束时更新价格
            this.tableData.forEach(item => {
                let resultItem = result.shoppingcart_items.filter(
                    freshItem => freshItem.activity_id === item.activity_id,
                )[0];
                if (resultItem) {
                    item.ticket_sell_price = klook.formatPriceThousands(
                        resultItem.ticket_sell_price,
                    );
                }
            });
            this.total_price = klook.formatPriceThousands(result.total_price);
        },
        processData(list) {
            list.forEach(item => {
                item.selected_time = item.selected_time
                    .substring(0, 'yyyy-mm-dd hh:mm'.length)
                    .split(' 00:00')[0]; //去掉秒数（最后两位），如果还以00:00结尾，那么就只保留年月日
                item.ticket_sell_price = klook.formatPriceThousands(
                    item.ticket_sell_price,
                );
                // 存在促销 sku 的，才会显示促销标记
                let promotions = (item.price_items || []).filter(
                    priceItem => priceItem.is_promotion === true,
                );
                item.isPromotion = promotions.length > 0 ? true : false;
            });
            return list;
        },
        loadData(show, isRenewalPrice) {
            this.loading_shopping_cart = true;
            klook.ajaxGet(urlObj.agent_shoppingcart_info_api, resp => {
                this.loading_shopping_cart = false;

                if (resp.success) {
                    let result = resp.result || {};
                    if (isRenewalPrice) {
                        this.renewalPrice(result);
                        return false;
                    }
                    this.tableData = this.processData(
                        result.shoppingcart_items || [],
                    );
                    this.currencySymbol = this.getCurrencySymbolByCurrencyCode(
                        result.currency,
                    );
                    this.total_price = klook.formatPriceThousands(
                        result.total_price,
                    );
                }
            });
        },
    },
};
</script>

<style lang="scss">
.agent_header_shopping_cart_dialog {
    .klk-modal {
        padding: 0 !important;
        .klk-modal-body,
        .klk-modal-footer {
            padding: 0 !important;
        }
    }
    .count-down {
        width: 233px;
        position: absolute;
        top: 60px;
        left: 50%;
        transform: translate3d(-55%, 0, 0);
        text-align: center;
        z-index: 11;
    }
    .close_shopping_cart {
        float: right;
        cursor: pointer;
    }

    .agent_header_shopping_cart {
        background: #fff;
    }

    .agent_header_shopping_cart_header {
        height: 50px;
        text-align: left;
        background: var(--primary-color);
        padding: 15px 30px;
        font-size: 20px;
        line-height: 1;
        color: #fff;
    }
    .agent_header_shopping_cart_content_wrapper {
        padding: 0 20px;
        max-height: 452px;
        -ms-overflow-style: none;
        overflow: auto;
        white-space: normal;

        .klk_grid {
            td,
            th {
                border: none;
                background: #fff;
                padding: 20px;
                text-align: left;
                vertical-align: top;
                font-size: 16px;
                color: #424242;
                position: relative;
                &:last-child {
                    text-align: center;
                }
                .activity_name {
                    font-size: 16px;
                    color: #424242;
                    word-break: break-word;
                }
                .package_name {
                    font-size: 14px;
                    color: #9e9e9e;
                }
                .expired_or_unpublished {
                    font-size: 14px;
                    color: red;
                }
                .klk-icon {
                    color: #b8b8b8;
                    font-size: 16px;
                    cursor: pointer;
                    &:hover {
                        color: #000;
                    }
                }
            }
            tr {
                border-bottom: solid 1px #e0e0e0;
            }
            .activity_image {
                width: 127px;
                height: 100px;
            }
        }
    }
    .agent_header_shopping_cart_footer {
        width: 100%;
        height: 80px;
        background: #fafafa;
        padding: 20px 30px;
        text-align: right;
        position: relative;
        .total_price_wrapper {
            font-size: 24px;
            color: var(--primary-color);
            right: 200px;
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
        }
    }
    .disabled_cart {
        cursor: not-allowed;
        p,
        td {
            color: gray !important;
        }
    }
}
</style>
