<template>
    <div
        class="promotion-list"
        v-if="pageType !== 'HomePage' || promotionList.length > 0"
    >
        <div class="flash-sale-title">
            <h2 class="main-title">{{ $t('flash_sale') }}</h2>
            <div class="count-wrapper" v-if="!isFinished && isPromotion">
                <count-down
                    :start-time="startTime"
                    :end-time="endTime"
                    :finished-data="$t('promotion_finished_data')"
                    :show-text="$t('end_in')"
                    @finishCallback="finishCallback"
                >
                </count-down>
            </div>
            <router-link
                :to="{ name: 'promotion_list' }"
                class="right"
                v-if="pageType === 'HomePage' && promotionList.length > 3"
            >
                {{ $t('theme.see_more') }}
            </router-link>
        </div>
        <div
            class="list-wrapper"
            v-if="promotionList && promotionList.length > 0"
        >
            <activity-card
                :class="{ disabled_card: activity.is_blocked }"
                v-for="(activity, index) in showPromotionList"
                :key="index"
                :activity="activity"
                @clickWish="clickWish(activity.is_blocked)"
            >
            </activity-card>
        </div>
        <klk-no-data
            v-else
            class="card-list-empty"
            no_data_text_prop="No Data!"
        >
        </klk-no-data>
    </div>
</template>
<script>
import ActivityCard from '@/pages/tpl/ActivityCard.vue';
import CountDown from '@/pages/tpl/countdown.vue';
import urlObj from '@/common/url';
import { filterBlockData } from '@/common/util';
import { differenceInSeconds } from 'date-fns';

export default {
    props: {
        pageType: { type: String, default: '' },
    },
    data() {
        return {
            isFinished: true,
            startTime: '',
            endTime: '',
            promotionList: [],
            isPromotion: false,
            moduleName: 'FlashSale',
        };
    },
    components: {
        'count-down': CountDown,
        ActivityCard,
    },
    computed: {
        showPromotionList() {
            if (this.pageType === 'HomePage') {
                return this.promotionList.slice(0, 3);
            } else {
                return this.promotionList;
            }
        },
    },

    methods: {
        filterBlockData,
        loadData() {
            klook.ajaxGet(urlObj.get_promotion_activites, null, res => {
                this.$emit('finishedLoadData');
                if (res.success && res.result) {
                    this.promotionList = this.filterBlockData(
                        res.result.activities || [],
                    );
                    this.endTime = res.result.end_promotion_time;
                    this.formatActivities(this.promotionList);
                    if (this.promotionList.length > 0) {
                        this.isPromotion = this.promotionList[0].is_promotion;
                    }
                    this.isFinished =
                        differenceInSeconds(new Date(), this.endTime) >= 0;
                }
            });
        },
        filterActivity(list = []) {
            return list.filter(item => !item.is_blocked);
        },
        formatActivities(activities) {
            activities.map(activity => {
                activity.currencySymbol = this.getCurrencySymbolByCurrencyCode(
                    activity.currency,
                );
                activity.title = activity.activity_name;
                activity.showWords = true;
                activity.showDate = true;
                activity.activity_type = activity.activity_type || '';
                activity.id = activity.activity_id;
                return activity;
            });
        },
        updatePromotionData() {
            let ids = this.promotionList.map(item => item.id).join(',');

            klook.ajaxGet(urlObj.update_promotion_activites(ids), null, res => {
                if (res.success) {
                    this.promotionList = res.result.activities;
                    this.formatActivities(this.promotionList);
                }
            });
        },
        finishCallback() {
            this.updatePromotionData();
            this.$emit('flash-sale-finished');
        },
        clickWish(is_blocked) {
            if (is_blocked) {
                return;
            }
        },
    },

    created() {
        this.loadData();
    },
};
</script>
<style lang="scss">
.promotion-list {
    margin-bottom: 15px;
    .count-wrapper {
        display: inline-block;
    }
    .right {
        margin-top: 10px;
    }
    .list-wrapper {
        display: flex;
        flex-wrap: wrap;
        .activity-card {
            margin-right: 20px;
            margin-bottom: 20px;
            // flex: 1;

            &:nth-child(3n) {
                margin-right: 0;
            }

            .card-content {
                height: 142px;
            }
        }
    }

    .disabled_card {
        .card-content {
            color: grey;
            cursor: not-allowed;
            .card-content-title,
            .sell-price,
            .klk-icon-fast {
                color: grey;
            }
        }
        .image-wrapper {
            cursor: not-allowed;
        }
    }
}
</style>
