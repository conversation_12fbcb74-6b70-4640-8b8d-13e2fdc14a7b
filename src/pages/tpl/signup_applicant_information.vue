<template lang="html">
    <div class="form_wrapper">
        <klk-form
            class="klk_form"
            ref="signup_applicant_information_form"
            :rules="formRules"
            :model="signupApplicantInformationData"
        >
            <div class="signup_applicant_information_details">
                <div class="sub_title">
                    {{ $t('applicant_information') }}
                </div>
                <klk-form-item :label="$t('title')" class="title">
                    <klk-select v-model="signupApplicantInformationData.title">
                        <klk-option
                            v-for="item in ['MR', 'MRS', 'MISS']"
                            :key="item"
                            :value="item"
                            :label="item"
                        ></klk-option>
                    </klk-select>
                </klk-form-item>

                <klk-form-item
                    :label="$t('first_name')"
                    class="first_name"
                    prop="first_name"
                >
                    <klk-input
                        :maxlength="255"
                        v-model="signupApplicantInformationData.first_name"
                    ></klk-input>
                </klk-form-item>

                <klk-form-item
                    :label="$t('last_name')"
                    class="last_name"
                    prop="last_name"
                >
                    <klk-input
                        :maxlength="255"
                        v-model="signupApplicantInformationData.last_name"
                    ></klk-input>
                </klk-form-item>
                <klk-form-item
                    :label="$t('job_description')"
                    class="job_description"
                    prop="duties"
                >
                    <klk-input
                        :maxlength="255"
                        v-model="signupApplicantInformationData.duties"
                    ></klk-input>
                </klk-form-item>
            </div>
        </klk-form>
    </div>
</template>

<script>
import formRules from '@/common/form_rules';

export default {
    name: 'SignupTravelAgentDetails',
    data() {
        return {
            signupApplicantInformationData: {
                title: 'MR',
                first_name: '',
                last_name: '',
                duties: '',
            },
        };
    },
    created() {
        this.formRules = formRules.call(this);
    },
    methods: {
        getFormData() {
            return Object.assign({}, this.signupApplicantInformationData);
        },
    },
};
</script>
<style lang="scss"></style>
