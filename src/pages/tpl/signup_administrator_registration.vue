<template>
    <div class="form_wrapper">
        <klk-form
            ref="signup_administrator_registration"
            :rules="formRules"
            :model="signupAdministratorRegistrationData"
            class="klk_form"
        >
            <div class="signup_administrator_registration">
                <div class="sub_title">
                    {{ $t('administrator_registration') }}
                </div>
                <klk-form-item
                    :label="$t('email')"
                    class="email_address width-250"
                    prop="email_account"
                >
                    <!-- prettier-ignore -->
                    <klk-input
                        :maxlength="255"
                        :placeholder="$t('signup_email_placeholder')"
                        v-model=" signupAdministratorRegistrationData.email_account"
                        :disabled="disabled"
                    ></klk-input>
                </klk-form-item>
                <input
                    type="text"
                    style="display:none;"
                /><!-- shut down auto complete -->
                <klk-form-item
                    :label="$t('signup_password')"
                    class="pwd"
                    prop="password"
                >
                    <klk-input
                        :maxlength="255"
                        :type="'password'"
                        :placeholder="$t('signup_pwd_placeholder')"
                        v-model="signupAdministratorRegistrationData.password"
                        :disabled="disabled"
                    ></klk-input>
                </klk-form-item>

                <klk-form-item
                    :label="$t('signup_confirm_password')"
                    class="repeat_pwd"
                    prop="repeatPwd"
                >
                    <klk-input
                        :maxlength="255"
                        type="password"
                        :placeholder="$t('signup_confirm_pwd_placeholder')"
                        v-model="signupAdministratorRegistrationData.repeatPwd"
                        :disabled="disabled"
                    ></klk-input>
                </klk-form-item>
            </div>
        </klk-form>
        <div class="form_tip step1_form_tip">
            {{ $t('signup_step1_tip', [domainName]) }}
        </div>
    </div>
</template>

<script>
import formRules from '@/common/form_rules';
import { JV_NAME } from '~/jv_config';

export default {
    name: 'signupAdministratorRegistration',
    props: {
        disabled: {
            type: Boolean,
            default: false,
        },
        baseInfo: {
            type: Object,
            default: function() {
                return {};
            },
        },
    },
    data() {
        return {
            signupAdministratorRegistrationData: {
                email_account: '',
                password: '',
                repeatPwd: '',
            },
        };
    },
    computed: {
        domainName() {
            return JV_NAME[window.KLK_JV_NAME];
        },
    },
    created() {
        this.formRules = formRules.call(this);
        if (this.disabled) {
            this.signupAdministratorRegistrationData = Object.assign(
                {},
                this.baseInfo,
            );
        }
    },
    methods: {
        getFormData() {
            return Object.assign({}, this.signupAdministratorRegistrationData, {
                phone_number: this.signupAdministratorRegistrationData
                    .phone_number,
                password: klook.md5(
                    this.signupAdministratorRegistrationData.password,
                ),
            });
        },
    },
};
</script>

<style lang="scss">
.klk_form_item_content {
    position: relative;

    .switch-password-visibility {
        font-size: 24px;
        position: absolute;
        top: 33px;
        right: 10px;
        cursor: pointer;
        color: #888;
    }
}
</style>
