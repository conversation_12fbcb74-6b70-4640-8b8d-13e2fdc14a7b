<template>
    <klk-modal
        class="otp-dialog"
        :open.sync="visible"
        :show-default-footer="false"
        :overlay-closable="false"
        closable
        @on-close="visible = false"
    >
        <div class="otp-dialog-panel" v-loading="validateLoading || sendingOtp">
            <div v-if="title" class="title">{{ title }}</div>
            <div v-if="description" class="description">
                <span class="des-text">{{ description }}</span>
                <div class="send-address">{{ rcv }}</div>
            </div>
            <klk-form @submit.native.prevent :model="{ code: code }">
                <OtpInputFormItem
                    ref="codeInputFormItem"
                    v-model="code"
                    :input-attrs="{
                        value: code,
                        inputSize: 'big',
                        inputDisabled: disableResend,
                    }"
                    :form-item-attrs="{
                        prop: 'code',
                        errorMessage,
                        inputDisabled: disableResend,
                    }"
                    @on-complete="handleCodeInputComplete"
                />
            </klk-form>
            <div class="operate-panel">
                <a
                    v-show="canResend && !sendingOtp"
                    :class="[
                        'btn',
                        'resend-btn',
                        { 'disabled-resend-btn': disableResend },
                    ]"
                    href="javascript:;"
                    @click="handleResendClick"
                >
                    {{ $t('otp.resend') }}
                </a>
                <klkPlatformCountDown
                    v-show="!canResend && endTime"
                    :type="3"
                    :end-time="endTime"
                    :end-call-back="countDownEnd"
                    class="btn count-down"
                >
                    <p
                        slot-scope="{ countDownTime }"
                        v-html="countDownText(countDownTime)"
                    />
                </klkPlatformCountDown>
            </div>
            <div class="help-tip-container">
                <div class="help-tip-msg">{{ helpTip }}</div>
                <klk-poptip
                    class="help-poptip"
                    :content="helpPoptipContent"
                    placement="bottom"
                >
                    <IconQuestion
                        class="help-tip-icon"
                        theme="outline"
                        size="20"
                        fill="#757575"
                    />
                </klk-poptip>
            </div>
        </div>
    </klk-modal>
</template>

<script>
import urlObj from '@/common/url';
import { IconQuestion } from '@klook/klook-icons';
import klkPlatformCountDown from '@klook/platform-countdown';
import OtpInputFormItem from '@/components/otp/otp-input-form-item.vue';

export default {
    name: 'OtpDialog',
    components: {
        IconQuestion,
        klkPlatformCountDown,
        OtpInputFormItem,
    },
    data() {
        return {
            visible: false,
            rcv: '',
            email: '',
            code: '',
            canResend: false,
            disableResend: false,
            errorMessage: '',
            sendingOtp: false,
            validateLoading: false,
            endTime: 0,
        };
    },
    computed: {
        isCNSite() {
            return klook.isCNSite;
        },
        title() {
            return this.isCNSite
                ? this.$t('otp.phone.title')
                : this.$t('otp.email.title');
        },
        description() {
            return this.isCNSite
                ? this.$t('otp.phone.desc')
                : this.$t('otp.email.desc');
        },
        helpTip() {
            return this.isCNSite
                ? this.$t('otp.phone.help_tip')
                : this.$t('otp.email.help_tip');
        },
        helpPoptipContent() {
            return this.isCNSite
                ? this.$t('otp.phone.help_desc')
                : this.$t('otp.email.help_desc');
        },
    },
    methods: {
        async show(rcv, email) {
            this.rcv = rcv;
            this.email = email;
            await this.sendOtp();
        },
        async sendOtp() {
            if (this.sendingOtp) {
                return false;
            }
            this.disableResend = false;
            this.sendingOtp = true;
            this.$refs.codeInputFormItem &&
                this.$refs.codeInputFormItem.focusInput();
            this.endTime = 0;
            try {
                klook.ajaxPost(
                    urlObj.agent_send_otp,
                    { email: this.email },
                    res => {
                        this.sendingOtp = false;
                        this.visible = true;
                        if (res && res.success && res.result) {
                            this.endTime = res.result.next_timestamp_sec || '';
                            this.canResend = false;
                            this.clearError();
                        } else {
                            this.canResend = true;
                            if (
                                res &&
                                res.error &&
                                res.error.code === 'P0802'
                            ) {
                                this.disableResend = true;
                            }

                            this.handleError(res.error || null);
                        }
                    },
                );
            } catch (error) {
                this.sendingOtp = false;
                this.canResend = true;
                this.handleError(null);
            }
        },

        countDownEnd() {
            this.canResend = true;
        },

        async codeInputComplete(val) {
            this.clearError();
            try {
                this.validateLoading = true;
                klook.ajaxPost(
                    urlObj.agent_validate_otp,
                    { email: this.email, verification_code: val },
                    res => {
                        this.validateLoading = false;
                        if (res && res.success) {
                            this.visible = false;
                            this.$emit('otp-validate-success');
                        } else {
                            this.handleError(res.error || null);
                        }
                    },
                );
            } catch (err) {
                this.validateLoading = false;
                this.handleError(null);
            }
        },

        handleError(error) {
            this.errorMessage = error ? error.message : 'error';
        },

        clearError() {
            this.errorMessage = '';
        },

        handleResendClick() {
            if (this.disableResend) {
                return;
            }
            this.code = '';
            this.sendOtp();
        },

        countDownText(time) {
            return this.$t('otp.resend_countdown', { 0: time.s });
        },

        handleCodeInputComplete(val) {
            this.codeInputComplete(val);
        },
    },
};
</script>

<style lang="scss" scoped>
.otp-dialog-panel {
    color: #212121;

    .title {
        font-size: 20px;
        font-weight: 600;
    }

    .description {
        display: flex;
        flex-wrap: wrap;
        margin-top: 12px;
        font-size: 16px;
        font-weight: 400;
        margin-bottom: 16px;
    }

    .operate-panel {
        display: flex;
        margin-top: 12px;
        align-items: center;
    }

    .btn {
        font-size: 16px;
        font-weight: 400;
        color: #212121;
    }

    .resend-btn {
        text-decoration: underline;
    }

    .disabled-resend-btn {
        color: #a8a8a8;
        cursor: not-allowed;
    }

    .count-down {
        color: #a8a8a8;
    }

    ::v-deep .time-number {
        display: inline-block;
        width: 23px;
        text-align: center;
    }
}

.help-tip-container {
    margin-top: 12px;
    color: #757575;
    display: flex;
    align-items: center;

    .help-tip-msg {
        margin-left: auto;
        font-size: 16px;
        font-weight: 400;
        margin-right: 4px;
    }

    ::v-deep .klk-poptip-popper-inner {
        margin: 12px 16px;
        padding: 0;
    }
}

.i-icon {
    display: flex;
}
</style>
