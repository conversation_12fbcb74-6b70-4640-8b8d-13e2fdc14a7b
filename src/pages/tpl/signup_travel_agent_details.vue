<template lang="html">
    <div class="form_wrapper">
        <klk-form
            ref="signup_travel_agent_details_form"
            :rules="formRules"
            class="klk_form"
            :model="signupTravelAgentDetailsData"
        >
            <div class="signup_travel_agent_details">
                <div class="sub_title">
                    {{ $t('travel_agent_details') }}
                </div>
                <klk-form-item
                    :label="$t('country_or_regional_code')"
                    class="country_or_regional_code"
                    prop="country_code"
                >
                    <klk-select
                        v-model="signupTravelAgentDetailsData.country_code"
                        @change="changeCountryCode"
                        :placeholder="$t('global.select.palceholder')"
                    >
                        <klk-option
                            v-for="(item, index) in countryCodeObj.main"
                            :value="item.country_code"
                            :key="index"
                            :label="item.country_name"
                        ></klk-option>
                        <klk-option-group
                            v-if="!isCN"
                            :label="$t('other_country_or_regional')"
                        >
                            <klk-option
                                v-for="(item, index) in countryCodeObj.others ||
                                    []"
                                :value="item.country_code"
                                :label="item.country_name"
                                :key="index"
                            ></klk-option>
                        </klk-option-group>
                    </klk-select>
                </klk-form-item>
                <klk-form-item
                    :label="$t('phone_number')"
                    class="mobile_number"
                    prop="phone_number"
                >
                    <klk-input
                        :maxlength="255"
                        v-model="signupTravelAgentDetailsData.phone_number"
                    ></klk-input>
                </klk-form-item>

                <klk-form-item
                    :label="$t('language')"
                    class="language"
                    prop="language"
                >
                    <klk-select v-model="signupTravelAgentDetailsData.language">
                        <klk-option
                            v-for="(item, key) in langOptions"
                            :key="key"
                            :value="item.iso.replace('-', '_')"
                            :label="$t(`global.${key}`)"
                        ></klk-option>
                    </klk-select>
                </klk-form-item>

                <klk-form-item
                    :label="$t('signup_currency_input')"
                    class="currency_code"
                    prop="currency"
                >
                    <klk-select v-model="signupTravelAgentDetailsData.currency">
                        <!-- 新增八种货币 覆盖之前对IDR屏蔽 -->
                        <klk-option
                            v-for="item in newCurrencyList"
                            :key="item.title"
                            :value="item.value"
                            :label="item.title"
                        ></klk-option>
                    </klk-select>
                </klk-form-item>

                <klk-form-item :label="$t('company_name')" prop="company_name">
                    <klk-input
                        :maxlength="255"
                        v-model="signupTravelAgentDetailsData.company_name"
                    ></klk-input>
                </klk-form-item>

                <klk-form-item
                    :label="$t('company_address')"
                    class="address"
                    prop="address"
                >
                    <klk-input
                        :maxlength="255"
                        v-model="signupTravelAgentDetailsData.address"
                    ></klk-input>
                </klk-form-item>
                <klk-form-item
                    :label="$t('website_url')"
                    class="website_url"
                    prop="website_url"
                >
                    <klk-input
                        :maxlength="255"
                        v-model="signupTravelAgentDetailsData.website_url"
                    ></klk-input>
                </klk-form-item>
            </div>
        </klk-form>

        <div class="form_tip currency_form_tip">
            {{ $t('signup_currency_input_tip') }}
        </div>
    </div>
</template>

<script>
import formRules from '@/common/form_rules';
import urlObj from '@/common/url';
import { AllCurrencies, CNCurrencies } from '../../../config';

export default {
    name: 'SignupTravelAgentDetails',
    data() {
        return {
            countryCodeObj: {
                main: [],
                others: [],
            },
            signupTravelAgentDetailsData: {
                phone_number: '',
                language: 'en_US', //默认英语
                currency: 'USD', //默认美元
                company_name: '',
                duties: '',
                address: '',
                website_url: '',
                country_code: '',
            },
        };
    },
    computed: {
        // 仅在注册有区分，单独写在注册里面
        currencyList() {
            const currencies = this.isCN ? CNCurrencies : AllCurrencies;
            return Object.values(currencies).map(item => ({
                value: item.currency,
                title: `${this.$t(item.currencyName)} | ${item.currencySymbol}`,
                currencySymbol: item.currencySymbol,
                currency: item.currency,
            }));
        },
        newCurrencyList() {
            return this.currencyList.filter(item => {
                if (
                    item.value === 'IDR' ||
                    item.value === 'INR' ||
                    item.value === 'VND'
                ) {
                    return false;
                }
                return true;
            });
        },
        isCN() {
            return klook.isCNSite;
        },
    },
    created() {
        this.formRules = formRules.call(this);
    },
    mounted() {
        this.loadCountryCode();
    },
    methods: {
        changeCountryCode() {
            this.$emit(
                'changeCountryCode',
                this.signupTravelAgentDetailsData.country_code,
            );
        },
        getFormData() {
            return Object.assign({}, this.signupTravelAgentDetailsData, {
                phone_number: this.signupTravelAgentDetailsData.phone_number,
            });
        },
        loadCountryCode() {
            klook.ajaxGet(urlObj.agent_account_countries, resp => {
                if (resp.success) {
                    this.countryCodeObj = resp.result || {};
                }
            });
        },
    },
};
</script>

<style lang="scss"></style>
