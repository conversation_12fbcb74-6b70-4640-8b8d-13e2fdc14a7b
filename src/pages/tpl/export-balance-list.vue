<template>
    <div class="export-balance-list">
        <div class="action">
            <klk-button
                type="secondary"
                icon="icon_edit_upload"
                class="export-balance-list-button"
                @click="showExportOptionDialog"
            >
                {{ $t('export') }}
            </klk-button>
        </div>
        <klk-modal
            :open.sync="dialog_export_option_show"
            :overlay-closable="true"
            :show-default-footer="false"
            class="dialog_export_option"
        >
            <div class="export_by_date">
                <div class="export_title">
                    {{ $t('agent_export_date') }} （{{
                        $t('agent_export_date_tips')
                    }}）
                </div>
                <div class="date">
                    <div class="calender_input_wrapper">
                        <klk-single-date-picker
                            v-model="start_date"
                            :isDayBlocked="disabledStartDateBooking"
                        >
                        </klk-single-date-picker>
                    </div>
                    <div class="calender_input_wrapper">
                        <klk-single-date-picker
                            v-model="end_date"
                            :disabled="!start_date"
                            :isDayBlocked="disabledEndDateBooking"
                        >
                        </klk-single-date-picker>
                    </div>
                </div>
                <div class="error_msg">{{ error_message }}</div>
                <div class="btn_group export_action">
                    <klk-button
                        type="secondary"
                        @click="dialog_export_option_show = false"
                    >
                        {{ $t('cancel') }}
                    </klk-button>
                    <klk-button @click="ordinaryExport()" :loading="loading">
                        {{ $t('confirm') }}
                    </klk-button>
                </div>
            </div>
        </klk-modal>

        <klk-modal
            :open.sync="dialog_export_progress_show"
            class="export-progress-dialog"
        >
            <div class="export-progress">
                <div class="loader">
                    <klk-icon class="icon_other_loading" type="circular" />
                </div>
                <span
                    >{{ $t('export_progress_tip') }}&nbsp;{{
                        dialog_export_progress_info
                    }}</span
                >
            </div>
        </klk-modal>
    </div>
</template>

<script>
import urlObj from '@/common/url';
import {
    subMonths,
    startOfDay,
    isBefore,
    parse,
    addMonths,
    isAfter,
    format,
} from 'date-fns';

const errorCodeMap = {
    B0003: 'export_error_no_logs',
    P0009: 'export_error_over_six_months',
};

export default {
    name: 'export-booking-list',
    props: {},
    data() {
        return {
            dialog_export_option_show: false,
            dialog_export_progress_show: false,
            dialog_export_progress_info: '',
            start_date: subMonths(startOfDay(new Date()), 1),
            end_date: startOfDay(new Date()),
            export_by_date_show: false,
            active: -1,
            loading: false,
            error_message: '',
        };
    },
    watch: {
        start_date(val) {
            // start date + 6month > today => end_date = today
            const today = startOfDay(new Date());
            const addSixMonDay = addMonths(this.start_date, 6);
            if (new Date(addSixMonDay).getTime() < new Date(today).getTime()) {
                this.end_date = addSixMonDay;
            }
            if (val && this.end_date && isAfter(val, this.end_date)) {
                this.end_date = startOfDay(new Date());
            }
        },
    },

    methods: {
        disabledStartDateBooking(day) {
            return isBefore(new Date(), day);
        },
        disabledEndDateBooking(day) {
            // limit today && max date | over start day
            if (!this.end_date) return false;
            const max_date = addMonths(parse(this.start_date), 6);
            const date = parse(this.start_date);
            return (
                isAfter(date, day) ||
                isBefore(max_date, day) ||
                isBefore(new Date(), day)
            );
        },

        showExportOptionDialog() {
            this.active = -1;

            this.dialog_export_option_show = true;
            this.$emit('export-data');
        },
        ordinaryExport() {
            let formList;
            formList = {
                start_time: format(this.start_date, 'YYYY-MM-DD'),
                end_time: format(this.end_date, 'YYYY-MM-DD'),
            };

            if (!window.Blob || !window.FileReader) {
                const inputs = Object.keys(formList).reduce(function(
                    prev,
                    key,
                ) {
                    return (
                        prev +
                        '<input name="' +
                        key +
                        '" value="' +
                        formList[key] +
                        '" type="hidden"/>'
                    );
                },
                '');
                const form = document.createElement('form');
                form.action = urlObj.export_agent_order_record;
                form.method = 'post';
                form.innerHTML = inputs;
                document.body.appendChild(form);
                form.submit();
                // setTimeout(() => document.body.removeChild(form), 500);
            } else {
                try {
                    this.dialog_export_progress_info = '';
                    this.pollingExportBookingRecords(formList);
                } catch (e) {
                    this.dialog_export_progress_show = false;
                    console.log('Error', e);
                }
            }
        },

        getPostData(sendData) {
            let postData;
            if (sendData.taskID) {
                postData = Object.keys(sendData)
                    .map(key => `${key}=${sendData[key]}`)
                    .join('&');
            } else {
                postData = JSON.stringify(sendData);
            }
            return postData;
        },
        pollingExportBookingRecords(sendData, taskID) {
            sendData = sendData || {};
            if (taskID) {
                sendData.taskID = taskID;
            }
            this.loading = true;
            const postData = this.getPostData(sendData);
            const xhr = new XMLHttpRequest();
            xhr.onreadystatechange = () => {
                this.loading = false;
                if (xhr.readyState === 4) {
                    if (xhr.status !== 200) {
                        this.dialog_export_progress_show = false;
                        console.log('Error Status: ' + xhr.status);
                        return;
                    }
                    const reader = new FileReader();
                    reader.onload = () => {
                        try {
                            const resJson = JSON.parse(reader.result);
                            this.loading = false;
                            if (resJson.success && resJson.result) {
                                this.error_message = '';
                                console.log(resJson.result);
                                if (!resJson.result.total) {
                                    this.dialog_export_progress_show = false;
                                    this.$message({
                                        type: 'warning',
                                        message: this.$t('export_no_result'),
                                    });
                                } else {
                                    this.dialog_export_option_show = false;
                                    this.dialog_export_progress_show = true;
                                    this.dialog_export_progress_info = `${resJson.result.generated}/${resJson.result.total}`;
                                    setTimeout(
                                        this.pollingExportBookingRecords.bind(
                                            this,
                                            {},
                                            resJson.result.task_id,
                                        ),
                                        1000,
                                    );
                                }
                            } else {
                                // this.dialog_export_option_show = false;
                                this.dialog_export_progress_show = false;
                                const code =
                                    resJson.error && resJson.error.code;

                                if (['B0003', 'P0009'].includes(code)) {
                                    this.error_message = this.$t(
                                        errorCodeMap[code],
                                    );
                                } else {
                                    this.error_message =
                                        resJson.desc ||
                                        (resJson.error &&
                                            resJson.error.message);
                                }
                            }
                        } catch (e) {
                            if (window.Blob) {
                                const blob = new Blob([xhr.response], {
                                    type: 'application/vnd.ms-excel',
                                });
                                const downloadUrl = (
                                    window.URL || window.webkitURL
                                ).createObjectURL(blob, {
                                    type: 'data:attachment/xlsx',
                                });
                                const a = document.createElement('a');
                                const disposition = xhr.getResponseHeader(
                                    'Content-Disposition',
                                );
                                let filename = '';
                                if (
                                    disposition &&
                                    disposition.indexOf('attachment') !== -1
                                ) {
                                    const matches = /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/.exec(
                                        disposition,
                                    );
                                    if (matches !== null && matches[1]) {
                                        filename = matches[1].replace(
                                            /['"]/g,
                                            '',
                                        );
                                    }
                                }
                                this.dialog_export_progress_show = false;
                                if (navigator.msSaveOrOpenBlob) {
                                    navigator.msSaveOrOpenBlob(blob, filename);
                                    return;
                                }
                                a.download = filename;
                                a.href = downloadUrl;
                                document.body.appendChild(a);
                                a.click();
                                setTimeout(() => {
                                    document.body.removeChild(a);
                                }, 500);
                            }
                        }
                    };
                    reader.readAsText(xhr.response);
                }
            };
            xhr.onerror = e => {
                console.log(e);
                this.dialog_export_progress_show = false;
            };
            const url = urlObj.agent_balance_export;
            if (sendData.taskID) {
                xhr.open('GET', `${url}?${postData}`);
            } else {
                xhr.open('POST', url);
            }
            xhr.responseType = 'blob';
            xhr.setRequestHeader('Content-Type', 'application/json');
            xhr.send(sendData.taskID ? '' : postData);
        },
    },
};
</script>

<style lang="scss" scoped>
.error_msg {
    color: #fd5252;
}
.dialog_export_option {
    .klk_dialog {
        width: 680px;
        overflow: revert;
    }
}

.export-balance-list-button {
    position: absolute;
    right: 25px;
}
.export-progress-dialog {
    .export-progress {
        text-align: center;
        padding: 20px 24px;

        .loader {
            position: relative;
            margin: 0 auto 25px;
            width: 30px;

            &:before {
                content: '';
                display: block;
                padding-top: 100%;
            }
        }

        .circular {
            animation: rotate 0.5s linear infinite;
            height: 100%;
            transform-origin: center center;
            width: 100%;
            position: absolute;
            top: 0;
            bottom: 0;
            left: 0;
            right: 0;
            margin: auto;
        }

        @keyframes rotate {
            100% {
                transform: rotate(360deg);
            }
        }
    }
}

.btn_group {
    margin-top: 30px;
}

.export_by_date {
    .export_title {
        margin: 10px 0;
    }

    .date {
        .calender_input_wrapper {
            margin: 10px 0;
        }
    }
}
</style>
