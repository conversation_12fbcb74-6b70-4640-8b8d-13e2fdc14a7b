<template lang="html">
    <ul class="side_menu" v-loading="load_side_menu">
        <li
            v-for="category in categoryList"
            :key="category.id"
            :class="{ selected: categoryIdMap[category.id], item: true }"
        >
            <p class="item_desc" @click="selectCategoryBy(category.id)">
                <!-- <klk-icon
                    class="side-icon"
                    size="16"
                    :type="getIconByTemplateId(category.id)"
                    :class="getIconByTemplateId(category.id)"
                ></klk-icon
                >&nbsp; -->
                <klk-icon
                    size="16"
                    type="icon_navigation_chevron_right_xs"
                ></klk-icon>
                {{ category.name }}
            </p>
            <ul
                class="sub_list"
                v-if="category.children && category.children.length > 0"
            >
                <li
                    v-for="sub_item in category.children"
                    :key="sub_item.id"
                    :class="{
                        selected: categoryIdMap[category.id] === sub_item.id,
                        sub_item: true,
                    }"
                    @click="selectCategoryBy(category.id, sub_item.id)"
                >
                    <p>{{ sub_item.name }}</p>
                </li>
            </ul>
        </li>
    </ul>
</template>

<script>
export default {
    name: 'SideMenu',
    props: {
        list: {
            type: Array,
            default: () => [],
        },
        city_id: {
            required: true,
        },
        categoryList: {
            type: Array,
            default: () => [],
        },
    },
    data() {
        return {
            categoryIdMap: {},
            load_side_menu: false,
            ids: [],
        };
    },
    methods: {
        // 产品：暂时不展示
        // getIconByTemplateId(template_id) {
        //     // need to do
        //     let id_icon_map = {
        //         1: 'tickets',
        //         2: 'tours',
        //         3: 'activity',
        //         4: 'food',
        //         5: 'wifi',
        //     };
        //     return id_icon_map[template_id];
        // },
        selectCategoryBy(categoryId, id) {
            this.resetData();
            this.$set(this.categoryIdMap, categoryId, id ? id : -1);
            Object.keys(this.categoryIdMap).forEach(key => {
                if (this.categoryIdMap[key] == -1) {
                    this.ids.push(+key);
                } else {
                    this.ids.push(this.categoryIdMap[key]);
                }
            });
            this.$emit('on-search-by-category', this.ids.join(','));
        },
        resetData() {
            this.categoryIdMap = {};
            this.ids = [];
        },
    },
};
</script>

<style lang="scss">
.side_menu {
    width: 280px;
    min-height: 46px;
    border: solid 1px #e0e0e0;
    position: relative;
    text-align: left;
    font-size: 14px;
    color: #333;
    border-radius: 2px;

    .item {
        width: 100%;
        position: relative;
        height: 46px;
        line-height: 46px;
        background: #fff;
        padding-left: 30px;
        border-bottom: solid 1px #e0e0e0;

        .side-icon {
            margin-right: 8px;
        }

        svg {
            color: #9e9e9e;
        }

        i {
            left: 10px;
            color: #888;
            vertical-align: middle;
        }

        &.selected {
            color: var(--primary-color);

            i::before {
                color: var(--primary-color);
            }
        }

        &:last-child {
            border-bottom: none;
        }

        &:nth-child(n + 4) {
            &:hover {
                .sub_list {
                    display: block;
                    bottom: -1px;
                    top: auto;
                }
            }
        }

        &:hover {
            color: var(--primary-color);
            width: 281px;
            border-right: none;

            i::before {
                color: var(--primary-color);
            }

            &::before {
                content: '';
                position: absolute;
                top: 0;
                right: 2px;
                width: 1px;
                height: 100%;
                background: #fff;
                z-index: 3;
            }

            svg {
                color: var(--primary-color);
            }

            .sub_list {
                display: block;
            }

            .klk-icon-icon_navigation_chevron_right_xs {
                display: none;
            }
        }

        .sub_list {
            display: none;
            position: absolute;
            left: 278px;
            top: -1px;
            background: #fff;
            width: auto;
            white-space: nowrap;
            border: solid 1px #d5d5d5;
            padding: 10px 20px;
            z-index: 3;
            color: #424242;

            .sub_item {
                height: 30px;
                line-height: 30px;
                font-size: 14px;

                &.selected {
                    color: var(--primary-color);
                }

                &:hover {
                    color: var(--primary-color);
                }
            }
        }

        .klk-icon-icon_navigation_chevron_right_xs {
            right: 10px;
            left: auto;
            top: 16px;
            position: absolute;
        }
    }
}
</style>
