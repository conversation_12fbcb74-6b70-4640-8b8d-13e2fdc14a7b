<template>
    <div class="amp-header">
        <div class="header__container" id="headerContainer">
            <div class="top">
                <router-link
                    class="header__logo--link"
                    :to="{ name: 'agent_index' }"
                >
                    <img :src="jvLogoSrc" class="img__logo" />
                </router-link>
                <klk-poptip
                    v-if="isLoggedIn && isDRPAgent"
                    :value="showCityExplore"
                    :offset="[0, 12]"
                    class="header__search"
                    :arrow="false"
                    trigger="none"
                    :max-width="868"
                    :width="868"
                    :max-height="388"
                    placement="bottom-start"
                >
                    <klk-input
                        class="header__search--input"
                        style-type="filled"
                        size="small"
                        :placeholder="searchPlaceholder"
                        v-model.trim="searchKeywordInput"
                        @keydown.enter="searchByKeyword"
                        @input="showCityExplore = true"
                        @focus="showCityExplore = true"
                        @blur="showCityExplore = false"
                    >
                        <klk-icon
                            slot="append"
                            type="icon_edit_search_s"
                            size="20"
                            @click="searchByKeyword"
                        ></klk-icon>
                    </klk-input>
                    <header-city-explore
                        slot="content"
                        ref="cityExplore"
                    ></header-city-explore>
                </klk-poptip>
                <div class="header__nav h-full">
                    <div class="header__nav__item nav__user" v-if="isLoggedIn">
                        <klk-poptip
                            :offset="[0, 30]"
                            placement="bottom-end"
                            :max-height="500"
                        >
                            <span class="nav--title"
                                >{{ userName
                                }}<klk-icon
                                    type="icon_navigation_chevron_down"
                                    size="14"
                                ></klk-icon
                            ></span>
                            <ul slot="content" class="nav--list">
                                <li
                                    style="position: relative"
                                    v-for="(nav, index) in navList"
                                    :key="index"
                                    @click="!!nav.fn ? nav.fn() : ''"
                                >
                                    <router-link
                                        v-if="nav.path"
                                        :to="{ name: nav.path }"
                                    >
                                        <klk-icon
                                            size="20"
                                            :type="nav.icon"
                                            class="agent_icon"
                                        ></klk-icon>
                                        <span class="nav-item-label"
                                            >{{ nav.label }}
                                        </span>
                                        <klk-icon
                                            style="position: absolute; top:14px;right: 20px"
                                            v-show="nav.path === $route.name"
                                            type="icon_other_check_xs"
                                            color="#23AC38"
                                        ></klk-icon>
                                    </router-link>
                                    <a
                                        v-else-if="nav.mailto"
                                        :href="`mailto:${nav.mailto}`"
                                    >
                                        <klk-icon
                                            size="20"
                                            :type="nav.icon"
                                            class="agent_icon"
                                        ></klk-icon>
                                        <span class="nav-item-label"
                                            >{{ nav.label }}
                                        </span>
                                    </a>
                                </li>
                                <li>
                                    <span @click="logout">
                                        <klk-icon
                                            type="login-out"
                                            size="20"
                                            class="agent_icon"
                                        ></klk-icon>
                                        {{ $t('log_out') }}</span
                                    >
                                </li>
                            </ul>
                        </klk-poptip>
                    </div>
                    <div class="header__nav__item nav__lang">
                        <klk-poptip
                            :offset="[0, 30]"
                            placement="bottom"
                            :max-height="500"
                        >
                            <span class="nav--title"
                                >{{ $t(`global.${currentLang}`)
                                }}<klk-icon
                                    type="icon_navigation_chevron_down"
                                    size="14"
                                ></klk-icon
                            ></span>
                            <ul slot="content" class="nav--list locale-list">
                                <li
                                    v-for="(value, key) in langOptionList"
                                    :key="key"
                                    @click="setLanguage(key)"
                                    :class="{ active: key === currentLang }"
                                >
                                    {{ $t(`global.${key}`) }}

                                    <klk-icon
                                        v-if="key === currentLang"
                                        type="icon_other_check_xs"
                                        color="#23AC38"
                                        class="agent-icon"
                                    ></klk-icon>
                                </li>
                            </ul>
                        </klk-poptip>
                    </div>
                    <div v-if="isLoggedIn" class="header__nav__item">
                        <klk-poptip
                            :offset="[0, 30]"
                            placement="bottom"
                            :max-height="500"
                        >
                            <span class="nav--title"
                                >{{ $t('manual')
                                }}<klk-icon
                                    type="icon_navigation_chevron_down"
                                    size="14"
                                ></klk-icon
                            ></span>
                            <ul slot="content" class="nav--list">
                                <li
                                    v-for="(value, key) in manualList()"
                                    :key="key"
                                    @click="toManual(value.path)"
                                >
                                    {{ value.label }}
                                </li>
                            </ul>
                        </klk-poptip>
                    </div>
                </div>
            </div>
        </div>
        <div class="entry-wrapper" v-if="isLoggedIn && isDRPAgent">
            <router-link
                class="entry"
                :class="{ active: $route.name === 'agent_index' }"
                :to="{ name: 'agent_index' }"
            >
                <span>
                    <klk-icon type="home" size="18"></klk-icon>
                    {{ $t('global.text.index') }}
                </span>
            </router-link>
            <span style="color: #B2B2B2">|</span>
            <span
                class="entry"
                :class="{ active: $route.name === 'hotels' }"
                @click="goHotels"
            >
                <klk-icon type="hotel" size="20"></klk-icon>
                {{ $t('hotels') }}
            </span>
            <span v-if="showZannadu" style="color: #B2B2B2">|</span>
            <span
                v-if="showZannadu"
                class="entry"
                :class="{ active: $route.name === 'auth_middleware' }"
                @click="goZanNaDu"
            >
                <klk-icon type="hotel" size="20"></klk-icon>
                {{ $t('znd_title') }}
            </span>
        </div>
    </div>
</template>

<script>
import { addDays, format } from 'date-fns';
import urlObj from '@/common/url';
import HeaderShoppingCard from './agent_header_shopping_cart.vue';
import HeaderCityExplore from './signup_multiple_destination_selection.vue';

export default {
    name: 'CommonHeader',
    data() {
        return {
            showCityExplore: false,
            searchKeywordInput: '',
            searchPlaceholder: this.$t('desktop.index.search_placeholder'),
        };
    },
    computed: {
        showZannadu() {
            return !!klook.isCNSite;
        },
        showShoppingCart() {
            return this.$store.state.shoppingCartStatus;
        },
        jvLogoSrc() {
            return window.KLK_JOINT_VENTRUES.logo_url;
        },
        accountType() {
            return window.KLK_USER_INFO.agent_type;
        },
        userName() {
            return `${window.KLK_USER_INFO.first_name || ''} ${window
                .KLK_USER_INFO.family_name || ''}`;
        },
        permission() {
            return window.KLK_USER_INFO.permissions || {};
        },
        currentLang() {
            return window.KLK_LANG || 'en';
        },
        isLoggedIn() {
            return klook.isLoggedIn();
        },
        isDRPAgent() {
            return !klook.isDRPAgent;
        },
        navList() {
            return [
                {
                    label: this.$t('my_account'),
                    path: 'home',
                    icon: 'icon_user_avatar',
                },
                {
                    label: this.$t('voucher_details'),
                    path: 'voucher_info',
                    icon: 'voucher',
                },
                {
                    label: this.$t('balance'),
                    path: 'credits',
                    icon: 'balance',
                },
                {
                    label: this.$t('bookings'),
                    path: 'bookings',
                    icon: 'bookings',
                },
                {
                    label: this.$t('history_bookings'),
                    path: 'history_bookings',
                    icon: 'bookings',
                    show: klook.showHistoryList,
                },
                {
                    label: this.$t('header.WishList'),
                    path: 'collection',
                    icon: 'icon_social_favorite',
                },
                {
                    label: this.$t('settings'),
                    path: 'account_setting',
                    icon: 'setting',
                    show: +this.accountType === 0,
                },
                {
                    label: this.$t('sub_accounts'),
                    path: 'sub_account',
                    icon: 'sub-account',
                    show: +this.accountType === 0,
                },
                {
                    label: this.$t('bulk_buy'),
                    path: 'batch_order',
                    icon: 'batch-order',
                    show: +this.permission.batch_order === 1,
                },
                {
                    label: this.$t('feedback'),
                    mailto: '<EMAIL>',
                    icon: 'icon_communication_email',
                },
                {
                    label: this.$t('tmall_bookings'),
                    path: 'tmall_bookings',
                    icon: 'taobao',
                    show: this.permission.mashang,
                },
            ].filter(nav => nav.show === undefined || nav.show === true);
        },
        isCNHotel() {
            return this.$route.name === 'hotels' && klook.isCNSite;
        },
        CNLangOptions() {
            return {
                'zh-CN': this.langOptions['zh-CN'],
            };
        },
        langOptionList() {
            return this.isCNHotel ? this.CNLangOptions : this.langOptions;
        },
    },
    components: {
        HeaderShoppingCard,
        HeaderCityExplore,
    },
    created() {
        const _this = this;
        this.$root.$on('setCityHolderName', city_name => {
            _this.setInputPlaceholder(city_name);
        });
    },
    mounted() {
        document.title = this.$t('jv_agent_title');
        this.changeFavicon();
    },
    watch: {
        $route: {
            handler() {
                const { keyword } = this.$route.query;
                this.searchKeywordInput = keyword || '';
                this.setInputPlaceholder();
            },
            immediate: true,
        },
    },
    methods: {
        manualList() {
            if (window.KLK_MARKET === 'cn') {
                return [
                    {
                        label: '简体中文版',
                        path:
                            'https://res.klook.com/image/upload/v1740050131/fm4jw4mekrzremtsa5t7.pdf',
                    },
                    {
                        label: 'English Version',
                        path:
                            'https://res.klook.com/image/upload/v1740050116/pvyozwkt6me86ecmai9c.pdf',
                    },
                ];
            }
            return [
                {
                    label: 'English Version',
                    path:
                        'https://res.klook.com/image/upload/v1740050116/pvyozwkt6me86ecmai9c.pdf',
                },
            ];
        },
        toManual(url) {
            const link = document.createElement('a');
            link.href = url;
            link.target = '_blank';
            link.click();
        },
        goHotels() {
            const query = {
                adult_num: 2,
                child_num: 0,
                room_num: 1,
                check_in: format(addDays(new Date(), 40), 'YYYY-MM-DD'),
                check_out: format(addDays(new Date(), 41), 'YYYY-MM-DD'),
            };

            const goCitySearchList = () => {
                query.stype = 'city';
                query.override = this.$t('SZOverride');
                query.city_id = '23301';
                this.$router.push({
                    path: '/hotels/searchresult',
                    query,
                });
            };

            if (navigator.geolocation) {
                // 获取地理位置
                navigator.geolocation.getCurrentPosition(
                    ({ coords: { longitude, latitude } }) => {
                        query.longitude = longitude;
                        query.latitude = latitude;
                        query.ts = new Date().getTime();
                        query.svalue = 23301;
                        query.city_id = 23301;
                        query.stype = 'location';
                        query.override = this.$t('near_by_activities');
                        this.$router.push({
                            path: '/hotels/searchresult',
                            query,
                        });
                    },
                    () => {
                        goCitySearchList();
                    },
                    {
                        timeout: 3000,
                        maximumAge: 60000,
                    },
                );
            } else {
                goCitySearchList();
            }
        },
        goZanNaDu() {
            this.$router.push({
                path: '/auth/middleware',
                query: {
                    lastPageName: this.$route.fullPath,
                },
            });
        },
        getMessage({ url, data }, callback) {
            klook.ajaxGet(url, data, res => {
                callback(res);
            });
        },
        longPollingMessage(idata, data, callback) {
            if (!this.isLoggedIn) return false;

            const { getMessage } = this;
            // 后端每延时设置为 2 分钟，如果 nginx 超时，则重新请求，但时间间隔不小于 2 秒
            const timestamp = Date.now();
            const timeout = 1000 * 2;
            let delay = 0;
            let timer = null;

            const delayPolling = (data, callback) => {
                delay = timeout - (Date.now() - timestamp);
                timer = setTimeout(
                    () => {
                        clearTimeout(timer);
                        this.longPollingMessage(data, data, callback);
                    },
                    delay > 0 ? 1000 * 60 : 0, //  如果超时立即重新请求，否则一分钟后再请求
                );
            };

            try {
                getMessage(idata, resp => {
                    callback(resp);
                    delayPolling(data, callback);
                });
            } catch (e) {
                delayPolling(data, callback);
            }
        },
        goMessage() {
            this.$router.push({
                name: 'message',
                query: {
                    type: 0, // default: all notification
                    _: Date.now(), // refresh
                },
            });
        },
        setLanguage(lang) {
            if (lang === window.KLK_LANG) {
                return;
            }
            let newPathname = location.pathname;

            if (window.KLK_LANG === 'en') {
                newPathname = `/${lang}${newPathname}`;
            } else {
                newPathname = location.pathname.replace(
                    `/${window.KLK_LANG}`,
                    lang === 'en' ? '' : `/${lang}`,
                );
            }
            window.location = `${location.protocol}//${location.host}${newPathname}${location.search}`;
        },
        changeFavicon() {
            const el = document.getElementById('dynamic-favicon');
            const link = document.createElement('link');
            link.id = 'dynamic-favicon';
            link.rel = 'shortcut icon';
            link.href = window.KLK_JOINT_VENTRUES.icon_url;
            if (document.head) {
                el && document.head.removeChild(el);
                document.head.appendChild(link);
            }
        },
        logout() {
            klook.ajaxPost(urlObj.agent_logout, { accessType: 4 }, res => {
                if (res.success) {
                    document.cookie = 'cookie1=0;max-age=-1;path=/';
                    window.location = `${window.KLK_LANG_PATH}/signin`;
                }
            });
        },
        searchByKeyword() {
            const el = this.$el.querySelector('.header__search--input');
            if (el) {
                el.blur();
            }
            this.$router.push({
                name: 'search',
                query: { keyword: this.searchKeywordInput, _: Date.now() },
            });
        },
        setInputPlaceholder(city_name) {
            if (city_name && this.$route.params.city_id) {
                this.searchPlaceholder = this.$t('activity_in_city', [
                    city_name,
                ]);
            } else {
                this.searchPlaceholder = this.$t(
                    'desktop.index.search_placeholder',
                );
            }
        },
    },
};
</script>

<style lang="scss">
.amp-header {
    .header__container {
        background: #fff;
        box-sizing: border-box;
        font-size: 14px;
        color: #424242;
        position: fixed;
        width: 100%;
        top: 0;
        left: 0;
        z-index: 1001;

        .top {
            padding: 0 30px;
            height: 60px;
            display: flex;
            align-items: center;
            box-shadow: inset 0 -1px 0 0 #eeeeee;

            .header__logo--link {
                margin-right: 30px;
                flex-basis: 200px;

                .img__logo {
                    max-height: 56px;
                }
            }

            .header__search {
                position: relative;

                .klk-poptip-popper-inner {
                    margin: 0;
                    padding: 0;
                    overflow: hidden;
                }

                .klk-icon-icon_edit_search_s {
                    position: absolute;
                    font-size: 20px;
                    right: 10px;
                    top: 8px;
                    cursor: pointer;
                    color: #999;
                }

                .header__search--input {
                    width: 400px;

                    &.klk-input-is-focus {
                        .klk-input-inner {
                            border-color: white;
                        }
                    }
                }
            }

            .header__nav {
                flex: 1 1;
                text-align: right;
                display: flex;
                align-items: center;
                flex-direction: row-reverse;

                &__item {
                    position: relative;
                    padding-left: 40px;
                    cursor: pointer;
                    white-space: nowrap;
                    height: 100%;
                    display: flex;
                    align-items: center;

                    .nav--title {
                        svg {
                            display: inline-block;
                            color: #424242;
                            margin-left: 8px;
                            vertical-align: middle;
                            font-size: 12px;
                        }
                    }

                    .nav--list {
                        padding: 0;
                        list-style-type: none;
                        background: #fff;
                        min-width: 180px;

                        .agent_icon {
                            margin: 0 16px;
                        }

                        li {
                            height: 48px;
                            line-height: 48px;
                            cursor: pointer;
                            text-align: left;

                            &:hover {
                                background: #f5f5f5;
                            }
                        }

                        .active {
                            background: #f5f5f5;
                        }

                        .agent_icon,
                        a {
                            color: #333333;
                        }

                        .agent_icon {
                            font-size: 16px;
                        }
                    }

                    .locale-list {
                        max-height: 300px;
                        overflow-y: auto;

                        li {
                            display: flex;
                            align-items: center;
                            justify-content: space-between;
                            padding: 0 16px;
                        }
                    }
                }

                .nav__user {
                    .nav--list {
                        width: 228px;
                        transform: none;

                        .nav-item-label {
                            vertical-align: middle;
                        }

                        li {
                            position: relative;
                            text-align: left;

                            a {
                                display: inline-block;
                                width: 100%;
                                height: 100%;
                            }

                            .router-link-active {
                                background: #f5f5f5;

                                .klk-icon-icon_other_check_xs {
                                    display: block;
                                }
                            }
                        }

                        svg {
                            &.klk-icon-icon_other_check_xs {
                                display: none;
                                position: absolute;
                                right: 14px;
                                top: 19px;
                            }
                        }
                    }
                }

                .nav__cart {
                    .nav--title {
                        position: relative;
                        .shopping_cart_flag {
                            width: 16px;
                            height: 16px;
                            background: #ff5722;
                            border-radius: 10px;
                            color: #fff;
                            font-size: 12px;
                            position: absolute;
                            line-height: 16px;
                            right: -8px;
                            top: -8px;
                            display: inline-block;
                            text-align: center;
                        }
                    }
                }
            }

            ::-webkit-scrollbar {
                width: 8px;
                height: 8px;
            }

            ::-webkit-scrollbar-thumb {
                border-radius: 1em;
                background-color: rgba(50, 50, 50, 0.3);
            }

            ::-webkit-scrollbar-track {
                border-radius: 1em;
                background-color: rgba(50, 50, 50, 0.1);
            }
        }
    }

    .entry-wrapper {
        line-height: 50px;

        .entry {
            display: inline-block;
            padding: 0 34px;
            font-weight: 600;
            cursor: pointer;

            &:hover {
                color: var(--primary-color);
            }

            &.active {
                color: var(--primary-color);
            }

            .klk-icon {
                margin-right: 4px;
            }
        }
    }
}
</style>
