<template>
    <klk-modal
        class="signin--reject__dialog"
        :open.sync="visible"
        :title="rejectError(errorCode, 'header')"
    >
        <div class="dialog__content">
            <p>{{ rejectError(errorCode, 'content1') }}</p>
            <p class="msg2" v-if="errorCode === '90100001'">
                {{ rejectError(errorCode, 'content2') }}
            </p>
        </div>
        <div class="dialog__footer" slot="footer">
            <klk-button size="small" @click="visible = false">
                {{ $t('login.ok') }}
            </klk-button>
        </div>
    </klk-modal>
</template>

<script>
import { format } from 'date-fns';

export default {
    name: 'KlkLoginRejectDialog',
    data() {
        return {
            errorCode: '',
            createdDate: '',
            visible: false,
        };
    },
    computed: {
        time() {
            return format(this.createdDate, 'HH:mm:ss') || '';
        },
        formatedDate() {
            return (
                format(
                    this.createdDate,
                    this.$t('global.standard.date.format'),
                ) || this.createdDate
            );
        },
    },
    methods: {
        rejectError(errorCode, sectionType) {
            let retStr = '';
            switch (sectionType) {
                case 'header':
                    retStr =
                        errorCode === '90100001'
                            ? this.$t('login.checking.header')
                            : this.$t('login.rejected.header');
                    break;
                case 'content1':
                    retStr =
                        errorCode === '90100001'
                            ? this.$t('login.checking.content1', [
                                  this.time,
                                  this.formatedDate,
                              ])
                            : this.$t('login.rejected.content', [
                                  window.KLK_JOINT_VENTRUES.reply_email,
                              ]);
                    break;
                case 'content2':
                    retStr =
                        errorCode === '90100001'
                            ? this.$t('login.checking.content2', [
                                  window.KLK_JOINT_VENTRUES.reply_email,
                              ])
                            : '';
                    break;
            }
            return retStr;
        },
        show(errorCode, createdDate) {
            this.errorCode = errorCode;
            this.createdDate = createdDate;
            this.visible = true;
        },
    },
};
</script>

<style lang="scss">
.signin--reject__dialog {
    .dialog__content {
        color: #999;
        font-size: 14px;

        p {
            margin: 0;
            padding: 0;
        }

        .msg2 {
            margin-top: 10px;
        }
    }

    .dialog__footer {
        padding-top: 24px;
        text-align: right;
    }
}
</style>
