<template lang="html">
    <div class="credits_page">
        <div class="title">
            {{ $t('balance') }}
        </div>
        <div class="cases">
            <div class="case left">
                <div class="case_top">
                    {{ `${$t('available_balance')}` }}
                </div>
                <div class="case_middle">
                    {{ `${currencySymbol} ${balance}` }}
                </div>
                <div class="case_bottom">
                    <div class="case_sub_text">
                        <span v-show="parseInt(creditAmount)">{{
                            `${$t(
                                'credit_limit',
                            )}: ${currencySymbol} ${creditAmount}`
                        }}</span>
                    </div>
                    <klk-button
                        class="check_detail_btn"
                        size="mini"
                        @click="showBalanceDetail"
                    >
                        {{ $t('check_details') }}
                    </klk-button>
                    <div class="mask" v-show="show_balance_detail_dialog">
                        <div class="balance_detail_dialog">
                            <h4 class="dialog-title">
                                {{ $t('balance_details') }}
                            </h4>
                            <div class="dialog-content">
                                <export-balance-list
                                    v-if="!isSeera && is_master_agent"
                                >
                                </export-balance-list>
                                <p>
                                    {{ $t('agent_register_account') }}：{{
                                        balanceDetailObj.register_account
                                    }}
                                </p>
                                <p>
                                    {{ $t('available_balance') }}：{{
                                        `${currencySymbol}
                  ${formatPriceThousands(balanceDetailObj.available_balance)}`
                                    }}
                                </p>
                                <p>
                                    {{ $t('credit_limit') }}
                                </p>
                                <klk-table
                                    border
                                    class="detail_table"
                                    :columns="creditLimitCols"
                                    :data="detailTableData.creditLimit"
                                >
                                </klk-table>
                                <p>
                                    {{ $t('topup') }}
                                </p>

                                <klk-table
                                    border
                                    class="detail_table"
                                    :columns="topUpCols"
                                    :data="detailTableData.topup"
                                >
                                </klk-table>

                                <p>
                                    {{
                                        `${$t(
                                            'fixed_deposit_amount',
                                        )}：${depositCurrencySymbol}
                  ${formatPriceThousands(
                      balanceDetailObj.fixed_deposit_amount,
                  )}`
                                    }}
                                </p>
                            </div>
                            <div class="dialog-bottom">
                                <klk-button
                                    @click="
                                        show_balance_detail_dialog = !show_balance_detail_dialog
                                    "
                                >
                                    {{ $t('login.ok') }}
                                </klk-button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="case right" v-if="is_master_agent">
                <div class="case_top">
                    {{ `${$t('settlement_due')}` }}
                </div>
                <div class="case_middle">
                    {{ `${currencySymbol} ${settlementDue}` }}
                </div>
                <div class="case_bottom">
                    <div class="case_sub_text"></div>
                    <klk-button size="mini" @click="prepareCharge">
                        {{ $t('topup') }}
                    </klk-button>
                    <!--Top Up-->
                    <div class="mask" v-show="show_charge_dialog">
                        <div class="charge_dialog">
                            <h4>{{ $t('topup') }}</h4>
                            <div
                                class="input_wrapper_bank"
                                :class="{ error: errorMsgObj['topupAmount'] }"
                            >
                                <div class="input_label">
                                    {{ $t('topup_amount') }}({{
                                        currencySymbol
                                    }})
                                </div>
                                <klk-input
                                    name="amount"
                                    @input="correctChargeAmount"
                                    v-model="topupAmount"
                                    @change="validate('topupAmount')"
                                >
                                </klk-input>
                                <div class="message">
                                    {{ errorMsgObj['topupAmount'] }}
                                </div>
                            </div>
                            <div
                                class="input_wrapper_bank"
                                :class="{
                                    error: errorMsgObj['billVoucherUrl'],
                                }"
                            >
                                <div class="input_label">
                                    <span>{{ $t('transfer_receipt') }}</span>
                                    <span class="transfer_receipt_trigger">
                                        <klk-icon
                                            size="16"
                                            type="icon_tips_question"
                                            class="va-top"
                                        ></klk-icon>
                                        <span
                                            class="transfer_receipt_tip_wrapper"
                                            >{{
                                                $t('transfer_receipt_tip')
                                            }}</span
                                        >
                                    </span>
                                </div>
                                <div class="upload_area">
                                    <klk-input
                                        class="upload_input"
                                        :disabled="true"
                                        name="amount"
                                        v-model="bill_voucher_name"
                                    >
                                    </klk-input>
                                    <klk-input
                                        style="display: none"
                                        type="hidden"
                                        v-model="billVoucherUrl"
                                        @change="validate('billVoucherUrl')"
                                    >
                                    </klk-input>
                                    <klk-button
                                        class="upload_btn"
                                        @click="uploadFile"
                                    >
                                        {{ $t('upload') }}
                                    </klk-button>
                                </div>
                                <div class="input_tips">
                                    {{ $t('upload_image_tip1') }}
                                </div>
                                <div class="message">
                                    {{ errorMsgObj['billVoucherUrl'] }}
                                </div>
                            </div>
                            <div
                                class="input_wrapper_bank"
                                :class="{ error: errorMsgObj['topupChannel'] }"
                                v-show="channels.length"
                            >
                                <div class="input_label">
                                    {{ $t('topup_channel') }}
                                </div>
                                <klk-radio-group v-model="topupChannel">
                                    <label
                                        class="input_radio"
                                        v-for="(channel, index) of channels"
                                        :key="index"
                                    >
                                        <img
                                            class="bank"
                                            :src="getTopupChannelImage(channel)"
                                            alt=""
                                        />
                                        <span
                                            class="topup_tips"
                                            v-if="
                                                currencyCode === 'CNY' &&
                                                    channel === 'bank'
                                            "
                                        >
                                            {{ $t('bank_top_up_tips') }}
                                        </span>
                                        <klk-radio
                                            :disabled="
                                                currencyCode === 'CNY' &&
                                                    channel === 'bank'
                                            "
                                            :group-value="channel"
                                            >{{ channel }}
                                        </klk-radio>
                                    </label>
                                </klk-radio-group>
                                <div class="message">
                                    {{ errorMsgObj['topupChannel'] }}
                                </div>
                            </div>
                            <div class="btn_group">
                                <klk-button
                                    block
                                    type="secondary"
                                    @click="hideChargeDialog"
                                >
                                    {{ $t('cancel') }}
                                </klk-button>

                                <klk-button block @click="submitForm('topup')">
                                    {{ $t('submit') }}
                                </klk-button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="bank_info">
            <!--            充值渠道信息-->
            <agent-currency-payment-channel></agent-currency-payment-channel>
        </div>

        <div class="topup_review_tip" v-show="is_master_agent">
            <h2>{{ $t('topup_note') }}</h2>
            <p v-for="(item, index) in topup_review_messages" :key="index">
                {{ item.message }}
                <span class="clear-icon" @click="deleteTopupNote(item.id)">
                    <klk-icon size="20" type="icon_feedback_failure"></klk-icon>
                </span>
            </p>
        </div>
        <div v-if="is_master_agent" class="topup_records_area">
            <klk-table
                :columns="cols"
                :data="tableData"
                class="detail_table"
                border
            >
            </klk-table>
            <klk-pagination
                :total="pagination.total"
                :page-size="pagination.pageSize"
                :current.sync="pagination.curPage"
                @change="handleCurrentChange"
            ></klk-pagination>
        </div>
    </div>
</template>

<script>
import find from 'lodash/find';
import urlObj from '@/common/url';
import { JV_NAME } from '~/jv_config';
import { format } from 'date-fns';
import alipay_en from '@/assets/imgs/alipay_en.png';
import alipay_zh from '@/assets/imgs/alipay_zh.png';
import paypal from '@/assets/imgs/paypal.png';
import bank from '@/assets/imgs/bank.png';
import wechat from '@/assets/imgs/wechat.png';
import fileInput from '@/common/fileInput';
import agentCurrencyPaymentChannel from '@/pages/tpl/agent_currency_payment_channel.vue';
import ExportBalanceList from '@/pages/tpl/export-balance-list.vue';

const channelMaps = [
    {
        currency: 'HKD',
        channels: ['bank'],
    },
    {
        currency: 'CNY',
        channels: ['bank', 'alipay', 'wechat'],
    },
    {
        currency: 'JPY',
        channels: ['bank'],
    },
    {
        currency: 'TWD',
        channels: ['bank'],
    },
    {
        currency: 'SGD',
        channels: ['bank'],
    },
    {
        currency: 'USD',
        channels: ['bank'],
    },
    {
        currency: 'KRW',
        channels: ['bank'],
    },
    {
        currency: 'MYR',
        channels: ['bank'],
    },
    {
        currency: 'IDR',
        channels: ['bank'],
    },
    {
        currency: 'AUD',
        channels: ['bank', 'alipay', 'wechat'],
    },
    {
        currency: 'INR',
        channels: ['bank', 'alipay', 'wechat'],
    },
    {
        currency: 'NZD',
        channels: ['bank', 'alipay', 'wechat'],
    },
    {
        currency: 'THB',
        channels: ['bank', 'alipay', 'wechat'],
    },
    {
        currency: 'VND',
        channels: ['bank', 'alipay', 'wechat'],
    },
    {
        currency: 'EUR',
        channels: ['bank', 'alipay', 'wechat'],
    },
    {
        currency: 'GBP',
        channels: ['bank', 'alipay', 'wechat'],
    },
];

export default {
    name: 'Credits',
    components: {
        agentCurrencyPaymentChannel,
        ExportBalanceList,
    },
    computed: {
        isSeera() {
            // 暂时先不改，因为seera产线的环境变量为 seeraprod 但是包里面没有【后续确定，最近大家很忙】
            return /seera/i.test(window.KLK_ARGS_ENV || '');
        },
    },
    data() {
        return {
            is_master_agent: !window.KLK_USER_INFO.agent_type, //0 master 1 SubAccount
            show_charge_dialog: false,
            show_balance_detail_dialog: false,
            billVoucherUrl: '',
            bill_voucher_name: '',
            topupAmount: '',
            channels: [],
            topupChannel: '',
            balanceDetailObj: {},
            creditLimitCols: [
                {
                    title: this.$t('credit_limit_amount'),
                    render: (h, { row }) => {
                        return h(
                            'span',
                            `${this.formatPossibleDashPrice(
                                row.credit_limit_amount,
                            )}`,
                        );
                    },
                },
                {
                    title: this.$t('available_credit_limit_amount'),
                    render: (h, { row }) => {
                        return h(
                            'span',
                            `${this.formatPossibleDashPrice(
                                row.credit_limit_amount,
                            )}`,
                        );
                    },
                },
                {
                    title: this.$t('actual_date'),
                    render: (h, { row }) => {
                        return h(
                            'span',
                            row.tcl_expiration_date &&
                                +row.available_tcl_amount !== 0
                                ? row.tcl_expiration_date
                                : '-',
                        );
                    },
                },
            ],
            topUpCols: [
                {
                    title: this.$t('available_top_up_amount'),
                    render: (h, { row }) => {
                        return h(
                            'span',
                            `${this.formatPossibleDashPrice(
                                row.available_top_up_amount,
                            )}`,
                        );
                    },
                },
                {
                    title: this.$t('last_top_up_amount'),
                    render: (h, { row }) => {
                        return h(
                            'span',
                            `${this.formatPossibleDashPrice(
                                row.last_top_up_amount,
                            )}`,
                        );
                    },
                },
                {
                    title: this.$t('last_approved_date'),
                    render: (h, { row }) => {
                        return h(
                            'span',
                            row.last_approved_date
                                ? row.last_approved_date
                                : '-',
                        );
                    },
                },
                {
                    title: this.$t('last_top_up_type'),
                    render: (h, { row }) => {
                        return h(
                            'span',
                            row.last_top_up_type ? row.last_top_up_type : '-',
                        );
                    },
                },
            ],
            detailTableData: {
                creditLimit: [],
                topup: [],
            },

            balance: '',
            creditAmount: '0.0',
            settlementDue: '',
            currencyCode: '',

            currencySymbol: '',
            depositCurrencySymbol: '',

            topup_review_messages: [],

            upload_img_error: '',

            tableData: [],
            pagination: {
                total: 0,
                pageSize: 5,
                curPage: 1,
            },
            errorMsgObj: {
                topupAmount: '',
                billVoucherUrl: '',
                topupChannel: '',
            },
            isAjaxing: false,
        };
    },
    watch: {
        topupChannel(channel) {
            if (channel) {
                this.validate('topupChannel');
            }
        },
        show_charge_dialog(visible) {
            if (visible) {
                this.showMask();
            } else {
                this.hideMask();
            }
        },

        show_balance_detail_dialog(visible) {
            if (visible) {
                this.showMask();
            } else {
                this.hideMask();
            }
        },
    },
    created() {
        this.show_charge_dialog = !!this.$route.query.topup;

        this.cols = [
            {
                title: this.$t('date'),
                width: '130',
                render: (h, { row }) => {
                    return h('span', this.formatTime(row.submittedTime));
                },
            },
            {
                title: this.$t('previous_balance'),
                render: (h, { row }) => {
                    return h(
                        'span',
                        `${this.formatPossibleDashPrice(row.previousAmount)}`,
                    );
                },
            },
            {
                title: this.$t('topup_amount'),
                render: (h, { row }) => {
                    return h(
                        'span',
                        `${this.formatPossibleDashPrice(row.topupAmount)}`,
                    );
                },
            },
            {
                title: this.$t('balance'),
                render: (h, { row }) => {
                    return h(
                        'span',
                        `${this.formatPossibleDashPrice(row.remainingAmount)}`,
                    );
                },
            },
            {
                title: this.$t('topup_type'),
                render: (h, { row }) => {
                    return h(
                        'span',
                        row.isBalanceConfirm
                            ? this.$t('jv_name_topup', [row.jvName])
                            : this.topupTypeMap[row.topupType],
                    );
                },
            },
            {
                title: this.$t('topup_channel'),
                width: '120px',
                render: (h, { row }) => {
                    return h('img', {
                        attrs: {
                            src: this.getTopupChannelImage(row.topup_channel),
                        },
                        class: {
                            bank: true,
                        },
                    });
                },
            },
            {
                title: this.$t('review_status'),
                render: (h, { row }) => {
                    return h(
                        'span',
                        row.isBalanceConfirm
                            ? row.confirmStatusStr
                            : this.topupStatusMap[row.status],
                    );
                },
            },
        ];
        this.topupTypeMap = {
            0: this.$t('user_topup'),
            1: this.$t('jv_name_topup', [JV_NAME[window.KLK_JV_NAME]]),
        };

        this.topupStatusMap = {
            0: this.$t('is_reviewing'),
            1: this.$t('approved'),
            2: this.$t('rejected'),
        };
    },
    mounted() {
        this.loadAgentBalance();
        this.getBalanceDetail();
        if (this.is_master_agent) {
            this.loadTopupRecords();
            this.loadInReviewingBalance();
        }
    },
    methods: {
        formatTime(time) {
            return format(time, 'YYYY-MM-DD HH:mm:ss');
        },
        uploadFile() {
            if (!window.FormData) {
                return;
            }
            fileInput.onchange(uploadFile.bind(this)).click();

            function uploadFile(e) {
                if (!e.target.files[0]) {
                    return;
                }
                const formData = new FormData();
                formData.append('upload', e.target.files[0]);
                formData.append('uploadFileName', e.target.files[0].name);

                klook.globalLoading.show('loading...');
                return klook.ajaxPostJSON(
                    urlObj.upload_bill_voucher,
                    formData,
                    response => {
                        klook.globalLoading.hide();
                        if (response.success) {
                            try {
                                if (response.success && response.result) {
                                    this.billVoucherUrl =
                                        response.result.saveName;
                                    this.bill_voucher_name =
                                        response.result.uploadFileName;
                                    this.validate('billVoucherUrl');
                                    this.showDialogSuccess(
                                        this.$t('upload_success'),
                                    );
                                } else {
                                    this.showDialogFail(
                                        (response.error &&
                                            response.error.message) ||
                                            'Something was wrong!',
                                    );
                                }
                            } catch (e) {
                                this.showDialogFail('JSON Parse Error!');
                            }
                        } else {
                            klook.globalLoading.hide();
                            this.showDialogFail(
                                (e && e.message) || 'Something was wrong!',
                            );
                        }
                    },
                );
            }
        },
        formatPossibleDashPrice(price) {
            return price !== '-'
                ? this.currencySymbol + ' ' + this.formatPriceThousands(price)
                : '-';
        },
        formatPriceThousands(price) {
            return klook.formatPriceThousands(price);
        },
        showDialogSuccess(tip) {
            this.$message({
                type: 'success',
                message: tip || 'success',
            });
        },
        showDialogFail(tip) {
            this.$message({
                type: 'error',
                message: tip || 'error',
            });
        },
        correctChargeAmount() {
            let topupAmount = parseInt(this.topupAmount);
            if (!topupAmount) {
                //资金不合法
                topupAmount = 0;
            }

            this.topupAmount = topupAmount;
            this.validate('topupAmount');
        },
        handleCurrentChange(curPage) {
            this.pagination.curPage = curPage;
            this.loadTopupRecords();
        },
        hideChargeDialog() {
            this.show_charge_dialog = false;
            this.resetTopupForm();
        },
        prepareCharge() {
            this.show_charge_dialog = !this.show_charge_dialog;
        },
        submitForm() {
            this.validate('topupAmount');
            this.validate('billVoucherUrl');
            this.validate('topupChannel');
            if (
                +this.topupAmount > 0 &&
                this.billVoucherUrl &&
                this.topupChannel
            ) {
                this.topup();
            }
        },
        resetTopupForm() {
            this.topupAmount = '';
            this.billVoucherUrl = '';
            this.bill_voucher_name = '';

            this.errorMsgObj.topupAmount = '';
            this.errorMsgObj.billVoucherUrl = '';
            this.errorMsgObj.topupChannel = '';
            if (
                this.channels &&
                this.channels.length === 1 &&
                this.channels[0] === 'bank'
            ) {
                this.topupChannel = this.channels[0];
            } else {
                this.topupChannel = '';
            }
        },
        topup() {
            if (this.isAjaxing) {
                return;
            }
            this.isAjaxing = true;
            klook.ajaxPostJSON(
                urlObj.agent_bill_save,
                {
                    increaseCredits: parseFloat(this.topupAmount),
                    billFile: this.billVoucherUrl,
                    topup_channel: this.topupChannel,
                },
                resp => {
                    this.isAjaxing = false;
                    if (resp.success) {
                        this.loadTopupRecords();
                        this.loadAgentBalance();
                        this.hideChargeDialog();
                        this.showDialogSuccess(this.$t('topup_success'));
                        this.resetTopupForm();
                        this.loadInReviewingBalance();
                    } else {
                        this.showDialogFail(resp.error.message);
                    }
                },
            );
        },

        loadInReviewingBalance() {
            klook.ajaxGet(
                urlObj.agent_account_topups,
                {
                    page: this.pagination.curPage,
                    page_size: -1, //拉去所有的
                    status: 0, // 0: non-reviewed 1: reviewed 2: rejected
                },
                resp => {
                    if (
                        resp.success &&
                        resp.result &&
                        Array.isArray(resp.result.list)
                    ) {
                        this.topup_review_messages = resp.result.list.reduce(
                            (acc, item) => {
                                acc.push({
                                    id: item.id,
                                    message:
                                        item.status === 0
                                            ? this.$t('topup_review_tip', [
                                                  this.formatTime(
                                                      item.submittedTime,
                                                  ),
                                                  JV_NAME[window.KLK_JV_NAME],
                                              ])
                                            : item.status === 1
                                            ? this.$t(
                                                  'topup_review_tip_passed',
                                                  [
                                                      this.formatTime(
                                                          item.submittedTime,
                                                      ),
                                                  ],
                                              )
                                            : this.$t(
                                                  'topup_review_tip_failed',
                                                  [
                                                      this.formatTime(
                                                          item.submittedTime,
                                                      ),
                                                  ],
                                              ),
                                });
                                return acc;
                            },
                            [],
                        );
                    } else {
                        this.topup_review_messages = [];
                    }
                },
            );
        },
        loadTopupRecords() {
            klook.ajaxGet(
                urlObj.agent_account_topups,
                {
                    page: this.pagination.curPage,
                    per_page: this.pagination.pageSize,
                },
                resp => {
                    let result;
                    if (resp.success) {
                        result = resp.result;
                        this.tableData = result.list;
                        this.pagination.total = result.totalCount;
                    }
                },
            );
        },
        loadAgentBalance() {
            const { permissions, agent_type } = window.KLK_USER_INFO || {};
            if (agent_type !== 0 && !(permissions || {}).balance_lookup) {
                return;
            }

            klook.ajaxGet(urlObj.agent_account_balance, resp => {
                let result;
                if (resp.success) {
                    result = resp.result || {};
                    this.balance = klook.formatPriceThousands(
                        result.availableBalance,
                    );
                    this.creditAmount = klook.formatPriceThousands(
                        result.creditLimit,
                    );
                    this.currencyCode = result.currencyCode;
                    this.channels = channelMaps.filter(
                        item => item.currency === this.currencyCode,
                    )[0].channels;
                    this.currencySymbol = find(
                        this.currencyList,
                        item => item.value === this.currencyCode,
                    ).title.split(' | ')[1];
                    this.settlementDue = klook.formatPriceThousands(
                        result.settlementDue,
                    );
                    if (
                        this.channels &&
                        this.channels.length === 1 &&
                        this.channels[0] === 'bank'
                    ) {
                        this.topupChannel = this.channels[0];
                    }
                }
            });
        },
        validate(field) {
            if (field === 'topupAmount') {
                this.errorMsgObj.topupAmount =
                    +this.topupAmount > 0
                        ? ''
                        : this.$t('please_input_integer_bigger_than_zero');
            }

            if (field === 'billVoucherUrl') {
                this.errorMsgObj.billVoucherUrl = this.billVoucherUrl
                    ? ''
                    : this.$t('cannot_be_empty');
            }

            if (field === 'topupChannel') {
                this.errorMsgObj.topupChannel = this.topupChannel
                    ? ''
                    : this.$t('channel_cannot_be_empty');
            }
        },
        getTopupChannelImage(channel) {
            const isCN = this.$i18n.locale === 'zh-CN';
            const imageMap = {
                bank,
                paypal,
                alipay: isCN ? alipay_zh : alipay_en,
                wechat,
            };
            return imageMap[channel] || '';
        },
        showBalanceDetail() {
            this.show_balance_detail_dialog = true;
        },
        getBalanceDetail() {
            klook.ajaxGet(urlObj.agent_balance_detail, {}, resp => {
                if (resp.success && resp.result) {
                    this.balanceDetailObj = resp.result || {};
                    this.detailTableData.creditLimit.push(
                        resp.result.credit_limit,
                    );
                    this.detailTableData.topup.push(resp.result.top_up);
                    let depositCurrencySymbol = find(
                        this.currencyList,
                        item => item.value == resp.result.deposit_currency_code,
                    );
                    // 判断是否存在于agent列表中，存在取货币符号，不存在直接显示deposit_currency_code
                    if (!depositCurrencySymbol) {
                        this.depositCurrencySymbol =
                            resp.result.deposit_currency_code;
                    } else {
                        this.depositCurrencySymbol = depositCurrencySymbol.title.split(
                            ' | ',
                        )[1];
                    }
                }
            });
        },
        showMask() {
            document.body.style = 'height:100%;overflow:hidden;';
        },
        hideMask() {
            document.body.style = '';
        },
        deleteTopupNote(id) {
            klook.ajaxPostJSON(
                urlObj.billrecord_hide_status,
                {
                    id: id,
                    is_hide: 1,
                },
                resp => {
                    if (resp.success) {
                        this.$message({
                            type: 'success',
                            message: this.$t('topup_review_tip_delete_success'),
                        });
                        this.loadInReviewingBalance();
                    }
                },
            );
        },
    },
};
</script>

<style lang="scss">
.input-border {
    border: 1px solid var(--primary-color) !important;
}

input[type='number'] {
    margin: 0;
    border: 1px solid #e0e0e0;
    border-radius: 2px;
    height: 40px;
    box-sizing: border-box;
    outline: none;
}

img.bank {
    vertical-align: top;
    height: 20px;
}

.credits_page {
    &.right_view {
        overflow: visible;
    }

    .pagination {
        margin-bottom: 40px;
    }

    .topup_records_area {
        margin-bottom: 10px;
    }

    .title {
        width: 100%;
        font-size: 24px;
        color: #333333;
        font-weight: 600;
        box-shadow: inset 0 -1px 0 0 #e0e0e0;
        padding-bottom: 24px;
        margin-bottom: 30px;
    }

    .bank_info {
        h1 {
            font-size: 16px;
            line-height: 1.62;
            color: #333333;
            height: 26px;
        }

        &_list {
            color: #888;

            i {
                display: inline-block;
                margin-right: 10px;
            }

            &--item div {
                padding-left: 30px;
                line-height: 1.71;
                font-size: 14px;

                &:first-child {
                    padding-left: 0;
                    font-size: 16px;
                }
            }
        }

        .bank_account_info {
            .icon_related_text {
                margin-left: 10px;
            }
        }

        .bank_operation_info {
            list-style-type: disc;
            padding: 16px;
            padding-bottom: 8px;
            margin-bottom: 32px;
            border-radius: 2px;
            background-color: #fefaf3;

            li {
                margin-left: 16px;
                font-size: 14px;
                line-height: 1.36;
                margin-bottom: 8px;
                color: #333;
            }
        }
    }

    .topup_review_tip {
        margin-bottom: 30px;

        h2 {
            margin-bottom: 16px;
            font-size: 14px;
            font-weight: 600;
            line-height: 16px;
            color: #333;
        }

        p {
            margin-bottom: 12px;
            line-height: 1.36;
            color: #666;
            display: flex;
        }

        .clear-icon {
            margin-left: 3px;
        }
    }

    .case_sub_text {
        margin-bottom: 4px;
        height: 13px;
    }

    .check_detail_btn {
        background: #ff5722;
        border-color: #ff5722;
    }

    .balance_detail_dialog {
        width: 960px;
        background: #fff;
        z-index: 102;
        padding: 21px 25px;
        position: fixed;
        left: 50%;
        top: 50%;
        transform: translate3d(-50%, -50%, 0);
        -webkit-transform: translate3d(-50%, -50%, 0);
        border-radius: 2px;
        white-space: normal;
        -webkit-box-shadow: 0 20px 24px 0 rgba(0, 0, 0, 0.04),
            0 18px 32px 0 rgba(0, 0, 0, 0.04);
        box-shadow: 0 20px 24px 0 rgba(0, 0, 0, 0.04),
            0 18px 32px 0 rgba(0, 0, 0, 0.04);

        .dialog-title {
            color: #333333;
            font-size: 20px;
            line-height: 24px;
            font-weight: 500;
            margin: 20px 0 15px;
        }

        p {
            color: #333333;
            font-size: 14px;
            line-height: 20px;
            margin-bottom: 10px;
        }

        .detail_table {
            width: 100%;
            font-size: 12px;
            margin-bottom: 15px;
        }

        .dialog-bottom {
            margin-top: 20px;
            text-align: right;
        }
    }

    .case_bottom {
        position: relative;
        width: 100%;
        margin-top: 7px;
        // .topup-mask {
        //     // top: 60px;
        // }
        .charge_dialog {
            position: fixed;
            top: 50%;
            z-index: 102;
            left: 50%;
            transform: translate3d(-50%, -50%, 0);

            width: 480px;
            padding: 32px;
            border-radius: 2px;
            box-shadow: 0 20px 24px 0 rgba(0, 0, 0, 0.04),
                0 18px 32px 0 rgba(0, 0, 0, 0.04);
            background-color: #ffffff;

            .topup_tips {
                margin-left: 8px;
                line-height: 1.2;
                color: #e64340;
            }

            h4 {
                line-height: 21px;
                font-size: 18px;
                font-weight: 600;
                color: #333;
                padding-bottom: 12px;
            }

            .input_wrapper_bank {
                text-align: left;
                padding-top: 12px;
                padding-bottom: 12px;
                margin: 0;
                word-wrap: break-word;
                white-space: normal;

                input[type='radio'] {
                    width: auto;
                }

                .input_radio {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    height: 48px;
                    line-height: 48px;
                    margin-top: 12px;
                    padding: 0 16px;
                    border-radius: 2px;
                    border: solid 1px #e0e0e0;
                    background-color: #ffffff;
                    box-sizing: border-box;

                    &:hover {
                        @extend .input-border;
                    }

                    .icon {
                        font-size: 24px;
                    }
                }

                .upload_input {
                    width: 208px;
                    border-bottom-right-radius: 0;
                    border-top-right-radius: 0;
                }

                .input_label {
                    margin-bottom: 12px;
                    font-size: 14px;
                    line-height: 16px;
                    color: #333;
                }

                .transfer_receipt_trigger {
                    position: relative;

                    .klook-icon-question-mark {
                        color: #888;
                    }

                    &:hover {
                        .transfer_receipt_tip_wrapper {
                            display: block;
                        }
                    }

                    .transfer_receipt_tip_wrapper {
                        display: none;
                        position: absolute;
                        top: -50px;
                        left: 50%;
                        width: 300px;
                        transform: translate(-50%, -50%);
                        padding: 16px;
                        line-height: 1.58;
                        font-size: 12px;
                        z-index: 1000;
                        box-sizing: border-box;
                        border-radius: 2px;
                        color: #666;
                        background-color: #fff;
                        box-shadow: 0 8px 18px 0 rgba(0, 0, 0, 0.08);
                        border: solid 1px #f5f5f5;

                        &:before {
                            content: '';
                            width: 12px;
                            height: 12px;
                            background-color: #fff;
                            position: absolute;
                            top: 100%;
                            left: 50%;
                            transform: translate(-50%, -50%) rotateZ(45deg);
                            // margin-top: 10px;
                        }
                    }
                }
            }

            .upload_area {
                display: flex;
                align-items: center;
                margin-top: 12px;
                margin-bottom: 8px;

                .upload_input {
                    flex: 1;
                    margin: 0;
                }

                .input_tips {
                    font-size: 12px;
                    line-height: 14px;
                    color: #999;
                }

                .upload_btn {
                    border: none;
                    border-radius: 0;
                }
            }

            .btn_group {
                display: flex;
                justify-content: space-between;
                text-align: center;
                margin-top: 12px;
            }

            .message {
                font-size: 12px;
                color: #e64340;
            }
        }
    }

    .mask {
        width: 100%;
        height: 100%;
        position: fixed;
        top: 0;
        left: 0;
        z-index: 101;
        background: rgba(0, 0, 0, 0.5);
    }
}

// 账号列表
.agent_currency_payment_channel_wrapper {
    overflow: hidden;
    margin-top: 40px;

    h3 {
        position: relative;
        font-size: 18px;
        font-weight: 600;
        line-height: 21px;
        color: #333;
        padding-left: 12px;

        &:before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            width: 4px;
            height: 16px;
            border-radius: 2px;
            background-color: var(--primary-color);
            transform: translate(0, -50%);
        }
    }

    h4 {
        margin-top: 30px;
        margin-bottom: 16px;
        font-size: 14px;
        line-height: 16px;

        i {
            font-size: 16px;
            color: #009685;
        }
    }

    .bank_account_info {
        padding-left: 12px;
        margin-bottom: 30px;

        .icon_related_text {
            margin-left: 10px;
            color: #009685;
        }

        .info_list {
            li {
                font-size: 14px !important;
                color: #333 !important;
                margin-bottom: 12px !important;
                line-height: 1 !important;
            }
        }
    }

    .bank_operation_info {
        padding: 24px 0 24px 24px;
        margin: 24px 0 24px 0;
        border-radius: 2px;
        background-color: #fafafa;

        p {
            line-height: 20px;
        }
    }

    .wechat_pay_img {
        width: 200px;
        margin-top: 16px;
        margin-left: 40px;
    }
}
</style>
