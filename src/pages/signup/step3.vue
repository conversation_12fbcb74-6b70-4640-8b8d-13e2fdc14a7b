<template>
    <div class="signup-step3">
        <div class="title">
            {{ $t('registration_has_been_sent_successful') }}
        </div>
        <div class="content">
            <p>{{ $t('signup_step3_success_text', [time, date]) }}</p>
            <p>{{ $t('signup_step3_contact_text', [contactEmail]) }}</p>
        </div>
        <div class="registration">
            {{ $t('registration_email_address', [emailAccount]) }}
        </div>
    </div>
</template>
<script>
import { format } from 'date-fns';

const DATE_FORMAT_STR = {
    en: 'D MMM YYYY',
    'zh-CN': 'YYYY年M月D日',
    'zh-TW': 'YYYY年M月D日',
};
export default {
    name: 'signupStep3',
    data() {
        return {
            emailAccount: window.KLK_USER_INFO.register_name || '',
            time: '',
            date: '',
        };
    },
    computed: {
        contactEmail() {
            return window.KLK_JOINT_VENTRUES.contact_email || '';
        },
    },
    methods: {
        formatDate(date, type) {
            if (!date) return date;
            if (!type)
                return format(
                    date,
                    DATE_FORMAT_STR[window.KLK_LANG] || 'YYYY-MM-DD',
                );

            let strTimeFormat = 'HH:MM:SS';
            return format(date, strTimeFormat);
        },
    },
    created() {
        const registerTime = sessionStorage.getItem('_session_time');
        this.date = this.formatDate(registerTime);
        this.time = this.formatDate(registerTime, 'time');
    },
};
</script>
<style lang="scss">
.signup-step3 {
    .title {
        font-size: 16px;
        margin-bottom: 20px;
        font-weight: 500;
    }

    .content {
        line-height: 1.5;
        font-size: 14px;

        p {
            margin-bottom: 20px;
        }
    }

    .registration {
        color: #b2b2b2;
        font-size: 16px;
    }
}
</style>
