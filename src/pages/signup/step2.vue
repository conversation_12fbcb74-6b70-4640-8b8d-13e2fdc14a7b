<template>
    <div class="sign-up-step2">
        <signup-administrator-registration
            ref="signup_administrator_registration"
            :disabled="true"
            :baseInfo="baseInfo"
        >
        </signup-administrator-registration>
        <signup-travel-agent-details
            @changeCountryCode="changeCountryCode"
            ref="signup_travel_agent_details_form"
        ></signup-travel-agent-details>
        <signup-company-details
            ref="signup_company_details_form"
        ></signup-company-details>
        <signup-business-details
            ref="signup_business_details_form"
        ></signup-business-details>
        <signup-applicant-information
            ref="signup_applicant_information_form"
        ></signup-applicant-information>
        <term v-if="!isCNSite" ref="term" :country-code="countryCode" />
        <klk-button @click="submitForm" class="submit_btn">
            {{ $t('next_step') }}
        </klk-button>
    </div>
</template>
<script>
import urlObj from '@/common/url';
import { acceptLanguage } from '@/common/const_data';
import SignupTravelAgentDetails from '../tpl/signup_travel_agent_details.vue';
import SignupCompanyDetails from '../tpl/signup_company_details.vue';
import SignupBusinessDetails from '../tpl/signup_business_details.vue';
import SignupAdministratorRegistration from '../tpl/signup_administrator_registration.vue';
import SignupApplicantInformation from '../tpl/signup_applicant_information.vue';
import Term from './term.vue';

export default {
    name: 'signupStep2',
    components: {
        Term,
        SignupTravelAgentDetails,
        SignupCompanyDetails,
        SignupBusinessDetails,
        SignupAdministratorRegistration,
        SignupApplicantInformation,
    },
    data() {
        return {
            passValidation: true,
            // administrator info
            baseInfo: {
                email_account: window.KLK_USER_INFO.register_name || '',
                password: '********', // password 不在接口传送，此处只是展示，不会再次传给接口，暂时写固定
                repeatPwd: '********',
            },
            countryCode: '',
        };
    },
    methods: {
        changeCountryCode(code) {
            this.countryCode = code;
        },
        use(fn) {
            this.validateMiddleware.unshift(next => {
                return () => {
                    fn(next);
                };
            });
        },
        composeMiddleware(callback) {
            this.composedValidateFn = this.validateMiddleware.reduce(
                (resultFn, curFn) => {
                    return curFn(resultFn);
                },
                callback || function() {},
            );
        },
        initValidateMiddleware(callback) {
            this.validateMiddleware = []; //多次点击submit时要初始化
            this.composedValidateFn = function() {};

            let validate_form_list = this.validate_form_list.concat([]);
            while (validate_form_list.length) {
                let formName = validate_form_list.shift();
                this.use(next => {
                    this.$refs[formName].$refs[formName].validate(valid => {
                        if (!valid) {
                            this.passValidation = valid;
                        }
                        next();
                    });
                });
            }
            const term = this.$refs['term'];
            this.use(next => {
                if (!term) {
                    next();
                    return;
                }
                term.$refs['agreemet_form'].validate(valid => {
                    if (!valid) {
                        this.passValidation = valid;
                    }
                    next();
                });
            });
            this.use(next => {
                if (!this.isCN || !term || !term.$refs['signup_terms_form']) {
                    next();
                    return;
                }
                term.$refs['signup_terms_form'].validate(valid => {
                    if (!valid) {
                        this.passValidation = valid;
                    }
                    next();
                });
            });
            this.composeMiddleware(callback);
        },
        getFormData() {
            let validate_form_list = this.validate_form_list.concat([]),
                account_data = {};

            while (validate_form_list.length) {
                let formName = validate_form_list.shift();
                Object.assign(account_data, this.$refs[formName].getFormData());
            }
            delete account_data.repeatPwd;

            return Object.assign(account_data, {
                user_channel: window.KLK_JOINT_VENTRUES.id,
                agreement:
                    (this.$refs.term && this.$refs.term.getFormData()) || true,
            });
        },
        validate(callback) {
            this.passValidation = true;
            this.initValidateMiddleware(callback);
            this.composedValidateFn();
        },
        submitForm() {
            this.validate(() => {
                if (this.passValidation) {
                    klook.ajaxPostJSON(
                        urlObj.register_info,
                        this.getFormData(),
                        async resp => {
                            if (resp.success) {
                                const userId =
                                    resp.result && resp.result.user_id;
                                // CN terms
                                if (this.isCN && userId) {
                                    await (this.$refs.term &&
                                        this.$refs.term.submit());
                                }
                                this.toStep3(resp.result);
                            } else {
                                this.$message({
                                    type: 'error',
                                    message: resp.error && resp.error.message,
                                });
                            }
                        },
                        acceptLanguage,
                    );
                } else {
                    console.log('no pass');
                }
            });
        },

        toStep3(data) {
            if (sessionStorage) {
                sessionStorage.setItem('_session_time', data.create_time);
                sessionStorage.setItem('agent_id', data.agent_id);
            }
            this.$router.push({
                name: 'signupStep3',
            });
        },
    },
    computed: {
        isCNSite() {
            return klook.isCNSite;
        },
        isCN() {
            return this.countryCode === 'CN';
        },
    },
    created() {
        this.validateMiddleware = []; //simulate express middleware to achieve async validate and avoid endless callback
        this.composedValidateFn = function() {};
        this.validate_form_list = [
            'signup_travel_agent_details_form',
            'signup_company_details_form',
            'signup_business_details_form',
            'signup_applicant_information_form',
        ];
    },
};
</script>
<style lang="scss"></style>
