<template>
    <div>
        <signup-administrator-registration
            ref="signup_administrator_registration"
        ></signup-administrator-registration>
        <term v-if="isCNSite" ref="term" country-code="CN" />
        <klk-button @click="submitForm" class="submit_btn">
            {{ $t('next_step') }}
        </klk-button>
    </div>
</template>
<script>
import urlObj from '@/common/url';
import SignupAdministratorRegistration from '../tpl/signup_administrator_registration.vue';
import Term from './term.vue';

const FORM_NAME = 'signup_administrator_registration';
export default {
    name: 'signupStep1',
    components: {
        Term,
        SignupAdministratorRegistration,
    },
    computed: {
        isCNSite() {
            return klook.isCNSite;
        },
    },
    methods: {
        getFormData() {
            return Object.assign(
                {
                    user_channel: window.KLK_JOINT_VENTRUES.id,
                },
                this.$refs[FORM_NAME].getFormData(),
            );
        },
        submitForm() {
            let validCount = 0;
            const totalForms = 3;
            const term = this.$refs['term'];
            const validateForm = formRef => {
                return new Promise(resolve => {
                    if (!term || !term.$refs[formRef]) {
                        validCount++;
                        resolve(true);
                        return;
                    }
                    term.$refs[formRef].validate(valid => {
                        if (valid) {
                            validCount++;
                        }
                        resolve(valid);
                    });
                });
            };

            Promise.all([
                validateForm('agreemet_form'),
                validateForm('signup_terms_form'),
                new Promise(resolve => {
                    this.$refs[FORM_NAME].$refs[FORM_NAME].validate(valid => {
                        if (valid) {
                            validCount++;
                        }
                        resolve(valid);
                    });
                }),
            ]).then(() => {
                if (validCount === totalForms) {
                    this.ajaxForm();
                }
            });
        },
        ajaxForm() {
            const formData = this.getFormData();
            const postData = {
                ...formData,
                user_term_ids: 1,
            };
            // 改成了 form表单提交【后端为了方便获取captcha参数】
            klook.ajaxPost(
                urlObj.register_account,
                postData,
                async resp => {
                    if (resp.success) {
                        if (this.isCNSite) {
                            await (this.$refs.term && this.$refs.term.submit());
                        }
                        window.location = `${window.KLK_LANG_PATH}/signup/step2`;
                    } else {
                        klook.logger.handlerError(
                            {
                                info: JSON.stringify(resp),
                                data: JSON.stringify(postData),
                            },
                            { type: 'register error' },
                        );
                        if (resp.error && resp.error.code !== '-9098') {
                            this.$message({
                                type: 'error',
                                message: resp.error && resp.error.message,
                            });
                        }
                    }
                },
                {},
                { needCaptcha: true },
            );
        },
    },
    mounted() {
        // simpleCaptchaInit('agent_register', this);
    },
};
</script>
