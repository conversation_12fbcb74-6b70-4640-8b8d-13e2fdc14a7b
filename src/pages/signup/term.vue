<template>
    <div class="terms">
        <!-- 仅展示的条款 -->
        <klk-markdown
            v-for="item in displayTerms"
            :key="item.term_id"
            :content="item.content_md"
            class="special-term-display-item"
        />
        <!-- signup-our-agreement -->
        <klk-form :rules="formRules" :model="agreementData" ref="agreemet_form">
            <div class="signup_our_agreement">
                <div class="sub_title">
                    {{ $t('our_agreement') }}
                </div>
                <klk-form-item
                    :label="$t('signup_agreement_label')"
                    prop="agreeUsageTerm"
                >
                    <klk-checkbox v-model="agreementData.agreeUsageTerm">
                        <agreement :country-code="countryCode"></agreement>
                    </klk-checkbox>
                </klk-form-item>
            </div>
        </klk-form>
        <!--  signup terms-->
        <klk-form
            v-if="isCN && Object.keys(termsForm).length > 0"
            ref="signup_terms_form"
            :model="termsForm"
            :rules="termsFormRules"
            class="signup-terms-form"
        >
            <klk-form-item
                v-for="term in terms"
                :key="term.term_id"
                :prop="`${term.term_id}`"
            >
                <div class="terms-wrapper">
                    <klk-checkbox
                        class="terms-checkbox"
                        v-model="termsForm[`${term.term_id}`]"
                    >
                    </klk-checkbox>
                    <klk-markdown
                        class="terms-content"
                        :content="term.content_md"
                    ></klk-markdown>
                </div>
            </klk-form-item>
        </klk-form>
        <!-- 仅展示的条款 -->
    </div>
</template>

<script>
import formRules from '@/common/form_rules';
import urlObj from '@/common/url';
import agreement from '@/pages/signup/agreement.vue';

export default {
    components: {
        agreement,
    },
    name: 'Term',
    data() {
        return {
            displayTerms: [],
            termsForm: {},
            formRules: {},
            agreementData: {
                agreeUsageTerm: false,
            },
        };
    },
    props: {
        countryCode: {
            type: String,
            default: () => '',
        },
    },
    computed: {
        isCN() {
            return this.countryCode === 'CN';
        },
        termsFormRules() {
            const rules = {};
            const validateTerms = (rule, value, callback) => {
                const msg = this.$t('required_terms');
                if (!value) {
                    callback(new Error(msg));
                } else {
                    callback();
                }
            };
            this.terms.forEach(item => {
                if (!item.display_only && item.required) {
                    rules[item.term_id] = {
                        required: 'true',
                        validator: validateTerms,
                        trigger: 'blur,change',
                    };
                }
            });
            return rules;
        },
    },
    created() {
        this.getUserTermList();
        this.formRules = formRules.call(this);
    },
    methods: {
        getUserTermList() {
            klook.ajaxGet(urlObj.user_term_list, {}, res => {
                if (res.success && res.result && res.result.terms) {
                    const terms = res.result.terms || [];
                    this.displayTerms = terms.filter(i => i.display_only);
                    this.terms = terms
                        .filter(j => !j.display_only)
                        .map(item => {
                            this.$set(
                                this.termsForm,
                                item.term_id,
                                item.default_check_status,
                            );
                            return {
                                ...item,
                                value: item.default_check_status,
                            };
                        });
                }
            });
        },
        submit() {
            // CN terms
            if (this.isCN) {
                const terms = [];
                Object.keys(this.termsForm).forEach(key => {
                    if (this.termsForm[key]) {
                        terms.push(Number(key));
                    }
                });
                if (terms.length > 0) {
                    return klook.ajaxPostJSON(urlObj.user_term_authorize, {
                        term_ids: terms,
                    });
                }
            }

            return Promise.resolve();
        },
        getFormData() {
            return this.agreementData.agreeUsageTerm;
        },
    },
};
</script>

<style lang="scss">
.terms {
    .klk-markdown a {
        color: #437dff !important;
    }

    .klk-markdown p {
        margin: 0 !important;
    }

    .signup-terms-form {
        margin-top: 10px;

        .terms-wrapper {
            display: flex;

            .terms-checkbox {
                flex: 0 0 32px;
            }

            .terms-content {
                flex: 1 1 auto;
            }
        }
    }
}
</style>
