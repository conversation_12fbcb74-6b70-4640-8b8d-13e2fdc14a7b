<template>
    <div class="agreement">
        <a
            v-if="!isCN"
            :href="'/conditions.html'"
            target="_blank"
            style="color: var(--primary-color)"
        >
            {{ $t('agree_term') }}
        </a>
        <!-- 国家或地区代码 为【中国(+86)】时，更改条款内容 https://orion.myklook.com/1000003/17/orion/features/1050384/detail -->
        <span style="font-size: 13px;" v-else>
            <span>
                {{ $t('agree_term') }}
            </span>
            <a
                href="/platform-online.html"
                target="_blank"
                style="color: var(--primary-color)"
            >
                同业平台使用条款
            </a>
            、
            <a
                href="/distributor-agreement.html"
                target="_blank"
                style="color: var(--primary-color)"
            >
                分销合作协议
            </a>
            <span>以及</span>
            <a
                href="https://www.klook.cn/zh-CN/tetris/tnc/agent-privacy-policy/"
                target="_blank"
                style="color: var(--primary-color)"
            >
                隐私政策
            </a>
        </span>
        <!-- <a
            v-else
            :href="'/conditions-flickket.html'"
            target="_blank"
            style="color: var(--primary-color)"
        >
            {{ $t('agree_term') }}
        </a> -->
    </div>
</template>

<script>
export default {
    name: 'agreement',
    props: {
        countryCode: {
            type: String,
            default: () => '',
        },
    },
    computed: {
        isCN() {
            return this.countryCode === 'CN';
        },
    },
};
</script>

<style lang="scss">
.agreement {
    display: inline-block;
}
</style>
