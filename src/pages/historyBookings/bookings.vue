<template lang="html">
    <booking-list-comp origin="history">
        <p class="history_tips">
            <span v-html="tips"></span>
        </p>
    </booking-list-comp>
</template>

<script>
import bookingListComp from '../bookings/components/booking-list-comp.vue';

export default {
    name: 'Bookings',
    data() {
        return {};
    },
    components: {
        bookingListComp,
    },
    computed: {
        tips() {
            return this.$t('booking_before_this_date', {
                0: `<a
                class="tips_nav"
                href="${
                    window.KLK_LANG === 'en' ? '' : '/' + window.KLK_LANG
                }/bookings"
                >${this.$t('bookings')}</a
            >`,
            });
        },
    },
};
</script>

<style lang="scss"></style>
