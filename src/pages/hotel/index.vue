<template>
    <div class="hotels">
        <div id="hotelContainer"></div>
    </div>
</template>

<script>
import { registerMicroApps, start } from 'qiankun';
import AMPSdk from '../../../sdk/amp_sdk';

export default {
    mounted() {
        if (!this.$store.state.initQianKun) {
            registerMicroApps([
                {
                    name: 'app',
                    entry:
                        process.env.NODE_ENV === 'development'
                            ? `//localhost:1111/?_t=${new Date().getTime()}`
                            : `${
                                  window.location.origin
                              }/klook-hotel-subweb/?_t=${new Date().getTime()}`,
                    container: '#hotelContainer',
                    activeRule: location =>
                        location.pathname.includes('/hotels'),
                    props: {
                        sdk: AMPSdk,
                    },
                },
            ]);
            start({
                sandbox: false,
            });
            this.$store.commit('UPDATE_SHOPPING_CART_STATUS', true);
        }
    },
};
</script>

<style lang="scss"></style>
