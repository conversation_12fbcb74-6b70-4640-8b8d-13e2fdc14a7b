<template>
    <div class="flash-sale-page" v-loading="loading">
        <div class="flash-sale-container">
            <promotion-list
                pageType="FlashSalePage"
                @finishedLoadData="loading = false"
            >
            </promotion-list>
        </div>
    </div>
</template>
<script>
import PromotionList from '@/pages/tpl/promotion_list.vue';

export default {
    name: 'FlashSalePage',
    components: {
        PromotionList,
    },
    data() {
        return {
            loading: true,
        };
    },
};
</script>
<style lang="scss">
.flash-sale-page {
    position: relative;
    width: 100%;
    min-height: 800px;
    .flash-sale-container {
        width: 1000px;
        min-height: 600px;
        margin: 0 auto;
        padding: 30px 0;
    }
}
</style>
