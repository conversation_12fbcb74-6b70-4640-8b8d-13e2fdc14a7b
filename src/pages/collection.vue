<template lang="html">
    <div class="agent_user_center_collection">
        <div class="title">{{ $t('header.WishList') }}</div>
        <p class="title_tips">{{ $t('collection.tips') }}</p>
        <div class="content">
            <agent-index-collection
                :prop_data="{
                    mode: 'user_center_collection',
                }"
            >
            </agent-index-collection>
        </div>
    </div>
</template>

<script>
import AgentIndexCollection from './tpl/agent_index_collection.vue';

export default {
    name: 'AgentUserCenterCollection',
    components: {
        AgentIndexCollection,
    },
};
</script>

<style lang="scss">
.agent_user_center_collection {
    .title {
        width: 100%;
        font-size: 24px;
        color: #333333;
    }

    .title_tips {
        padding: 12px 0 20px;
        color: #999;
        border-bottom: 1px solid #eee;
    }

    .activity_card_wrapper:nth-child(3n) {
        margin-right: 0;
    }

    .content {
        padding-top: 24px;
    }

    .activity_card_wrapper {
        width: 226px;

        .activity-card,
        .image-wrapper {
            width: 226px;
        }

        .activity-card {
            min-height: 250px;
        }

        .image-wrapper {
            height: 127px;
        }
    }
}
</style>
