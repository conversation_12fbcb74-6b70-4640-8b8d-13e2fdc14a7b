<template>
    <div class="detail-page">
        <div class="nav">
            <a
                href="javascript:void(0)"
                class="clickable_text"
                @click="backBookingList"
            >
                {{ $t('booking_list') }}
            </a>
            <klk-icon
                type="icon_navigation_chevron_right_xxs"
                size="16"
            ></klk-icon>
            <span>
                {{ $t('detail_page') }}
            </span>
        </div>
        <booking-detail :order_no="order_no" type="present"></booking-detail>
        <div class="back">
            <klk-icon
                color="#4c87e6"
                type="icon_navigation_chevron_left"
            ></klk-icon>
            <a
                href="javascript:void(0)"
                class="clickable_text"
                @click="backBookingList"
            >
                <!-- back to 历史订单 -->
                {{ $t('back_to_booking_list') }}
            </a>
        </div>
    </div>
</template>

<script>
import bookingDetail from './components/booking-detail.vue';

export default {
    name: 'detail-page',
    components: {
        bookingDetail,
    },
    data() {
        return {
            order_no: '',
        };
    },
    methods: {
        backBookingList() {
            this.$router.push({ name: 'bookings' });
        },
    },
    created() {
        this.order_no = this.$route.query.order_no;
    },
};
</script>

<style lang="scss"></style>
