<template>
    <div>
        <table-template
            ref="orderList"
            :tableData="tableData"
            :cols="cols"
            :currencySymbol="currencySymbol"
            @move-to-detail="moveToDetail"
            @submit-booking-no="submitBookingNo"
        >
            <template v-slot:default="slotProps">
                <div class="klk-booking-tbody">
                    <div class="klk-tr-body">
                        <div
                            class="klk-tr"
                            v-for="(item, index) in dealTickets(
                                slotProps.row.tickets,
                                slotProps.row.is_more,
                            )"
                            :key="index"
                        >
                            <div style="width:150px">
                                <activity-and-package
                                    :activityId="item.activity_id"
                                    :activityName="item.activity_name"
                                    :packageName="item.package_name"
                                />
                            </div>
                            <div style="width:90px">
                                {{ item.booking_ref_id }}
                            </div>
                            <div style="width:100px">
                                {{ item.participation_date }}
                            </div>
                            <div style="width:100px">
                                <unit-info
                                    :unit-info="item.units_info"
                                    :currencySymbol="currencySymbol"
                                />
                            </div>
                            <div style="width:100px">{{ item.sub_total }}</div>
                            <div style="width:100px">
                                {{ item.refunded_total }}
                            </div>
                            <div style="width:100px" class="status">
                                <span class="hover_tip">{{
                                    item.status_label
                                }}</span>
                                <klk-icon
                                    size="22"
                                    :color="statusTypeList[item.status].color"
                                    :type="statusTypeList[item.status].icon"
                                ></klk-icon>
                            </div>
                            <div class="voucher_url">
                                <a
                                    href="javascript:;"
                                    @click="openVouch(item.voucher_url)"
                                    :class="
                                        (item.status === 4 ||
                                            item.status === 8) &&
                                        item.voucher_url
                                            ? 'clickable_text'
                                            : 'disabled-tap'
                                    "
                                >
                                    {{ $t('view_voucher') }}</a
                                >
                                <a
                                    v-if="item.identity_total_edit_count > 0"
                                    href="javascript:;"
                                    class="clickable_text margin-top-10"
                                    @click="modifyOtherInfo(item)"
                                >
                                    {{ $t('modify_other_info') }}
                                </a>
                            </div>
                        </div>
                        <div
                            class="length-tip"
                            v-show="slotProps.row.tickets.length > 3"
                        >
                            <a
                                href="javascript:void(0)"
                                class="clickable_text"
                                @click="
                                    slotProps.row.is_more = !slotProps.row
                                        .is_more
                                "
                            >
                                {{
                                    slotProps.row.is_more
                                        ? $t('show_less')
                                        : `${$t('show_more') + '(+'}${slotProps
                                              .row.tickets.length - 3})`
                                }}
                            </a>
                        </div>
                    </div>
                </div>
            </template>
        </table-template>
        <dialog-modify-other-info
            :visible.sync="modifyOtherInfoVisible"
            @visible-change="modifyOtherInfoRow = {}"
            :ticket-id="modifyOtherInfoRow.ticket_id"
        >
        </dialog-modify-other-info>
    </div>
</template>

<script>
import urlObj from '@/common/url';
import { getRuleDate, statusTypeList } from '@/common/const_data';
import { openVouch } from '@/common/util';
import dialogAgentBooking from './dialog-agent-booking.vue';
import activityAndPackage from './activity-package.vue';
import DialogModifyOtherInfo from './modify-other-info.vue';
import unitInfo from './unit-info.vue';
import tableTemplate from './table-template.vue';

export default {
    name: 'klk-booking-table',
    data() {
        return {
            edit_booking_number_show: false,
            edit_booking_number: '',
            edit_booking_row_index: -1,
            error_tip: '',
            loading: false,
            modifyOtherInfoVisible: false,
            modifyOtherInfoRow: {},
        };
    },
    props: {
        tableData: {
            type: Array,
            required: true,
        },
        currencySymbol: {
            type: String,
        },
        origin: {
            type: String,
            default: 'present',
            required: true,
        },
    },
    components: {
        dialogAgentBooking,
        activityAndPackage,
        unitInfo,
        DialogModifyOtherInfo,
        tableTemplate,
    },
    computed: {
        statusTypeList() {
            return statusTypeList;
        },
        cols() {
            return [
                {
                    label: this.$t('activity_and_package'),
                    itemStyle: {
                        width: '150px',
                    },
                },
                {
                    label: this.$t('booking_no'),
                    itemStyle: {
                        width: '90px',
                    },
                },
                {
                    label: this.$t('participation_date'),
                    itemStyle: {
                        width: '100px',
                    },
                },
                {
                    label: this.$t('current_bookings'),
                    itemStyle: {
                        width: '100px',
                    },
                },
                {
                    label: this.$t('subtotal'),
                    itemStyle: {
                        width: '100px',
                    },
                },
                {
                    label: this.$t('refunded_amount'),
                    itemStyle: {
                        width: '100px',
                    },
                },
                {
                    label: this.$t('status'),
                    itemStyle: {
                        width: '100px',
                    },
                },
                {
                    label: this.$t('actions'),
                    itemStyle: {
                        width: '100px',
                    },
                },
            ];
        },
        originParams() {
            return this.origin === 'history'
                ? {
                      history: 1,
                  }
                : {};
        },
        detailName() {
            return this.origin === 'history'
                ? 'history_bookings_detail'
                : 'bookings_detail';
        },
    },
    methods: {
        getRuleDate,
        openVouch,
        dealTickets(arr, is_more) {
            if (arr.length < 4 || is_more) {
                return arr;
            } else {
                return arr.slice(0, 3);
            }
        },
        moveToDetail(order) {
            let routeData = this.$router.resolve({
                name: this.detailName,
                query: { order_no: order.order_no },
            });
            window.open(routeData.href, '_blank');
        },
        submitBookingNo(data) {
            const { index, value } = data;
            klook.ajaxPostJSON(
                urlObj.agent_account_bookings_update(
                    this.tableData[index].order_id,
                ),
                Object.assign(
                    {
                        relatedBookingNumber: value,
                    },
                    this.originParams,
                ),
                resp => {
                    this.$refs.orderList.loading = false;
                    if (resp.success) {
                        this.tableData[index].agent_booking_ref_no = value;
                        this.$refs.orderList.edit_booking_row_index = -1;
                        this.$refs.orderList.edit_booking_number_show = false;
                        this.$message({
                            message: this.$t('success'),
                            type: 'success',
                        });
                    } else {
                        this.$refs.orderList.error_tip = resp.error.message;
                        console.error(resp);
                    }
                },
            );
        },
        modifyOtherInfo(info) {
            this.modifyOtherInfoRow = Object.assign({}, info);
            this.modifyOtherInfoVisible = true;
        },
    },
};
</script>

<style lang="scss">
$table-border: 1px solid #d5d5d5;
.klk-booking-table {
    .margin-top-10 {
        margin-top: 10px;
    }

    .klk-booking-thead {
        margin: 20px 0;
        display: flex;
        justify-content: center;
        align-items: stretch;
        background-color: #f9f9f9;
        color: #999;

        .klk-booking-th {
            text-align: center;
            display: flex;
            justify-content: center;
            align-items: center;
            border-top: $table-border;
            border-bottom: $table-border;
            border-left: $table-border;
            padding: 15px 8px;

            &:last-child {
                flex: 1;
                border-right: $table-border;
            }
        }
    }

    .klk-booking-tbody {
        .klk-booking-tr {
            margin: 10px 0;
            border: $table-border;
            border-top-left-radius: 2px;
            border-top-right-radius: 2px;
            color: #333;
            .klk-tr-body {
                position: relative;

                .klk-tr {
                    display: flex;
                    justify-content: center;
                    align-items: stretch;

                    > div {
                        text-align: center;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        padding: 15px 8px;
                        border-top: $table-border;
                        border-right: $table-border;

                        &:last-child {
                            flex: 1;
                            border-right: none;
                        }
                    }

                    .status {
                        position: relative;

                        span.hover_tip {
                            top: 15%;
                            padding: 5px 8px;
                            background-color: #fff;
                            border: 1px solid #ccc;
                            box-shadow: 0px 0px 5px #bbb;
                            border-radius: 2px;
                            position: absolute;
                            z-index: 99;
                            display: none;
                        }

                        &:hover span {
                            display: initial;
                        }
                    }

                    .voucher_url {
                        flex-direction: column;

                        .disabled-tap {
                            color: #d5d5d5;
                            cursor: not-allowed;
                        }
                    }
                }

                .length-tip {
                    box-shadow: 0px 0px 5px #bbb;
                    background-color: #fff;
                    border-radius: 10px;
                    padding: 5px 10px;
                    position: absolute;
                    bottom: 10px;
                    left: 50%;
                    transform: translateX(-50%);
                }
            }
        }
    }
}
</style>
