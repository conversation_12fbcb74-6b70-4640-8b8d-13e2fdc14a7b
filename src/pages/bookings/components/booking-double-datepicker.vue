<template>
    <div class="date_range_wrapper">
        <div class="input_wrapper">
            <div class="input_label">
                <span>{{ label }}</span>
            </div>
            <div @click="handleFocus">
                <div class="input_span">
                    <klk-icon
                        type="icon_time_calendar"
                        class="calendar-size"
                    ></klk-icon>
                    <span>{{ dateStr }}</span>
                    <klk-icon
                        v-if="!visible"
                        type="icon_navigation_chevron_down"
                        class="calendar-right"
                    ></klk-icon>
                    <klk-icon
                        v-if="visible"
                        type="icon_navigation_chevron_up"
                        class="calendar-right"
                    ></klk-icon>
                </div>
            </div>
            <div class="calender_input_wrapper">
                <klk-date-picker
                    v-if="visible"
                    type="date-range"
                    :date.sync="date"
                    double-panel
                    :min-date="new Date('1900-01-01')"
                    @change="onChange"
                ></klk-date-picker>
            </div>
        </div>
    </div>
</template>

<script>
import { format } from 'date-fns';

export default {
    props: {
        label: {
            type: String,
        },
        value: {
            type: Array | Object,
            // required: true,
        },
    },
    data() {
        return {
            visible: false,
            date: [],
        };
    },
    watch: {
        value: {
            handler(val) {
                this.date = val;
            },
            deep: true,
        },
    },
    computed: {
        dateStr() {
            if (this.date && this.date[0] && this.date[1]) {
                return `${format(this.date[0], 'YYYY-MM-DD')} ~ ${format(
                    this.date[1],
                    'YYYY-MM-DD',
                )}`;
            } else {
                return '';
            }
        },
    },
    mounted() {
        this.date = this.value;
    },
    methods: {
        handleFocus() {
            this.visible = true;
        },
        onChange(date) {
            this.visible = false;
            this.date = date;
            this.$emit('input', date);
        },
    },
};
</script>

<style lang="scss" scoped>
.date_range_wrapper {
    display: flex;
    align-items: flex-end;
    position: relative;
    .calender_input_wrapper {
        position: absolute;
        top: 80px;
        left: 50%;
        transform: translateX(-50%);
        z-index: 999;
    }

    div.input_span {
        display: flex;
        align-items: center;
        width: 250px;
        height: 44px;
        padding: 0 12px;
        box-sizing: border-box;
        border: solid 1px #e0e0e0;
        border-radius: 2px;
        position: relative;
        .klk-icon {
            position: absolute;
        }
        .calendar-size {
            font-size: 18px;
        }
        .calendar-right {
            right: 12px;
        }
        span {
            padding-left: 26px;
        }
    }
}
</style>
