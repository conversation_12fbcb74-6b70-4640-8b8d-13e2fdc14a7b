<template>
    <div class="activity_and_package">
        <span v-if="activityId">【{{ activityId }}】</span>
        <span class="text_over_three">
            <span class="hover_title">
                {{ activityName }}
            </span>
            <a
                href="javascript:void(0)"
                @click="moveToActivityDetail(activityId)"
            >
                {{ activityName }}
            </a>
        </span>
        <span class="text_over_two">{{ packageName }}</span>
    </div>
</template>

<script>
export default {
    name: 'activity-and-package',
    props: {
        activityId: {
            type: Number,
        },
        activityName: {
            type: String,
        },
        packageName: {
            type: String,
        },
    },
    methods: {
        moveToActivityDetail(id) {
            this.$router.push({ name: 'activity_detail', params: { id: id } });
        },
    },
};
</script>

<style lang="scss">
.activity_and_package {
    width: 100%;
    flex-direction: column;
    justify-content: center;
    align-items: flex-start;
    position: relative;
    text-align: left;
    > span {
        margin: 10px 0;
        display: -webkit-box;
        overflow: hidden;
        white-space: normal !important;
        text-overflow: ellipsis;
        word-wrap: break-word;
        -webkit-box-orient: vertical;
    }
    span.text_over_three {
        -webkit-line-clamp: 3;
    }
    span.text_over_two {
        -webkit-line-clamp: 2;
    }
    span.hover_title {
        bottom: 100%;
        padding: 5px 8px;
        background-color: #fff;
        border: 1px solid #ccc;
        box-shadow: 0px 0px 5px #bbb;
        border-radius: 2px;
        position: absolute;
        z-index: 99;
        display: none;
    }
    span.text_over_three:hover a {
        color: #4d87e5;
    }
    span.text_over_three:hover span.hover_title {
        display: block;
    }
}
</style>
