<template>
    <div class="table-list">
        <klk-table-extend :cols="batch_order_cols" :table_data="tableData">
            <template slot-scope="scope">
                <tr>
                    <td
                        v-show="scope.row.show_activity"
                        :rowspan="scope.row.activity_rowspan"
                    >
                        <activity-and-package
                            :activityId="scope.row.activityId"
                            :activityName="scope.row.activityName"
                            :packageName="scope.row.packageName"
                        />
                    </td>
                    <td
                        v-show="scope.row.show_activity"
                        :rowspan="scope.row.activity_rowspan"
                    >
                        {{ scope.row.bookingNumber }}
                    </td>
                    <td
                        v-show="scope.row.show_activity"
                        :rowspan="scope.row.activity_rowspan"
                    >
                        {{ scope.row.createDate }}
                    </td>
                    <td>{{ scope.row.units }}</td>
                    <td
                        v-show="scope.row.show_activity"
                        :rowspan="scope.row.activity_rowspan"
                    >
                        {{ scope.row.statusStr }}
                    </td>
                    <td>{{ scope.row.settlementPrice }}</td>
                    <td
                        v-show="scope.row.show_activity"
                        :rowspan="scope.row.activity_rowspan"
                    >
                        {{ scope.row.totalAmount }}
                    </td>
                    <td
                        v-show="scope.row.show_activity"
                        :rowspan="scope.row.activity_rowspan"
                    >
                        <span class="pay-item">{{
                            scope.row.paymentAmount
                        }}</span>
                        <span class="pay-item">{{
                            scope.row.paymentGateway
                        }}</span>
                    </td>
                    <td
                        v-show="scope.row.show_activity"
                        :rowspan="scope.row.activity_rowspan"
                    >
                        {{ scope.row.poundage }}
                    </td>
                    <td
                        v-show="scope.row.show_activity"
                        :rowspan="scope.row.activity_rowspan"
                    >
                        {{ scope.row.relatedBookingNumber }}
                        <a
                            href="javascript:void(0)"
                            class="text_clickable"
                            @click="
                                editBookingNum(
                                    scope.row.relatedBookingNumber,
                                    scope.$index,
                                )
                            "
                            >{{ $t('edit') }}</a
                        >
                    </td>

                    <td
                        v-show="scope.row.show_activity"
                        :rowspan="scope.row.activity_rowspan"
                    >
                        <div>
                            <a
                                v-show="scope.row.voucherUrl"
                                class="text_clickable download"
                                target="_blank"
                                href="javascript:;"
                                :class="{ disabled: scope.row.downloading }"
                                @click="
                                    !scope.row.downloading &&
                                        downloadVoucher(scope.row)
                                "
                            >
                                {{ $t('view_voucher') }}
                            </a>
                        </div>

                        <a
                            class="text_clickable"
                            target="_blank"
                            @click="payBatchOrder(scope.$index)"
                            v-if="
                                tableData[scope.$index].batch_status !== 4 &&
                                    tableData[scope.$index].batch_status !== 5
                            "
                        >
                            <span
                                v-if="
                                    tableData[scope.$index].batch_status !== 5
                                "
                                class="pay-btn"
                                :class="{
                                    'can-pay':
                                        [2, 6].includes(
                                            +tableData[scope.$index]
                                                .batch_status,
                                        ) &&
                                        +tableData[scope.$index].agentId ===
                                            +current_agent_id,
                                }"
                            >
                                {{ $t('shoppingcart.paynow') }}
                            </span>
                        </a>

                        <span
                            v-if="tableData[scope.$index].batch_status === 5"
                            class="order-cancel"
                            >-</span
                        >
                    </td>
                </tr>
            </template>
        </klk-table-extend>
        <dialog-agent-booking
            v-loading="loading"
            v-model="edit_booking_number"
            :edit_booking_number_show="edit_booking_number_show"
            :error_tip="error_tip"
            @submit-booking="submitBooking"
            @cancel-booking="
                edit_booking_number_show = false;
                error_tip = '';
            "
        />
    </div>
</template>

<script>
import urlObj from '@/common/url';
import dialogAgentBooking from './dialog-agent-booking.vue';
import activityAndPackage from './activity-package.vue';

const CURRENT_LANG = {
    'zh-TW': 'zh_TW',
    'zh-CN': 'zh_CN',
    en: 'en_US',
};
export default {
    name: 'bulk-buy-table',
    props: {
        tableData: {
            type: Array,
            required: true,
        },
        origin: {
            type: String,
            default: 'present',
            required: true,
        },
    },
    data() {
        return {
            edit_booking_number_show: false,
            edit_booking_number: '',
            edit_booking_row_index: -1,
            error_tip: '',
            loading: false,
        };
    },
    components: {
        dialogAgentBooking,
        activityAndPackage,
    },
    computed: {
        batch_order_cols() {
            return [
                { width: '120px', label: this.$t('activity_and_package') },
                { width: '84px', label: this.$t('order_no') },
                { width: '90px', label: this.$t('booking_date') },
                { width: '100px', label: this.$t('current_bookings') },
                { width: '80px', label: this.$t('order_status') },
                { width: '90px', label: this.$t('settlement_price') },
                { width: '90px', label: this.$t('total_amount') },
                { width: '90px', label: this.$t('payment_amount_and_type') },
                { width: '90px', label: this.$t('handling_fee') },
                { width: '120px', label: this.$t('agent_booking_ref_num') },
                { width: '100px', label: this.$t('actions'), fixed: 'right' },
            ];
        },
        current_agent_id() {
            return window.KLK_USER_INFO.id;
        },
        originParams() {
            return this.origin === 'history'
                ? {
                      history: 1,
                  }
                : {};
        },
    },
    methods: {
        downloadVoucher(row) {
            if (!row.voucherUrl) return;
            window.open(
                `${row.voucherUrl}&lang=${
                    CURRENT_LANG[window.KLK_LANG || 'en']
                }`,
            );
            this.$set(row, 'downloading', true);
            setTimeout(() => {
                this.$set(row, 'downloading', false);
            }, 5000);
        },
        payBatchOrder(index) {
            if (
                ![2, 6].includes(+this.tableData[index].batch_status) ||
                +this.tableData[index].agentId !== +this.current_agent_id
            )
                return;
            this.$router.push({
                name: 'pay',
                query: { batchOrderId: this.tableData[index].batchOrderId },
            });
        },
        editBookingNum(val, index) {
            this.edit_booking_number_show = true;
            this.edit_booking_number = val;
            this.edit_booking_row_index = index;
        },
        submitBooking(val) {
            this.loading = true;
            this.error_tip = '';
            klook.ajaxPostJSON(
                urlObj.agent_account_batch_order_bookings_update(
                    this.tableData[this.edit_booking_row_index].bookingId,
                ),
                Object.assign({ relatedBookingNumber: val }, this.originParams),
                resp => {
                    this.loading = false;
                    if (resp.success) {
                        this.edit_booking_number = val;
                        this.tableData[
                            this.edit_booking_row_index
                        ].relatedBookingNumber = val;
                        let newRow = this.tableData[
                            this.edit_booking_row_index
                        ];
                        this.tableData.splice(
                            this.edit_booking_row_index,
                            1,
                            newRow,
                        ); //无请求更新
                        this.edit_booking_row_index = -1; //还原表单状态
                        this.edit_booking_number_show = false;
                    } else {
                        this.error_tip = resp.error.message;
                        console.error(resp);
                    }
                },
            );
        },
    },
};
</script>

<style lang="scss">
.klk-table-fixed-right {
    .pay-btn.can-pay,
    .download {
        &:hover {
            text-decoration: underline;
        }
    }
}

.order-cancel {
    cursor: not-allowed;
    color: #999;
}

.pay-btn {
    cursor: not-allowed;
    color: #999;

    &.can-pay {
        color: #4d87e5;
        cursor: pointer;
    }
}
.disabled {
    color: #999999;
    cursor: not-allowed;
}
.pay-item {
    display: inline-block;
}
</style>
