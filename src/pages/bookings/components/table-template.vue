<template>
    <div class="klk-booking-table">
        <div class="klk-booking-thead">
            <div
                class="klk-booking-th"
                :style="item.itemStyle"
                v-for="(item, index) in cols"
                :key="index"
            >
                {{ item.label }}
            </div>
        </div>
        <div class="klk-booking-tbody">
            <div
                class="klk-booking-tr"
                v-for="(row, index) in tableData"
                :key="index"
            >
                <div class="klk-tr-head">
                    <div class="header1">
                        <span>
                            <span>{{
                                `${$t('order_number')} : ${row.order_no}`
                            }}</span>
                            <span v-if="isHotel">({{ row.booking_no }})</span>
                        </span>
                        <span>
                            {{ $t('booking_date') }} :
                            {{ getRuleDate(row.booking_time_utc) }}
                        </span>
                        <span>
                            {{ `${$t('total_amount')}: ${row.total_amount}` }}
                        </span>
                    </div>
                    <div class="header2">
                        <span>
                            {{ $t('agent_booking_ref_num') }}
                            <span>{{ row.agent_booking_ref_no }}</span>
                            <a
                                href="javascript:void(0)"
                                class="clickable_text"
                                @click="
                                    editBookingNum(
                                        row.agent_booking_ref_no,
                                        index,
                                    )
                                "
                                >{{ $t('edit') }}</a
                            >
                        </span>
                        <span v-show="row.payment_gateway">
                            {{ row.payment_gateway }}
                        </span>
                        <span>
                            <a
                                href="javascript:void(0)"
                                class="clickable_text"
                                @click="moveToDetail(row)"
                                >{{ $t('view_detail') }}
                                <klk-icon type="detail" size="14"></klk-icon>
                            </a>
                        </span>
                    </div>
                </div>
                <slot name="default" :row="row"></slot>
            </div>
        </div>
        <dialog-agent-booking
            v-model="edit_booking_number"
            :edit_booking_number_show="edit_booking_number_show"
            :error_tip="error_tip"
            @submit-booking="submitBooking"
            @cancel-booking="
                edit_booking_number_show = false;
                error_tip = '';
            "
        />
    </div>
</template>

<script>
import { getRuleDate } from '@/common/const_data';
import dialogAgentBooking from './dialog-agent-booking.vue';

export default {
    name: 'table-template',
    data() {
        return {
            edit_booking_number_show: false,
            edit_booking_number: '',
            edit_booking_row_index: -1,
            error_tip: '',
            loading: false,
        };
    },
    props: {
        tableData: {
            type: Array,
        },
        currencySymbol: {
            type: String,
        },
        cols: {
            type: Array,
        },
        isHotel: {
            type: Boolean,
            default: false,
        },
    },
    components: {
        dialogAgentBooking,
    },
    methods: {
        getRuleDate,
        moveToDetail(order) {
            this.$emit('move-to-detail', order);
        },
        editBookingNum(val, index) {
            this.edit_booking_number_show = true;
            this.edit_booking_number = val;
            this.edit_booking_row_index = index;
        },
        submitBooking(val) {
            this.error_tip = '';
            this.loading = true;
            console.log(this.tableData[this.edit_booking_row_index]);
            this.$emit('submit-booking-no', {
                index: this.edit_booking_row_index,
                value: val,
            });
        },
    },
};
</script>

<style lang="scss">
$table-border: 1px solid #d5d5d5;
.klk-booking-table {
    .klk-booking-thead {
        margin: 20px 0;
        display: flex;
        justify-content: center;
        align-items: stretch;
        background-color: #f9f9f9;
        color: #999;
        .klk-booking-th {
            text-align: center;
            display: flex;
            justify-content: center;
            align-items: center;
            border-top: $table-border;
            border-bottom: $table-border;
            border-left: $table-border;
            padding: 15px 8px;
            &:last-child {
                flex: 1;
                border-right: $table-border;
            }
        }
    }
    .klk-booking-tbody {
        .klk-booking-tr {
            margin: 10px 0;
            border: $table-border;
            border-top-left-radius: 2px;
            border-top-right-radius: 2px;
            color: #333;

            .klk-tr-head {
                width: 100%;
                padding: 10px;
                background-color: #e6efff;
                > div {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                }
                .header1 {
                    margin-bottom: 10px;
                }
                .header2 {
                    a {
                        margin-left: 3px;
                    }
                }
            }
        }
    }
}
</style>
