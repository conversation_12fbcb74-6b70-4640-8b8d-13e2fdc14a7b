<template>
    <div class="export-booking-list">
        <div class="action">
            <klk-button
                type="secondary"
                icon="icon_edit_upload"
                @click="showExportOptionDialog"
            >
                {{ $t('export') }}
            </klk-button>
        </div>
        <klk-modal
            :open.sync="dialog_export_option_show"
            :overlay-closable="true"
            :show-default-footer="false"
            class="dialog_export_option"
        >
            <!-- add -->
            <div v-if="!isShowEncryption" class="encryption-wrapper">
                <klk-checkbox v-model="is_encryption">
                    {{ $t('109497') }}
                </klk-checkbox>
            </div>
            <div class="btn_group">
                <klk-button
                    :type="active === 0 ? 'primary' : 'secondary'"
                    @click="
                        active = 0;
                        exportBookingRecords(0);
                    "
                >
                    {{ $t('export_search_result') }}
                </klk-button>
                <klk-button
                    v-if="showExportDate"
                    :type="active === 1 ? 'primary' : 'secondary'"
                    @click="
                        active = 1;
                        export_by_date_show = true;
                    "
                >
                    {{ $t('booking.export_date') }}
                </klk-button>
            </div>
            <div class="export_tip">{{ $t('export_tip') }}</div>
            <div class="export_by_date" v-show="export_by_date_show">
                <div class="export_title">
                    {{ $t('booking.border_date') }}
                </div>
                <div class="date">
                    <div class="calender_input_wrapper">
                        <klk-single-date-picker
                            v-model="start_date"
                            :isDayBlocked="disabledStartDateBooking"
                        >
                        </klk-single-date-picker>
                    </div>
                    <div class="calender_input_wrapper">
                        <klk-single-date-picker
                            v-model="end_date"
                            :disabled="!start_date"
                            :isDayBlocked="disabledEndDateBooking"
                        >
                        </klk-single-date-picker>
                    </div>
                </div>
                <div class="btn_group export_action">
                    <klk-button
                        type="secondary"
                        @click="export_by_date_show = false"
                    >
                        {{ $t('cancel') }}
                    </klk-button>
                    <klk-button
                        @click="exportBookingRecords(1)"
                        :loading="loading"
                    >
                        {{ $t('confirm') }}
                    </klk-button>
                </div>
            </div>
        </klk-modal>

        <klk-modal
            :open.sync="dialog_export_progress_show"
            class="export-progress-dialog"
        >
            <div class="export-progress">
                <div class="loader">
                    <klk-icon class="icon_other_loading" type="circular" />
                </div>
                <span
                    >{{ $t('export_progress_tip') }}&nbsp;{{
                        dialog_export_progress_info
                    }}</span
                >
            </div>
        </klk-modal>
    </div>
</template>

<script>
import urlObj from '@/common/url';
import {
    subMonths,
    startOfDay,
    addDays,
    format,
    isBefore,
    parse,
    addMonths,
    isAfter,
} from 'date-fns';
import { formatDateRFC3339 } from '@/common/util';

export default {
    name: 'export-booking-list',
    props: {
        formData: {
            type: Object,
            required: true,
        },
        showExportDate: {
            type: Boolean,
            default: true,
        },
        batchData: {
            type: Object,
            required: true,
        },
        hotelParam: {
            type: Object,
            required: true,
        },
        orderType: {
            type: String,
            required: true,
        },
        origin: {
            type: String,
            required: true,
            default: 'present',
        },
    },
    data() {
        return {
            dialog_export_option_show: false,
            dialog_export_progress_show: false,
            dialog_export_progress_info: '',
            start_date: subMonths(startOfDay(new Date()), 1),
            end_date: startOfDay(new Date()),
            export_by_date_show: false,
            active: -1,
            loading: false,
            is_encryption: true,
        };
    },
    watch: {
        start_date(val) {
            // start date + 6month > today => end_date = today
            const today = startOfDay(new Date());
            const addSixMonDay = addMonths(this.start_date, 6);
            if (new Date(addSixMonDay).getTime() < new Date(today).getTime()) {
                this.end_date = addSixMonDay;
            }
            if (val && this.end_date && isAfter(val, this.end_date)) {
                this.end_date = startOfDay(new Date());
            }
        },
    },
    /**
     * orderType ordinary hotel
     */
    computed: {
        bookingTypeMap() {
            return {
                hotel: {
                    url: urlObj.hotel_booking_export,
                    'Content-Type': 'application/json;',
                },
                ordinary: {
                    url: urlObj.export_agent_order_record,
                    'Content-Type': 'application/x-www-form-urlencoded;',
                },
                batch: {
                    url: urlObj.export_agent_order_batch_record,
                    'Content-Type': 'application/x-www-form-urlencoded;',
                },
                voucher: {
                    url: urlObj.export_agent_order_voucher_batch_record,
                    'Content-Type': 'application/x-www-form-urlencoded;',
                },
            };
        },
        isShowEncryption() {
            return ['batch', 'hotel', 'voucher'].includes(this.orderType);
        },
        typeParams() {
            return this.origin === 'history'
                ? {
                      history: 1,
                  }
                : {};
        },
    },
    methods: {
        disabledStartDateBooking(day) {
            return isBefore(new Date(), day);
        },
        disabledEndDateBooking(day) {
            // limit today && max date | over start day
            if (!this.end_date) return false;
            const max_date = addMonths(parse(this.start_date), 6);
            const date = parse(this.start_date);
            return (
                isAfter(date, day) ||
                isBefore(max_date, day) ||
                isBefore(new Date(), day)
            );
        },
        formatDateRFC3339,
        showExportOptionDialog() {
            this.active = -1;
            this.export_by_date_show = false;
            this.dialog_export_option_show = true;
            this.$emit('export-data');
        },
        ordinaryExport(type) {
            //1 by date 0 搜索结果
            let formList;
            if (type == 1) {
                //导出全部就传所有ids，如果是母账号就导出自己以及子账号所有的，如果是子账号就只导出自己的所有
                formList = {
                    allOrder: type,
                    start_time: this.formatDateRFC3339(this.start_date),
                    end_time: this.formatDateRFC3339(addDays(this.end_date, 1)),
                };
            } else {
                formList = Object.assign({ allOrder: type }, this.formData);
            }
            if (!window.Blob || !window.FileReader) {
                const inputs = Object.keys(formList).reduce(function(
                    prev,
                    key,
                ) {
                    return (
                        prev +
                        '<input name="' +
                        key +
                        '" value="' +
                        formList[key] +
                        '" type="hidden"/>'
                    );
                },
                '');
                const form = document.createElement('form');
                form.action = urlObj.export_agent_order_record;
                form.method = 'post';
                form.innerHTML = inputs;
                document.body.appendChild(form);
                form.submit();
                // setTimeout(() => document.body.removeChild(form), 500);
            } else {
                try {
                    this.dialog_export_progress_info = '';
                    this.pollingExportBookingRecords(formList, false);
                } catch (e) {
                    this.dialog_export_progress_show = false;
                    console.log('Error', e);
                }
            }
        },
        hotelExport(type) {
            let searchData;
            if (type == 1) {
                searchData = {
                    booking_date_start: this.formatDateRFC3339(this.start_date),
                    booking_date_end: this.formatDateRFC3339(
                        addDays(this.end_date, 1),
                    ),
                    display_status: -1,
                };
                // 加上is_hotel
                if (this.origin === 'history') {
                    searchData.is_history = true;
                } else {
                    searchData.is_history = false;
                }
            } else {
                searchData = this.hotelParam;
            }
            this.pollingExportBookingRecords(searchData, true);
        },
        formatDate(date) {
            return date ? format(date, 'YYYY-MM-DD') : '';
        },
        batchExport(type) {
            let searchData;
            if (type == 1) {
                searchData = {
                    all_order: type,
                    start_time: this.formatDate(this.start_date),
                    end_time: this.formatDate(this.end_date),
                };
            } else {
                searchData = Object.assign({ all_order: type }, this.batchData);
            }
            this.pollingExportBookingRecords(searchData, false);
        },
        voucherExport() {
            this.pollingExportBookingRecords(this.batchData, false);
        },
        exportBookingRecords(type) {
            if (this.orderType === 'hotel') {
                this.hotelExport(type);
            } else if (this.orderType === 'batch') {
                this.batchExport(type);
            } else if (this.orderType === 'voucher') {
                this.voucherExport(type);
            } else {
                this.ordinaryExport(type);
            }
        },
        getPostData(sendData, isHotel) {
            let postData;
            if (sendData.taskID || !isHotel) {
                postData = Object.keys(sendData)
                    .map(key => `${key}=${sendData[key]}`)
                    .join('&');
            } else {
                postData = JSON.stringify(sendData);
            }
            return postData;
        },
        pollingExportBookingRecords(sendData, isHotel, taskID) {
            sendData = sendData || {};
            if (taskID) {
                sendData.taskID = taskID;
            }
            this.loading = true;
            const postData = this.getPostData(
                Object.assign(sendData, this.typeParams),
                isHotel,
            );
            const xhr = new XMLHttpRequest();
            xhr.onreadystatechange = () => {
                this.loading = false;
                if (xhr.readyState === 4) {
                    if (xhr.status !== 200) {
                        this.dialog_export_progress_show = false;
                        console.log('Error Status: ' + xhr.status);
                        return;
                    }
                    const reader = new FileReader();
                    reader.onload = () => {
                        try {
                            const resJson = JSON.parse(reader.result);
                            this.loading = false;
                            if (resJson.success && resJson.result) {
                                console.log(resJson.result);
                                if (!resJson.result.total) {
                                    this.dialog_export_progress_show = false;
                                    this.$message({
                                        type: 'warning',
                                        message: this.$t('export_no_result'),
                                    });
                                } else {
                                    this.dialog_export_option_show = false;
                                    this.dialog_export_progress_show = true;
                                    this.dialog_export_progress_info = `${resJson.result.generated}/${resJson.result.total}`;
                                    setTimeout(
                                        this.pollingExportBookingRecords.bind(
                                            this,
                                            {},
                                            isHotel,
                                            resJson.result.task_id,
                                        ),
                                        1000,
                                    );
                                }
                            } else {
                                this.dialog_export_option_show = false;
                                this.dialog_export_progress_show = false;
                                window.alert(
                                    resJson.desc ||
                                        (resJson.error &&
                                            resJson.error.message),
                                );
                            }
                        } catch (e) {
                            if (window.Blob) {
                                const blob = new Blob([xhr.response], {
                                    type: 'application/vnd.ms-excel',
                                });
                                const downloadUrl = (
                                    window.URL || window.webkitURL
                                ).createObjectURL(blob, {
                                    type: 'data:attachment/xlsx',
                                });
                                const a = document.createElement('a');
                                const disposition = xhr.getResponseHeader(
                                    'Content-Disposition',
                                );
                                let filename = '';
                                if (
                                    disposition &&
                                    disposition.indexOf('attachment') !== -1
                                ) {
                                    const matches = /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/.exec(
                                        disposition,
                                    );
                                    if (matches !== null && matches[1]) {
                                        filename = matches[1].replace(
                                            /['"]/g,
                                            '',
                                        );
                                    }
                                }
                                this.dialog_export_progress_show = false;
                                if (navigator.msSaveOrOpenBlob) {
                                    navigator.msSaveOrOpenBlob(blob, filename);
                                    return;
                                }
                                a.download = filename;
                                a.href = downloadUrl;
                                document.body.appendChild(a);
                                a.click();
                                setTimeout(() => {
                                    document.body.removeChild(a);
                                }, 500);
                            }
                        }
                    };
                    reader.readAsText(xhr.response);
                }
            };
            xhr.onerror = e => {
                console.log(e);
                this.dialog_export_progress_show = false;
            };
            if (sendData.taskID) {
                xhr.open(
                    'GET',
                    `${this.bookingTypeMap[this.orderType]['url']}?${postData}${
                        this.is_encryption || this.isShowEncryption
                            ? ''
                            : '&show_original=1'
                    }${
                        isHotel
                            ? `&is_history=${this.origin === 'history'}`
                            : ''
                    }`,
                );
            } else {
                xhr.open(
                    'POST',
                    `${this.bookingTypeMap[this.orderType]['url']}${
                        this.is_encryption || this.isShowEncryption
                            ? isHotel
                                ? `?is_history=${this.origin === 'history'}`
                                : ''
                            : `?show_original=1${
                                  isHotel
                                      ? `&is_history=${this.origin ===
                                            'history'}`
                                      : ''
                              }`
                    }`,
                );
            }
            xhr.responseType = 'blob';
            xhr.setRequestHeader(
                'Content-Type',
                this.bookingTypeMap[this.orderType]['Content-Type'],
            );
            xhr.send(sendData.taskID ? '' : postData);
        },
    },
};
</script>

<style lang="scss" scoped>
.dialog_export_option {
    .klk_dialog {
        width: 680px;
        overflow: revert;
    }
}

.export-progress-dialog {
    .export-progress {
        text-align: center;
        padding: 20px 24px;

        .loader {
            position: relative;
            margin: 0 auto 25px;
            width: 30px;

            &:before {
                content: '';
                display: block;
                padding-top: 100%;
            }
        }

        .circular {
            animation: rotate 0.5s linear infinite;
            height: 100%;
            transform-origin: center center;
            width: 100%;
            position: absolute;
            top: 0;
            bottom: 0;
            left: 0;
            right: 0;
            margin: auto;
        }

        @keyframes rotate {
            100% {
                transform: rotate(360deg);
            }
        }
    }
}

.btn_group {
    margin-top: 15px;
    display: flex;
    justify-content: center;
    align-items: center;
}
.export_tip {
    color: #ff9d26;
    margin: 0 8px;
}
.export_by_date {
    .export_title {
        margin: 10px 0;
    }

    .date {
        .calender_input_wrapper {
            margin: 10px 0;
        }
    }
}
.encryption-wrapper {
    margin-left: 8px;
    margin-top: 10px;
}
</style>
