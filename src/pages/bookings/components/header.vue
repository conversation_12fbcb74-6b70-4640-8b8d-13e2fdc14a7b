<template>
    <div class="search-header">
        <div class="title">
            {{ $t('bookings') }}
        </div>
        <div class="form_options_wrapper">
            <div class="form_options">
                <span
                    class="option_span"
                    @click="tabClick('ordinary')"
                    :class="order_type === 'ordinary' && 'active_option'"
                    >{{ $t('ordinary_order') }}</span
                >
                <span
                    class="option_span"
                    @click="tabClick('voucher')"
                    :class="isVoucher && 'active_option'"
                >
                    {{ $t('voucher_order') }}
                </span>
                <span
                    class="option_span"
                    @click="tabClick('hotel')"
                    :class="order_type === 'hotel' && 'active_option'"
                    >{{ $t('hotel.hotel_order') }}</span
                >
                <!-- 只有当前的订单列表存在batch & 历史订单不存在 -->
                <span
                    v-if="has_batch_order && isPresent"
                    class="option_span"
                    @click="tabClick('batch')"
                    :class="order_type === 'batch' && 'active_option'"
                    >{{ $t('bulk_order') }}</span
                >
            </div>
            <div class="form-search">
                <klk-select
                    v-model="searchObj.searchType"
                    width="200"
                    size="small"
                >
                    <klk-option
                        v-for="item in orderList"
                        :key="item.value"
                        :value="item.value"
                        :label="item.label"
                        :disabled="item.disabled"
                    >
                    </klk-option>
                </klk-select>
                <klk-input
                    size="small"
                    class="search-input"
                    v-model="searchObj.searchNo"
                    :placeholder="$t('please_enter_search')"
                    @keyup.enter="searchByBasicNo"
                >
                </klk-input>
                <span @click="searchByBasicNo">
                    <klk-icon
                        class="search-icon"
                        type="icon_edit_search"
                        size="16"
                    />
                </span>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    props: {
        order_type: {
            type: String,
            required: true,
        },
        has_batch_order: {
            required: true,
        },
        value: {
            type: Object,
            required: true,
        },
        origin: {
            type: String,
            required: true,
            default: 'present',
        },
    },
    data() {
        return {
            searchObj: {},
        };
    },
    computed: {
        isVoucher() {
            return this.order_type === 'voucher';
        },
        orderList() {
            return [
                {
                    label: this.$t('booking_batch_order_no'),
                    value: 0,
                },
                {
                    label: this.$t('booking_no'),
                    value: 1,
                    disabled: this.isVoucher,
                },
                {
                    label: this.$t('agent_booking_ref_num'),
                    value: 2,
                    disabled: this.isVoucher,
                },
            ];
        },
        isPresent() {
            return this.origin === 'present';
        },
    },
    mounted() {
        this.searchObj = this.value;
    },
    methods: {
        // getURL,
        tabClick(flag) {
            this.$emit('tab-click', flag);
        },
        searchByBasicNo() {
            this.$emit('search-by-basic-no');
        },
    },
};
</script>

<style lang="scss" scoped>
.search-header {
    .title {
        width: 100%;
        height: 52px;
        font-size: 24px;
        color: #333333;
        // position: relative;
        // a {
        //     text-align: right;
        //     position: absolute;
        //     right: 0px;
        //     font-size: 16px;
        //     color: var(--primary-color);
        // }
    }
    .form_options_wrapper {
        width: 100%;
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding-bottom: 15px;
        .form_options {
            text-align: center;
            cursor: pointer;
            display: inline-block;
            .option_span {
                float: left;
                padding: 9px 16px;
                font-weight: 600;
                font-size: 14px;
                background: #fff;
                color: #b2b2b2;
                box-sizing: border-box;
                border-bottom: 1px solid #b2b2b2;
            }
            .active_option {
                border-color: var(--primary-color);
                color: var(--primary-color);
            }
        }
        .form-search {
            position: relative;
            display: flex;
            width: 350px;
            .search-input {
                height: 30px;
            }
            .search-icon {
                position: absolute;
                transform: translateY(50%);
                right: 6px;
                cursor: pointer;
            }
        }
    }
}
</style>
