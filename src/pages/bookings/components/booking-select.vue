<template>
    <div class="input_wrapper">
        <div class="input_label">{{ label }}</div>
        <klk-select
            class="selection"
            v-model="selectStr"
            @input="$emit('input', selectStr)"
        >
            <klk-option
                v-for="item in options"
                :key="item[keyName]"
                :value="item[keyName]"
                :label="item[valueName]"
            >
            </klk-option>
        </klk-select>
    </div>
</template>

<script>
export default {
    props: {
        options: {
            type: Array,
            required: true,
        },
        value: {
            type: [String, Number],
            required: true,
        },
        label: {
            type: String,
            required: true,
        },
        valueName: {
            type: String,
            default: 'label',
        },
        keyName: {
            type: String,
            default: 'value',
        },
    },
    data() {
        return {
            selectStr: '',
        };
    },
    mounted() {
        this.selectStr = this.value;
    },
};
</script>

<style lang="scss"></style>
