<template>
    <div class="result-list">
        <div class="header">
            <div class="header_left">
                {{
                    order_type === 'batch'
                        ? $t('search_result')
                        : $t('booking_records')
                }}
            </div>
            <div
                class="header_right"
                :style="{
                    display: `${order_type === 'batch' ? 'flex' : 'initial'}`,
                }"
            >
                <div class="header_right_batch" v-if="order_type === 'batch'">
                    <span>{{ $t('order_status') }}:</span>
                    <klk-select v-model="order_status" @input="changeStatus">
                        <klk-option
                            v-for="item in order_status_list"
                            :key="item.value"
                            :value="item.value"
                            :label="item.label"
                        >
                        </klk-option>
                    </klk-select>
                </div>
                <div class="header_right_normal" v-if="exportButton">
                    <export-booking-list
                        :show-export-date="order_type !== 'voucher'"
                        :origin="origin"
                        :formData="formData"
                        :batchData="batchData"
                        :hotelParam="hotelParam"
                        :order-type="order_type"
                        @export-data="exportData"
                    />
                </div>
            </div>
        </div>
        <div class="child-pay-tip" v-if="order_type === 'batch'">
            {{ $t('children_pay_is_not_allowed.tips') }}
        </div>
        <div>
            <bulk-buy-table
                v-if="order_type === 'batch' && current_order.tableData.length"
                :tableData="current_order.tableData"
                :currencySymbol="currencySymbol"
                :origin="origin"
            />
            <booking-voucher-table
                v-if="
                    order_type === 'voucher' && current_order.tableData.length
                "
                :tableData="current_order.tableData"
                :currencySymbol="currencySymbol"
                :origin="origin"
            />
            <hotel-table
                v-if="order_type === 'hotel' && current_order.tableData.length"
                :tableData="current_order.tableData"
                :currencySymbol="currencySymbol"
                :origin="origin"
            />
            <booking-order-table
                v-if="
                    order_type === 'ordinary' && current_order.tableData.length
                "
                :tableData="current_order.tableData"
                :currencySymbol="currencySymbol"
                :origin="origin"
            />
        </div>
        <div class="pagination">
            <klk-pagination
                v-if="current_order.tableData && current_order.tableData.length"
                :current.sync="current_order.pagination.curPage"
                :page-size="current_order.pagination.pageSize"
                :total="current_order.pagination.total"
                @change="handleCurrentChange"
            ></klk-pagination>
        </div>
    </div>
</template>

<script>
import bookingOrderTable from './booking-order-table.vue';
import bookingVoucherTable from './booking-voucher-table.vue';
import bulkBuyTable from './bulk-buy-table.vue';
import exportBookingList from './export-booking-list.vue';
import hotelTable from '../hotel/list.vue';

export default {
    props: {
        order_type: {
            type: String,
            required: true,
        },
        order_status_list: {
            type: Array,
            required: true,
        },
        //当前订单列表对象
        current_order: {
            type: Object,
            required: true,
        },
        value: {
            type: Number,
            required: true,
        },
        formData: {
            type: Object,
            required: true,
        },
        batchData: {
            type: Object,
            required: true,
        },
        currencySymbol: {
            type: String,
        },
        hotelParam: {
            type: Object,
            required: true,
        },
        origin: {
            type: String,
            required: true,
            default: 'present',
        },
    },
    data() {
        return {
            order_status: 0,
        };
    },
    components: {
        bookingOrderTable,
        bookingVoucherTable,
        bulkBuyTable,
        exportBookingList,
        hotelTable,
    },
    computed: {
        exportButton() {
            return (
                (this.current_order.tableData.length &&
                    (this.order_type === 'ordinary' ||
                        this.order_type === 'hotel')) ||
                this.order_type === 'batch' ||
                this.order_type === 'voucher'
            );
        },
    },
    mounted() {
        this.order_status = this.value;
    },
    methods: {
        changeStatus(status) {
            this.$emit('input', status);
            this.$emit('change-status', status);
        },
        handleCurrentChange(curPage) {
            this.$emit('handle-current-change', curPage);
        },
        exportData() {
            this.$emit('handle-export-data');
        },
    },
};
</script>

<style lang="scss">
.result-list {
    margin-bottom: 10px;
    border-top: 1px solid #e0e0e0;

    .klk-icon-export::before {
        vertical-align: middle;
        margin-right: 5px;
    }

    .header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        height: 60px;
        position: relative;
        font-size: 16px;
        color: #888;

        .header_left {
            font-size: 20px;
            font-weight: 600;
            color: var(--primary-color);
        }

        .header_right_normal {
            cursor: pointer;
        }

        .header_right_batch {
            // margin: 20px 0;
            display: flex;
            align-items: center;
            position: relative;
            right: 24px;
            // top: 50%;
            // transform: translateY(-100%);
            z-index: 99;
            > span {
                white-space: nowrap;
                margin-right: 5px;
            }
        }
    }

    .child-pay-tip {
        display: block;
        color: #ff9d26;
        margin: 5px 0;
    }

    $status_color: #4d87e5;

    .pagination {
        margin-bottom: 16px;
        display: flex;
        justify-content: flex-end;
    }

    $border-button: #4d87e5;

    .export {
        font-family: SFProText;
        // font-size: 14px;
        color: $border-button;
        border: 1px solid $border-button;
        border-radius: 3px;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        width: 112px;

        span {
            margin-left: 4px;
        }
    }
}
</style>
