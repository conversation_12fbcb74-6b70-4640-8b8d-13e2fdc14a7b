<template>
    <div class="table-list">
        <klk-table-extend :cols="batch_order_cols" :table_data="tableData">
            <template slot-scope="scope">
                <tr>
                    <td>
                        <activity-and-package
                            :activityId="scope.row.activity_id"
                            :activityName="scope.row.activity_name"
                            :packageName="scope.row.package_name"
                        />
                    </td>
                    <td>
                        {{ scope.row.order_no }}
                    </td>
                    <td>
                        {{ scope.row.create_time || '-' }}
                    </td>
                    <td>
                        {{ scope.row.sku_info || '-' }}
                    </td>
                    <td>
                        {{ getLabelByValue(scope.row.status) }}
                    </td>
                    <td>
                        {{ scope.row.amount || '-' }}
                        {{ scope.row.currency }}
                    </td>
                    <td>
                        <a
                            v-if="
                                scope.row.status === 1 && scope.row.is_my_order
                            "
                            class="text_clickable"
                            href="javascript:;"
                            @click="openOrderCheckout(scope.row.order_no)"
                        >
                            {{ $t('to_pay') }}
                        </a>
                        <span v-else></span>
                        <a
                            v-if="scope.row.status === 3"
                            class="text_clickable"
                            href="javascript:;"
                            @click="downloadVoucher(scope.row)"
                        >
                            {{ $t('view_voucher') }}
                        </a>
                    </td>
                </tr>
            </template>
        </klk-table-extend>
    </div>
</template>

<script>
import { openOrderCheckout } from '@/common/standaloneCheckout';
import { voucher_types_list } from '@/common/const_data';
import urlObj from '@/common/url';
import activityAndPackage from './activity-package.vue';

const CURRENT_LANG = {
    'zh-TW': 'zh_TW',
    'zh-CN': 'zh_CN',
    en: 'en_US',
};

export default {
    name: 'booking-voucher-table',
    props: {
        tableData: {
            type: Array,
            required: true,
        },
        origin: {
            type: String,
            default: 'present',
            required: true,
        },
    },
    data() {
        return {
            edit_booking_number_show: false,
            edit_booking_number: '',
            edit_booking_row_index: -1,
            error_tip: '',
            loading: false,
        };
    },
    components: {
        activityAndPackage,
    },
    computed: {
        batch_order_cols() {
            return [
                { width: '150px', label: this.$t('activity_and_package') },
                { width: '130px', label: this.$t('order_no') },
                { width: '130px', label: this.$t('booking_date') },
                { width: '100px', label: this.$t('modify_sku') },
                { width: '90px', label: this.$t('order_status') },
                { width: '100px', label: this.$t('payment_amount') },
                { width: '100px', label: this.$t('actions') },
            ];
        },
    },
    mounted() {},
    methods: {
        downloadVoucher(row) {
            window.open(
                `${urlObj.unitorder_download}?order_no=${row.order_no}&lang=${
                    CURRENT_LANG[window.KLK_LANG || 'en']
                }`,
            );
        },
        openOrderCheckout(order_no) {
            openOrderCheckout(order_no);
        },
        getLabelByValue(value) {
            const data = voucher_types_list();
            const item = data.find(entry => entry.value === value);
            return item ? item.label : '-';
        },
    },
};
</script>

<style lang="scss"></style>
