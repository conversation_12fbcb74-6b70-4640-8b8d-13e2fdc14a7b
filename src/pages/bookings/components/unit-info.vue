<template>
  <div class="unit-info">
      <div class="current-item" v-for="(unit,index) in unitInfo" :key='index'>
          <p>{{ unit.name }}</p>
          <p>{{ currencySymbol + unit.quantity +"x" +unit.unitPrice}}</p>
      </div>
  </div>
</template>

<script>
export default {
  name: "unit-info",
  props: {
    unitInfo:{
        type:Array
    },
    currencySymbol:{
        type:String
    }
  }
};
</script>

<style lang="scss">
.unit-info{
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    .current-item{
        margin: 2px 0;
        p{
            margin: 2px 0;
            word-break: break-all;
        }
    }
}
</style>
