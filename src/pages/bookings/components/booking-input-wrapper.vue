<template>
    <div class="booking-input-wrapper">
        <div class="input_wrapper" v-if="showSelectType">
            <div class="input_label">
                <span>{{ label }}</span>
            </div>
            <klk-select
                v-model.trim="select.selectType"
                :placeholder="outPlace"
                @change="$emit('select-type', select.selectType)"
            >
                <klk-option
                    v-for="item in selectList"
                    :key="item[outKeyName]"
                    :value="item[outKeyName]"
                    :label="item[outValueName]"
                >
                </klk-option>
            </klk-select>
        </div>
        <div class="input_wrapper">
            <div v-if="!showSelectType" class="input_label">
                <span>{{ label }}</span>
            </div>
            <klk-input
                :maxlength="150"
                v-if="!ableSelect"
                v-model.trim="select.inputValue"
                :placeholder="$t('please_enter_search')"
            >
            </klk-input>
            <klk-select
                ref="selectComp"
                v-show="ableSelect"
                :filterable="filterable"
                v-model.trim="select.inputValue"
                :placeholder="innerPlace"
                :clearable="clearable"
                v-loading="loading"
            >
                <klk-option
                    v-for="item in options"
                    :key="item[keyName]"
                    :value="item[keyName]"
                    :label="item[valueName]"
                >
                </klk-option>
            </klk-select>
        </div>
    </div>
</template>

<script>
import debounce from 'lodash/debounce';

export default {
    props: {
        label: {
            type: String,
            required: true,
        },
        value: {
            type: Object,
            required: true,
        },
        selectList: {
            type: Array,
            required: true,
        },
        options: {
            type: Array,
            default: () => {
                return [];
            },
        },
        showSelectType: {
            type: Boolean,
            default: true,
        },
        loading: {
            type: Boolean,
        },
        keyName: {
            type: String,
            default: 'value',
        },
        valueName: {
            type: String,
            default: 'label',
        },
        clearable: {
            type: Boolean,
            default: false,
        },
        filterable: {
            type: Boolean,
            default: false,
        },
        outKeyName: {
            type: String,
            default: 'value',
        },
        outValueName: {
            type: String,
            default: 'label',
        },
        outPlace: {
            type: String,
            defualt: () => {
                return this.$t('please_enter_search');
            },
        },
        innerPlace: {
            type: String,
            defualt: () => {
                return this.$t('please_enter_search');
            },
        },
        secondSelect: {
            type: Boolean,
            defualt: false,
        },
        isActivity: {
            type: Boolean,
            default: false,
        },
    },
    data() {
        return {
            select: {},
            activatedBookingDateShortCut: 'last_1_month',
            isComposing: false,
        };
    },
    computed: {
        ableSelect() {
            if (this.isActivity) {
                return this.select.selectType == 1;
            } else {
                return this.secondSelect;
            }
        },
    },
    watch: {
        select: {
            handler() {
                this.emitValue();
            },
            deep: true,
        },
    },
    mounted() {
        this.select = this.value;
        this.getValueList = debounce(this.getValueList, 1000);
        this.$nextTick(() => {
            const selectInput = this.$refs.selectComp.$refs.input || '';
            const selsctDiv = this.$refs.selectComp.$refs.reference || '';
            if (selsctDiv) {
                selsctDiv.addEventListener('click', () => {
                    this.select.inputValue = '';
                });
            }
            if (selectInput) {
                selectInput.addEventListener('input', e => {
                    if (!this.isComposing) {
                        this.getValueList(e.target.value);
                    }
                });

                //兼容输入中文还在输入时候就出发input事件
                selectInput.addEventListener('compositionstart', () => {
                    this.isComposing = true;
                });

                selectInput.addEventListener('compositionend', e => {
                    this.isComposing = false;
                    this.getValueList(e.target.value);
                });
                selectInput.addEventListener('blur', () => {
                    this.$refs.selectComp.isFocus = false;
                });
            }
        });
    },
    methods: {
        getValueList(value) {
            if (this.isComposing) return;
            if (value) {
                this.$emit('get-value-list', value);
            }
        },
        emitValue() {
            this.$emit('input', this.select);
        },
    },
};
</script>

<style lang="scss">
.booking-input-wrapper {
    display: inline-block;
}
</style>
