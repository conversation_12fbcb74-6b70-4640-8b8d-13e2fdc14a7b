<template>
    <klk-modal
        class="modify-other-info"
        :title="$t('traveler_info')"
        :scrollable="true"
        width="640"
        :show-default-footer="false"
        :open.sync="visible"
        :closable="!ifEditable"
        @on-close="hideModal"
    >
        <div v-loading="loading">
            <div class="modify-modal-content">
                <div class="basic-info-wrapper">
                    <div class="info-item">
                        <div class="left-wrapper">
                            <span class="title">{{
                                $t('modify_activity_name')
                            }}</span>
                            <span>{{ orderInfo.activity_name }}</span>
                        </div>
                        <div class="right-wrapper">
                            <span class="title">{{
                                $t('remain_modify_count')
                            }}</span>
                            <span
                                >{{ remainEditableCount }}/{{
                                    totalEditableCount
                                }}</span
                            >
                            <klk-poptip
                                v-if="modifyHistory.length"
                                content="Your details are safe with us. "
                                :width="300"
                                placement="bottom"
                                dark
                            >
                                <a href="javascript:;" class="clickable_text">
                                    {{ $t('modify_history') }}
                                </a>
                                <div
                                    slot="content"
                                    class="modify-history-wrapper"
                                >
                                    <div
                                        v-for="(history,
                                        index) in modifyHistory"
                                        :key="index"
                                    >
                                        <span class="split" v-if="index"
                                            >-</span
                                        >
                                        <p>
                                            {{ $t('modify_time') }}：
                                            {{
                                                formatTime(
                                                    history.create_time,
                                                    'YYYY-MM-DD HH:mm:ss',
                                                )
                                            }}
                                        </p>
                                        <p>
                                            {{ $t('modify_status') }}：{{
                                                statusMap[history.status]
                                            }}
                                        </p>
                                        <p
                                            v-if="
                                                +history.status === 2 &&
                                                    history.message
                                            "
                                        >
                                            {{ $t('modify_fail_reason') }}：{{
                                                history.message
                                            }}
                                        </p>
                                    </div>
                                </div>
                            </klk-poptip>
                        </div>
                    </div>
                    <div class="info-item">
                        <span class="title">{{
                            $t('modify_package_name')
                        }}</span>
                        <span>{{ orderInfo.package_name }}</span>
                    </div>
                    <div class="info-item">
                        <span class="title">{{ $t('modify_sku') }}</span>
                        <span
                            v-for="(sku, index) in orderInfo.item_list"
                            :key="index"
                        >
                            <span v-if="index"> , </span> {{ sku.sku_name }} X
                            {{ sku.count }}
                        </span>
                    </div>
                    <div class="info-item">
                        <span class="title">{{
                            $t('participation_date')
                        }}</span>
                        <span>{{
                            formatTime(orderInfo.start_time, 'YYYY-MM-DD ')
                        }}</span>
                    </div>
                </div>
            </div>
            <!--        other info-->
            <div class="other-info-wrapper">
                <pay-traveler-info
                    id="travelerSection"
                    ref="travelerInfoForm"
                    :traveler-info-form="travelerInfoForm"
                    :disabled="!ifEditable"
                ></pay-traveler-info>
            </div>
            <div class="divider"></div>
            <div class="modify-tips-wrapper">
                <div class="title">{{ $t('modify_tips') }}</div>
                <div v-html="modifyTips"></div>
            </div>
            <div class="foot-wrapper" slot="footer" v-if="ifEditable">
                <klk-button
                    class="cancel"
                    type="outlined"
                    size="small"
                    @click="hideModal"
                >
                    {{ $t('modify_cancel') }}
                </klk-button>
                <klk-button
                    v-loading="submitting"
                    :disabled="isEqualData"
                    size="small"
                    @click="submitInfo"
                >
                    {{ $t('modify_submit') }}
                </klk-button>
            </div>
        </div>
    </klk-modal>
</template>

<script>
import urlObj from '@/common/url';
import { modifyOtherInfoStatusList } from '@/common/const_data';
import { format } from 'date-fns';
import { cloneDeep, isEqual } from 'lodash';
import PayTravelerInfo from '../../ticket/pay/traveler-info/index.vue';

export default {
    components: {
        PayTravelerInfo,
    },
    props: {
        visible: {
            type: Boolean,
            required: true,
            default: false,
        },
        ticketId: {
            type: String | Number,
            required: true,
            default: '',
        },
    },
    data() {
        return {
            loading: true,
            submitting: false,
            identityEditable: false,
            totalEditableCount: 0,
            remainEditableCount: 0,
            modifyHistory: [],
            modifyTips: '',
            orderInfo: {},
            travelerInfoForm: {
                extraInfo: null,
                travelerInfo: {
                    booking_traveler_info: null,
                    unit_traveler_info: null,
                },
            },
            oldTravelerInfoForm: {},
        };
    },
    watch: {
        ticketId(newVal) {
            if (!newVal) return;
            this.loadTicketInfo();
        },
    },
    computed: {
        isEqualData() {
            return isEqual(this.oldTravelerInfoForm, this.travelerInfoForm);
        },
        ifEditable() {
            // 订单状态是可修改的且最新一条修改记录的状态不是修改中
            if (!this.identityEditable) return false;
            return (
                this.modifyHistory.length === 0 ||
                this.modifyHistory[0].status !== 0
            );
        },
        statusMap() {
            return modifyOtherInfoStatusList();
        },
    },
    methods: {
        formatTime(time, rule) {
            return format(time, rule);
        },
        hideModal() {
            this.$emit('update:visible', false);
            this.$emit('visible-change', false);
        },
        loadTicketInfo() {
            if (!this.ticketId) return;
            this.loading = true;
            klook.ajaxGet(
                urlObj.ticket_alterinfo.get(this.ticketId),
                {},
                res => {
                    this.loading = false;
                    if (res.success && res.result) {
                        const result = res.result || {};
                        this.modifyHistory = (
                            result.id_edit_history || []
                        ).reverse();
                        this.totalEditableCount =
                            result.id_total_editable_count;
                        this.remainEditableCount =
                            result.id_remain_editable_count;
                        this.modifyTips = result.id_edit_tips;
                        this.identityEditable = result.identity_editable;
                        this.orderInfo =
                            (result.item_info_list &&
                                result.item_info_list[0]) ||
                            {};
                        if (this.orderInfo.other_info) {
                            this.travelerInfoForm.extraInfo =
                                this.orderInfo.other_info.extra_info || null;
                            if (this.orderInfo.other_info.traveler_other_info) {
                                this.travelerInfoForm.travelerInfo.booking_traveler_info =
                                    this.orderInfo.other_info
                                        .traveler_other_info
                                        .booking_traveler_info || null;
                                this.travelerInfoForm.travelerInfo.unit_traveler_info =
                                    this.orderInfo.other_info
                                        .traveler_other_info
                                        .unit_traveler_info || null;
                            }
                            this.oldTravelerInfoForm = cloneDeep(
                                this.travelerInfoForm,
                            );
                        }
                    } else {
                        this.$message({
                            type: 'error',
                            message: res.error && res.error.message,
                        });
                    }
                },
            );
        },
        submitInfo() {
            this.submitting = true;
            const postData = {
                ticket_id: this.ticketId,
                item_info_list: [
                    {
                        activity_id: this.orderInfo.activity_id,
                        package_id: this.orderInfo.package_id,
                        item_list: this.orderInfo.item_list,
                        other_info: {
                            extra_info: this.travelerInfoForm.extraInfo,
                            traveler_other_info: {
                                booking_traveler_info: this.travelerInfoForm
                                    .travelerInfo.booking_traveler_info,
                                unit_traveler_info: this.travelerInfoForm
                                    .travelerInfo.unit_traveler_info,
                            },
                        },
                    },
                ],
            };
            klook.ajaxPostJSON(
                urlObj.ticket_alterinfo.update,
                postData,
                res => {
                    this.submitting = false;
                    if (res.success) {
                        this.hideModal();
                        this.$emit('modify-success');
                        this.$message({
                            type: 'success',
                            message: this.$t('submit_success'),
                        });
                    } else {
                        this.$message({
                            type: 'error',
                            message: res.error && res.error.message,
                        });
                    }
                },
            );
        },
        formatDate(date) {
            return date.substring(0, 19).replace('T', ' ');
        },
    },
    mounted() {
        this.loadTicketInfo();
    },
};
</script>

<style lang="scss">
.modify-other-info {
    margin-top: 10px;

    .modify-modal-content {
        margin-top: 14px;

        .basic-info-wrapper {
            padding: 16px;
            background: #f5f5f5;

            .left-wrapper,
            .right-wrapper {
                display: inline-block;
                width: 50%;
                vertical-align: top;
            }

            .info-item {
                font-size: 14px;

                .title {
                    color: #999999;
                    margin-right: 12px;
                }

                .clickable_text {
                    color: var(--primary-color);
                    margin-left: 12px;
                }
            }

            .info-item + .info-item {
                margin-top: 8px;
            }
        }
    }

    .other-info-wrapper {
        width: 100%;

        .klk-form-item {
            .klk-input,
            .klk-select {
                width: 268px;
            }
        }
    }

    .divider {
        border-top: 1px solid #e0e0e0;
    }

    .modify-tips-wrapper {
        margin-top: 24px;
        padding: 16px;
        background-color: #f5f5f5;
        line-height: 20px;

        .title {
            font-weight: bold;
            margin-bottom: 4px;
            color: #4a4a4a;
        }
    }

    .foot-wrapper {
        margin-top: 32px;
        text-align: right;

        .cancel {
            margin-right: 16px;
        }
    }
}
</style>
