<template>
    <div class="detail-page">
        <div class="info">
            <p class="info-title">
                {{ $t('order_info') }}
                <klk-button
                    size="small"
                    type="secondary"
                    class="toggle_eye_icon"
                    @click="toggleLoadDetailPage"
                    >{{ toggleText }}</klk-button
                >
            </p>
            <div class="info_detail">
                <span>
                    {{ `${$t('order_status')} : ${info.order_status}` }}
                </span>
                <span>
                    {{
                        `${$t('agent_booking_ref_num')} : ${
                            info.agent_booking_ref_no
                        }`
                    }}
                    <a
                        href="javascript:void(0)"
                        class="clickable_text"
                        @click="
                            editBookingNum(
                                info.agent_booking_ref_no,
                                info.order_no,
                            )
                        "
                        >{{ $t('edit') }}</a
                    >
                </span>
                <span>
                    {{ `${$t('order_channel')} : ${info.order_channel}` }}
                </span>
                <span>
                    {{ `${$t('booking_batch_order_no')} : ${info.order_no}` }}
                </span>
                <span>
                    {{ `${$t('booking_date')} : ${info.booking_time_utc}` }}
                </span>
                <span>
                    {{ `${$t('payment_type')} : ${info.payment_gateway}` }}
                </span>
                <span>
                    {{ `${$t('payment_date')} : ${info.payment_time_utc}` }}
                </span>
                <span>
                    {{
                        `${$t('booking_agent_name')} : ${
                            info.booking_agent_name
                        }`
                    }}
                </span>
                <span>
                    {{
                        `${$t('booking_agent_mobile')} : ${
                            info.booking_agent_mobile
                        }`
                    }}
                </span>
                <span>
                    {{
                        `${$t('booking_agent_email')} : ${
                            info.booking_agent_email
                        }`
                    }}
                </span>
            </div>
        </div>
        <dialog-agent-booking
            v-loading="loading"
            v-model="edit_booking_number"
            :edit_booking_number_show="edit_booking_number_show"
            :error_tip="error_tip"
            @submit-booking="submitBooking"
            @cancel-booking="
                edit_booking_number_show = false;
                error_tip = '';
            "
        />
        <div
            class="booking-detail-list"
            v-if="tableData && tableData.length > 0"
        >
            <div class="header">
                <span class="inner-header"> {{ $t('booking_details') }}</span>
                <span>
                    <span class="payment_amount">{{
                        `${$t('payment_amount')}:${info.payment_amount}`
                    }}</span>
                    {{
                        `+ ${$t('promo_code')} : ${info.promo_amount} = ${$t(
                            'total_amount',
                        )}:${currencySymbol} ${total_amount}`
                    }}
                </span>
            </div>
            <klk-table
                class="detail-table"
                :columns="cols"
                :data="tableData"
                border
            >
                <activity-and-package
                    slot="activity-package"
                    slot-scope="{ row }"
                    :activityId="row.activity_id"
                    :activityName="row.activity_name"
                    :packageName="row.package_name"
                />

                <unit-info
                    slot="cur-booking"
                    slot-scope="{ row }"
                    :unit-info="row.units_info"
                    :currencySymbol="currencySymbol"
                />
                <div slot="modify_other_info" slot-scope="{ row }">
                    <div v-if="row.modify_other_info">
                        <p>修改时间：</p>
                        <p>{{ row.modify_other_info.create_time }}</p>
                        <p>修改状态： {{ row.modify_other_info.status }}</p>
                    </div>
                </div>

                <div slot="operation" slot-scope="{ row }">
                    <a
                        href="javascript:;"
                        :class="
                            row.voucher_url ? 'clickable_text' : 'disabled-tap'
                        "
                        @click="openVouch(row.voucher_url)"
                        >{{ $t('view_voucher') }}</a
                    >
                    <br />
                    <a
                        class="clickable_text modify"
                        v-if="row.identity_total_edit_count > 0"
                        href="javascript:;"
                        @click="modifyOtherInfo(row)"
                    >
                        {{ $t('modify_other_info') }}
                    </a>
                </div>
            </klk-table>
        </div>
        <div v-else>
            <klk-no-data></klk-no-data>
        </div>
        <dialog-modify-other-info
            :visible.sync="modifyOtherInfoVisible"
            @visible-change="modifyOtherInfoRow = {}"
            @modify-succes="loadData"
            :ticket-id="modifyOtherInfoRow.ticket_id"
        >
        </dialog-modify-other-info>
    </div>
</template>

<script>
import urlObj from '@/common/url';
import {
    booking_status_list,
    getRuleDate,
    modifyOtherInfoStatusList,
    payment_types_list,
} from '@/common/const_data';
import { format } from 'date-fns';
import find from 'lodash/find';
import { openVouch } from '@/common/util';
import dialogAgentBooking from './dialog-agent-booking.vue';
import activityAndPackage from './activity-package.vue';
import unitInfo from './unit-info.vue';
import DialogModifyOtherInfo from './modify-other-info.vue';

export default {
    name: 'detail-page',
    props: {
        order_no: {
            type: String,
            default: '',
            required: true,
        },
        type: {
            type: String,
            default: '',
            required: true,
        },
    },
    data() {
        return {
            tableData: [],
            info: {
                order_status: '',
                agent_booking_ref_no: '',
                order_channel: '',
                order_no: '',
                order_id: '',
                booking_time_utc: '',
                payment_gateway: '',
                payment_time_utc: '',
                booking_agent_name: '',
                booking_agent_mobile: '',
                booking_agent_email: '',
                payment_amount: '',
                promo_amount: '',
            },
            currencySymbol: '',
            edit_booking_number_show: false,
            edit_booking_number: '',
            error_tip: '',
            loading: false,
            modifyOtherInfoVisible: false,
            modifyOtherInfoRow: {},
            is_encryption: true,
        };
    },
    computed: {
        toggleText() {
            return this.is_encryption
                ? this.$t('show_all_texts')
                : this.$t('hide_partial_texts');
        },
        cols() {
            return [
                {
                    title: this.$t('activity_and_package'),
                    width: 120,
                    slot: 'activity-package',
                },
                {
                    title: this.$t('booking_no'),
                    width: 100,
                    key: 'booking_ref_id',
                },
                {
                    title: this.$t('participation_date'),
                    width: 120,
                    key: 'participation_date',
                },
                {
                    title: this.$t('current_bookings'),
                    width: 100,
                    key: 'cur-booking',
                },
                {
                    title: this.$t('subtotal'),
                    key: 'sub_total',
                },
                {
                    title: this.$t('status'),
                    width: 120,
                    key: 'status',
                },
                {
                    title: this.$t('refunded'),
                    width: 100,

                    key: 'refunded',
                },
                {
                    title: this.$t('refunded_amount'),
                    width: 100,
                    key: 'refunded_amount',
                },
                {
                    title: this.$t('refunded_time'),
                    width: 130,
                    key: 'refunded_time',
                },
                {
                    width: 190,
                    title: '出行人修改状态',
                    slot: 'modify_other_info',
                },
                {
                    width: 150,
                    title: this.$t('operation'),
                    slot: 'operation',
                },
            ];
        },
        ticket_status_list() {
            return booking_status_list();
        },
        paymentTypesList() {
            return payment_types_list();
        },
        total_amount() {
            return +this.info.payment_amount + +this.info.promo_amount;
        },
        order_status_list() {
            return [
                {
                    value: 'paid',
                    label: this.$t('paid'),
                },
                {
                    value: 'cancel',
                    label: this.$t('cancel'),
                },
            ];
        },
        order_channel_list() {
            return [
                {
                    value: 'batch',
                    label: this.$t('booking_channel_batch'),
                },
                {
                    value: 'amp',
                    label: this.$t('booking_channel_amp'),
                },
                {
                    value: 'agentapi',
                    label: this.$t('booking_channel_agent_api'),
                },
            ];
        },
        paramsStr() {
            if (this.type === 'history') {
                return this.is_encryption
                    ? '?history=1'
                    : '?show_original=1&history=1';
            } else if (this.type === 'present') {
                return this.is_encryption ? '' : '?show_original=1';
            } else {
                return '';
            }
        },
        typeParams() {
            return this.type === 'history'
                ? {
                      history: 1,
                  }
                : {};
        },
    },
    components: {
        dialogAgentBooking,
        activityAndPackage,
        unitInfo,
        DialogModifyOtherInfo,
    },
    methods: {
        toggleLoadDetailPage() {
            this.is_encryption = !this.is_encryption;
            this.loadData();
        },
        getRuleDate,
        openVouch,
        groupedTickets(tickets) {
            tickets.forEach(ticket => {
                ticket.sub_total =
                    this.currencySymbol +
                    klook.formatPriceThousands(ticket.sub_total_amount);
                ticket.refunded_amount =
                    this.currencySymbol +
                    klook.formatPriceThousands(ticket.refunded_amount);
                ticket.voucher_url =
                    ticket.status == 4 ? ticket.voucher_url : '';
                ticket.status =
                    this.ticket_status_list.find(
                        status => status.value == ticket.status,
                    ).label || '';
                ticket.participation_date = this.getRuleDate(
                    ticket.participation_date,
                );
            });
            return tickets;
        },
        processData(obj) {
            obj.payment_amount = Number(obj.payment_amount).toFixed(2);
            obj.promo_amount = Number(obj.promo_amount).toFixed(2);
            obj.order_status =
                this.order_status_list.find(
                    item => item.value == obj.order_status,
                ).label || '';
            obj.order_channel =
                this.order_channel_list.find(
                    item => item.value == obj.order_channel,
                ).label || '';
            obj.payment_gateway =
                obj.payment_gateway == 'newpaypal'
                    ? 'paypal'
                    : obj.payment_gateway;
            obj.payment_gateway =
                this.paymentTypesList.find(
                    item => item.value == obj.payment_gateway,
                ).label || '';
            obj.payment_time_utc = this.getRuleDate(obj.payment_time_utc);
            obj.booking_time_utc = this.getRuleDate(obj.booking_time_utc);
            return obj;
        },
        loadData() {
            const url = `${urlObj.agent_account_order_detail(this.order_no)}${
                this.paramsStr
            }`;
            klook.ajaxGet(url, {}, resp => {
                if (resp.success) {
                    const result = resp.result || {};
                    this.currencySymbol = find(
                        this.currencyList,
                        item => item.value == result.currency_code,
                    ).title.split(' | ')[1];
                    for (const key in result) {
                        if (key !== 'tickets') {
                            this.info[key] = result[key];
                        }
                    }

                    this.info = this.processData(this.info);
                    this.tableData = this.groupedTickets(result.tickets);

                    this.tableData.forEach((ticket, index) => {
                        klook.ajaxGet(
                            urlObj.ticket_alterinfo.getHistory(
                                ticket.ticket_id,
                            ),
                            {},
                            res => {
                                if (res.success && res.result) {
                                    const modifyHistory = (
                                        res.result.id_edit_history || []
                                    ).reverse();
                                    if (modifyHistory.length > 0) {
                                        const history_info = modifyHistory[0];
                                        this.tableData[
                                            index
                                        ].modify_other_info = {
                                            create_time: format(
                                                new Date(
                                                    history_info.create_time,
                                                ),
                                                'YYYY-MM-DD HH:mm:ss',
                                            ),
                                            status: modifyOtherInfoStatusList()[
                                                history_info.status
                                            ],
                                        };
                                    }
                                }
                            },
                        );
                    });
                }
            });
        },
        editBookingNum(val) {
            this.edit_booking_number_show = true;
            this.edit_booking_number = val;
        },
        submitBooking(val) {
            this.error_tip = '';
            this.loading = true;
            klook.ajaxPostJSON(
                urlObj.agent_account_bookings_update(this.info.order_id),
                Object.assign(
                    {
                        relatedBookingNumber: val,
                    },
                    this.typeParams,
                ),
                resp => {
                    this.loading = false;
                    if (resp.success) {
                        this.info.agent_booking_ref_no = val;
                        this.edit_booking_number_show = false;
                    } else {
                        this.error_tip = resp.error.message;
                        console.error(resp);
                    }
                },
            );
        },
        modifyOtherInfo(info) {
            this.modifyOtherInfoRow = Object.assign({}, info);
            this.modifyOtherInfoVisible = true;
        },
    },
    mounted() {
        this.loadData();
    },
};
</script>

<style lang="scss">
.detail-page {
    .toggle_eye_icon {
        margin-left: 20px;
        border-color: #999;
        color: #666;
    }

    .nav {
        margin: 20px 0 30px;
        display: flex;
        align-items: center;
    }

    .info {
        .info-title {
            font-size: 20px;
            color: #000;
            margin: 10px 0;
        }

        .info_detail {
            border: 1px solid #e5e5e5;
            border-radius: 2px;
            background-color: #f9f9f9;
            color: #333;
            padding: 20px;
            margin: 20px 0 15px;

            span {
                display: inline-block;
                margin: 10px 0;
                font-size: 14px;

                &:nth-child(3n + 1) {
                    width: 30%;
                }

                &:nth-child(3n + 2) {
                    width: 40%;
                }

                &:nth-child(3n) {
                    width: 30%;
                }

                &:nth-child(n + 7) {
                    width: 50%;
                }
            }
        }
    }

    .booking-detail-list {
        margin: 40px 0 10px;

        .header {
            margin: 15px 0;
            display: flex;
            justify-content: space-between;
            align-items: center;

            .inner-header {
                font-size: 20px;

                .payment_amount {
                    font-size: 16px;
                    font-weight: bold;
                }
            }
        }
    }

    .clickable_text {
        color: var(--primary-color);

        &.modify {
            margin-top: 10px;
            display: inline-block;
        }
    }

    .disabled-tap {
        color: #d5d5d5;
        cursor: not-allowed;
    }

    .back {
        display: flex;
        align-items: center;
    }

    .current_bookings {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;

        .current_item {
            margin: 3px 0;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            overflow: hidden;
        }
    }
}
</style>
