<template>
    <div class="date_range_wrapper">
        <div class="input_wrapper">
            <div class="input_label">
                <span>{{ label }}</span>
            </div>
            <div class="calender_input_wrapper">
                <klk-single-date-picker
                    class="booking_date_start"
                    v-model="date.dateStart"
                >
                </klk-single-date-picker>
            </div>
        </div>
        <div class="input_wrapper">
            <div class="calender_input_wrapper">
                <klk-single-date-picker
                    class="booking_date_end"
                    :disabled="!date.dateStart"
                    :isDayBlocked="disabledDateBooking"
                    v-model="date.dateEnd"
                >
                </klk-single-date-picker>
            </div>
        </div>
        <ul v-if="hasRangeSelect" class="range_select">
            <li
                v-for="(item, index) in dateShortCuts"
                @click="setBookingDateRange(item)"
                :key="index"
                :class="
                    (activatedBookingDateShortCut === item.key &&
                        'option active') ||
                        'option'
                "
            >
                {{ item.label }}
            </li>
        </ul>
    </div>
</template>

<script>
import {
    subMonths,
    subYears,
    parse,
    isBefore,
    isValid,
    startOfDay,
} from 'date-fns';

export default {
    props: {
        label: {
            type: String,
            required: true,
        },
        value: {
            type: Object,
            required: true,
        },
        hasRangeSelect: {
            type: Boolean,
        },
    },
    data() {
        return {
            date: {},
            activatedBookingDateShortCut: 'last_1_month',
        };
    },
    watch: {
        date: {
            handler(val) {
                this.emitValue(val);
            },
            deep: true,
        },
    },
    computed: {
        dateShortCuts() {
            return [
                //日期范围选择快捷键
                {
                    key: 'last_1_month',
                    label: this.$t('last_1_month'),
                    startDate: subMonths(startOfDay(new Date()), 1),
                    endDate: startOfDay(new Date()),
                },
                {
                    key: 'last_3_month',
                    label: this.$t('last_3_month'),
                    startDate: subMonths(startOfDay(new Date()), 3),
                    endDate: startOfDay(new Date()),
                },
                {
                    key: 'last_1_year',
                    label: this.$t('last_1_year'),
                    startDate: subYears(startOfDay(new Date()), 1),
                    endDate: startOfDay(new Date()),
                },
            ];
        },
    },
    mounted() {
        this.date = this.value;
    },
    methods: {
        setBookingDateRange(obj) {
            this.activatedBookingDateShortCut = obj.key;
            this.date.dateStart = obj.startDate;
            this.date.dateEnd = obj.endDate;
        },
        disabledDateBooking(day) {
            if (!this.date.dateStart) {
                return false;
            }
            const date = parse(this.date.dateStart);
            if (!isValid(date)) {
                return false;
            }
            return isBefore(day, date);
        },
        emitValue() {
            this.$emit('input', this.date);
        },
    },
};
</script>

<style lang="scss" scoped>
.date_range_wrapper {
    display: flex;
    align-items: flex-end;
    .range_select {
        flex: 1;
        line-height: 44px;
        font-size: 14px;
        color: #333333;
        .option {
            display: inline-block;
            cursor: pointer;
            margin-left: 16px;
        }

        .active {
            color: var(--primary-color);
        }
    }
}
</style>
