<template>
    <klk-modal
        class="dialog_change_alert"
        :open.sync="edit_booking_number_show"
        :closable="true"
        :title="$t('agent_booking_ref_num')"
        :ok-label="$t('submit')"
        :cancel-label="$t('cancel')"
        @on-close="cancelBooking"
        @on-cancel="cancelBooking"
        @on-confirm="submitBooking"
    >
        <div class="input_booking">
            <klk-input
                v-model="booking_number"
                :placeholder="$t('please_input_agent_booking_ref_num')"
            >
            </klk-input>
            <i>{{ error_tip }}</i>
        </div>
    </klk-modal>
</template>

<script>
export default {
    data() {
        return {
            booking_number: '',
        };
    },
    props: {
        edit_booking_number_show: {
            type: Boolean,
            default: false,
        },
        value: {
            type: String | Number,
        },
        error_tip: {
            type: String,
        },
    },
    watch: {
        value(val) {
            this.booking_number = val;
        },
    },
    created() {
        this.booking_number = this.value;
    },
    methods: {
        submitBooking() {
            this.$emit('submit-booking', this.booking_number);
        },
        cancelBooking() {
            this.$emit('cancel-booking');
            this.booking_number = this.value;
        },
    },
};
</script>

<style lang="scss">
.dialog_change_alert {
    .input_booking {
        margin: 20px 0;
        text-align: center;
        input {
            border-radius: 2px;
        }
        i {
            font-style: normal;
            color: red;
            font-size: 12px;
        }
    }
    .action {
        display: flex;
        justify-content: space-around;
        align-items: center;
    }
}
</style>
