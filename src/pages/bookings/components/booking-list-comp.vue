<template lang="html">
    <div class="my_bookings">
        <booking-header
            :origin="origin"
            :order_type="order_type"
            :has_batch_order="has_batch_order"
            @tab-click="tabClick"
            @search-by-basic-no="searchByBasicNo"
            v-model="searchObj"
        />
        <slot v-if="this.order_type !== 'batch'"></slot>
        <div class="content">
            <div v-if="order_type === 'hotel'">
                <hotel-search
                    ref="hotelSearch"
                    :isReset="isReset"
                    @reset="handleHotelReset"
                ></hotel-search>
            </div>
            <!-- 非hotel -->
            <div v-if="order_type !== 'hotel'">
                <!-- bookingDate -->
                <booking-date-range
                    v-model="bookingDate"
                    :label="$t('date_range')"
                    :hasRangeSelect="true"
                />
                <!-- activity info -->
                <booking-input-wrapper
                    v-model="activityInfo"
                    :label="
                        order_type !== 'voucher'
                            ? $t('activity_info')
                            : $t('activity_id')
                    "
                    :selectList="activityType"
                    :loading="activityLoading"
                    keyName="id"
                    valueName="title"
                    :options="activityList"
                    :filterable="true"
                    :clearable="true"
                    :isActivity="true"
                    :showSelectType="order_type !== 'voucher'"
                    @get-value-list="getActivityList"
                />
                <!-- payment -->
                <template v-show="order_type === 'batch'">
                    <booking-select
                        v-if="order_type !== 'voucher'"
                        v-model="paymentTypes"
                        class="payment_type"
                        :options="paymentTypeList"
                        :label="$t('payment_type')"
                    />
                    <!-- agentid -->
                    <booking-select
                        v-model="agentId"
                        :options="agentAccountInfoListIncludeAll"
                        :label="$t('filter_by_booking_agent')"
                        valueName="email"
                        keyName="agentId"
                    />
                </template>
                <template
                    v-if="order_type === 'batch' || order_type === 'voucher'"
                >
                    <div>
                        <div class="input_wrapper">
                            <div class="input_label">
                                <span>{{ $t('participation_date') }}</span>
                            </div>
                            <div class="calender_input_wrapper">
                                <klk-single-date-picker
                                    class="booking_date_start"
                                    v-model="participate_start_time"
                                />
                            </div>
                        </div>
                        <div class="input_wrapper">
                            <div class="calender_input_wrapper">
                                <klk-single-date-picker
                                    class="booking_date_end"
                                    :disabled="!participate_start_time"
                                    :isDayBlocked="disabledDateBooking"
                                    v-model="participate_end_time"
                                >
                                </klk-single-date-picker>
                            </div>
                        </div>
                        <booking-select
                            v-if="order_type === 'voucher'"
                            v-model="voucherBookingStatus"
                            :options="voucherTypesList"
                            :label="$t('booking_status')"
                        />
                    </div>
                </template>
                <!-- !batch order 额外的  -->
                <!-- Traveler -->
                <booking-input-wrapper
                    v-show="order_type === 'ordinary'"
                    v-model="travelerInfo"
                    :label="$t('traveler')"
                    :selectList="travelerTypeList"
                />

                <!-- less/more  -->
                <div v-show="order_type === 'ordinary'">
                    <div class="filters_detail" v-show="!is_less_filter">
                        <booking-date-range
                            v-model="travelDate"
                            :label="$t('participation_date')"
                        />
                        <booking-input-wrapper
                            v-model="destinationInfo"
                            :label="$t('destination')"
                            :selectList="country_city_list"
                            outValueName="country_name"
                            outKeyName="country_id"
                            keyName="id"
                            valueName="name"
                            :options="city_list"
                            :secondSelect="true"
                            :outPlace="$t('country_or_regions')"
                            :innerPlace="$t('city')"
                            @select-type="updateRelatedCityList"
                        />

                        <!-- booking account -->
                        <booking-select
                            v-model="agentId"
                            :options="agentAccountInfoListIncludeAll"
                            :label="$t('filter_by_booking_agent')"
                            valueName="email"
                            keyName="agentId"
                        />
                        <booking-select
                            v-model="refundOfNot"
                            v-show="is_master_agent"
                            :options="refoundList"
                            :label="$t('refund_of_not')"
                        />
                        <booking-select
                            v-model="paymentTypes"
                            class="payment_type"
                            :options="paymentTypeList"
                            :label="$t('payment_type')"
                        />

                        <booking-select
                            v-model="bookingChannel"
                            :options="booking_channel_list"
                            :label="$t('order_channel')"
                        />

                        <booking-select
                            v-model="bookingStatus"
                            :options="bookingStatusList"
                            :label="$t('booking_status')"
                        />
                    </div>
                    <div
                        class="filters_show"
                        @click="is_less_filter = !is_less_filter"
                    >
                        <span>{{
                            is_less_filter
                                ? $t('more_filters')
                                : $t('less_filters')
                        }}</span>
                        <i
                            :class="{
                                less_filters: !is_less_filter,
                                more_filters: is_less_filter,
                            }"
                        ></i>
                    </div>
                </div>
            </div>
            <div class="search-btn">
                <div>
                    <klk-button class="search" @click="handleSearch">
                        {{ $t('search') }}
                    </klk-button>
                    <!-- hotel新增reset -->
                    <klk-button
                        type="text"
                        v-show="order_type === 'hotel'"
                        class="search"
                        @click="handleReset"
                    >
                        {{ $t('search_reset') }}
                    </klk-button>
                </div>
            </div>
        </div>
        <!--result -->
        <result-list
            :origin="origin"
            v-loading="loading"
            v-model="order_status"
            :order_type="order_type"
            :order_status_list="order_status_list"
            :current_order="orderTypeMap[order_type]"
            :formData="formData"
            :batchData="batchData"
            :hotelParam="hotelParam"
            :currencySymbol="currencySymbol"
            @change-status="loadBatchOrderData"
            @handle-current-change="handleCurrentChange"
            @handle-export-data="handleExportData"
        />
        <div
            v-show="orderTypeMap[order_type].tableData.length === 0"
            :style="{ borderTop: 'solid 1px #e0e0e0' }"
        >
            <klk-no-data></klk-no-data>
        </div>
    </div>
</template>

<script>
import find from 'lodash/find';
import urlObj from '@/common/url';
import orderBy from 'lodash/orderBy';
import {
    subMonths,
    format,
    startOfDay,
    addDays,
    parse,
    isValid,
    isBefore,
} from 'date-fns';
import {
    payment_types_list,
    booking_status_list,
    voucher_types_list,
} from '@/common/const_data';
import { formatDateRFC3339 } from '@/common/util';
import bookingHeader from './header.vue';
import bookingDateRange from './booking-date-range.vue';
import bookingInputWrapper from './booking-input-wrapper.vue';
import bookingSelect from './booking-select.vue';
import resultList from './result-list.vue';
import hotelSearch from '../hotel/search.vue';

export default {
    name: 'BookingsComp',
    props: {
        origin: {
            type: String,
            default: 'present',
        },
    },
    data() {
        return {
            is_less_filter: true,
            is_master_agent: !window.KLK_USER_INFO.agent_type,
            has_batch_order: (window.KLK_USER_INFO.permissions || {})
                .batch_order, //有大宗采购
            //异步获取的基本信息
            country_city_list: [],
            city_list: [],
            agentAccountInfoList: [], //agent列表
            searchObj: {
                searchType: 0,
                searchNo: '',
            },
            //表单数据
            bookingDate: {
                dateStart: subMonths(startOfDay(new Date()), 1),
                dateEnd: startOfDay(new Date()),
            },
            travelDate: {
                dateStart: '',
                dateEnd: '',
            },
            destinationInfo: {
                selectType: '',
                inputValue: '-1',
            },
            activityInfo: {
                selectType: 0,
                inputValue: '',
            },
            activityLoading: false,
            activityList: [],
            agentId: '', //booking_account
            travelerInfo: {
                selectType: 0,
                inputValue: '',
            },
            paymentTypes: '',
            bookingChannel: '',
            refundOfNot: 0,
            bookingStatus: -1,
            voucherBookingStatus: 0,
            participate_start_time: undefined,
            participate_end_time: undefined,

            //分页
            batch_order: {
                pagination: {
                    total: 0,
                    pageSize: 5,
                    curPage: 1,
                },
                originalData: [],
                tableData: [],
            },
            ordinary_order: {
                pagination: {
                    total: 0,
                    pageSize: 5,
                    curPage: 1,
                },
                tableData: [],
            },
            hotel_order: {
                pagination: {
                    total: 0,
                    pageSize: 5,
                    curPage: 1,
                },
                tableData: [],
            },
            hotel_search: {},
            order_type: 'ordinary', // 订单类型 ordinary hotel batch
            currencySymbol: '', //显示货币类型
            order_status: 0, // batch order status
            loading: false,
            isReset: false,
            hotelParam: {},
        };
    },
    computed: {
        // 订单类型
        orderTypeMap() {
            return {
                ordinary: this.ordinary_order,
                hotel: this.hotel_order,
                batch: this.batch_order,
                voucher: this.batch_order,
            };
        },
        voucherTypesList() {
            return voucher_types_list();
        },
        paymentTypeList() {
            return payment_types_list();
        },
        bookingStatusList() {
            return booking_status_list();
        },
        order_status_list() {
            return [
                {
                    label: this.$t('all'),
                    value: 0,
                },
                {
                    label: this.$t('pending_payment'),
                    value: 2,
                },
                {
                    label: this.$t('pending_confirm'),
                    value: 1,
                },
                {
                    label: this.$t('pending_ticketing'),
                    value: 3,
                },
                {
                    label: this.$t('complete'),
                    value: 4,
                },
                {
                    label: this.$t('canceled'),
                    value: 5,
                },
                {
                    label: this.$t('order_generating'),
                    value: 6,
                },
            ];
        },
        booking_channel_list() {
            return [
                {
                    value: '',
                    label: this.$t('all'),
                },
                {
                    value: 'amp',
                    label: this.$t('booking_channel_amp'),
                },
                {
                    value: 'agentapi',
                    label: this.$t('booking_channel_agent_api'),
                },
            ];
        },
        activityType() {
            return [
                {
                    value: 0,
                    label: this.$t('activity_id'),
                },
                {
                    value: 1,
                    label: this.$t('activity_name'),
                },
            ];
        },
        travelerTypeList() {
            return [
                {
                    label: this.$t('traveler_mobile'),
                    value: 0,
                },
                {
                    label: this.$t('traveler_email'),
                    value: 1,
                },
            ];
        },
        refoundList() {
            return [
                {
                    label: this.$t('all'),
                    value: 0,
                },
                {
                    label: this.$t('yes'),
                    value: 1,
                },
            ];
        },
        agentAccountInfoListIncludeAll() {
            let list = this.agentAccountInfoList.slice(0);
            list.unshift({ email: this.$t('all'), agentId: '' });
            return list;
        },
        batchData() {
            let searchData = {
                order_no: this.getValueByType('no', 0),
                booking_no: this.getValueByType('no', 1),
                booking_agent_id: this.agentId,
                activity_id: this.activityInfo.inputValue,
            };
            if (
                this.order_type === 'batch' &&
                this.participate_start_time &&
                this.participate_end_time
            ) {
                searchData.participate_start_time = this.formatDate(
                    this.participate_start_time,
                );
                searchData.participate_end_time = this.formatDate(
                    this.participate_end_time,
                );
            }
            if (this.bookingDate.dateStart && this.bookingDate.dateEnd) {
                searchData.start_time = this.formatDate(
                    this.bookingDate.dateStart,
                );
                searchData.end_time = this.formatDate(this.bookingDate.dateEnd);
            }
            if (this.order_type === 'batch' && this.order_status) {
                searchData.batch_status = this.order_status;
            }
            if (this.order_type === 'voucher') {
                searchData.status = this.voucherBookingStatus;
                searchData.participate_start_time = this.formatDate(
                    this.participate_start_time,
                );
                searchData.participate_end_time = this.formatDate(
                    this.participate_end_time,
                );
            }
            return searchData;
        },
        formData() {
            return {
                activity_id: this.activityInfo.inputValue,
                country_id: this.destinationInfo.selectType,
                city: this.destinationInfo.inputValue,
                participation_date_start_time: this.travelDate.dateStart
                    ? this.formatDateRFC3339(
                          startOfDay(this.travelDate.dateStart),
                      )
                    : '',
                participation_date_end_time: this.travelDate.dateEnd
                    ? this.formatDateRFC3339(
                          startOfDay(addDays(this.travelDate.dateEnd, 1)),
                      )
                    : '',
                traveler_email: this.getValueByType('traveler', 1),
                traveler_phone: this.getValueByType('traveler', 0),
                has_refund: this.refundOfNot,
                agent_id: this.agentId,
                payment_gateway: this.paymentTypes,
                booking_channel: this.bookingChannel,
                order_no: this.getValueByType('no', 0),
                booking_number: this.getValueByType('no', 1),
                related_booking_number: this.getValueByType('no', 2),
                ticket_status: this.bookingStatus,
                start_time: this.formatDateRFC3339(this.bookingDate.dateStart),
                end_time: this.formatDateRFC3339(
                    addDays(this.bookingDate.dateEnd, 1),
                ),
            };
        },
        originParam() {
            return this.origin === 'history'
                ? {
                      history: 1,
                  }
                : {};
        },
    },
    watch: {
        order_type: {
            handler: function(val) {
                // loadhotelData的时候需要拿到子组件data
                this.$nextTick(() => {
                    this.loadDataBy(val, 1);
                    this.hotelParam = this.getHotelParams();
                });
            },
            immediate: true,
        },
    },
    components: {
        bookingHeader,
        bookingDateRange,
        bookingInputWrapper,
        bookingSelect,
        resultList,
        hotelSearch,
    },
    created() {
        this.agentId = this.$route.query.sub_account || ''; //从sub_account 查看订单带着sub_account跳转过来
    },
    mounted() {
        this.loadAgentAccountEmails();
        // 支持携带参数跳转本页面 请求不同订单接口
        this.order_type = this.$route.query.tab || 'ordinary';
        this.loadCountryCity();
    },
    methods: {
        formatDateRFC3339,
        tabClick(flag) {
            if (this.order_type !== flag) {
                this.order_type = flag;
                this.$router.push({
                    query: Object.assign({}, this.$route.query, {
                        tab: this.order_type,
                    }),
                });
            }
        },

        //amp逻辑：如果是直接搜索header的order by 则不需要带上时间
        loadDataBy(type, page, searchByNo = false) {
            // 分页
            page
                ? (this.orderTypeMap[this.order_type].pagination.curPage = page)
                : '';
            if (type === 'batch') {
                this.loadBatchOrderData('', searchByNo);
            } else if (type === 'voucher') {
                this.loadVoucherData();
            } else if (type === 'hotel') {
                this.loadHotelData(searchByNo);
            } else {
                this.loadData(searchByNo);
            }
        },
        handleSearch() {
            this.loadDataBy(this.order_type, 1);
        },
        getValueByType(type, number) {
            return type === 'no'
                ? this.searchObj.searchType === number
                    ? this.searchObj.searchNo
                    : ''
                : this.travelerInfo.selectType === number
                ? this.travelerInfo.inputValue
                : '';
        },
        searchByBasicNo() {
            this.loadDataBy(this.order_type, 1, true);
        },
        setDefaultValue(value) {
            return value ? value : 'Not Selected';
        },
        updateRelatedCityList() {
            this.city_list = this.city_list.splice(1);
            let country_city =
                find(this.country_city_list, item => {
                    return item.country_id === this.destinationInfo.selectType;
                }) || {};
            this.city_list = country_city.country_id ? country_city.cities : [];
            this.city_list.unshift({
                name: this.$t('city'),
                id: -1,
            });
            this.destinationInfo.inputValue = -1;
        },
        setFixedHeight() {
            const th = document.querySelector('.fixed_right th');
            const tdList = document.querySelectorAll(
                '.fixed_right tr td:last-child',
            );
            th.style.height =
                document.querySelector(
                    '.batch_order_table_wrapper tr th:last-child',
                ).offsetHeight + 'px';
            tdList.forEach((td, index) => {
                td.style.height =
                    this.$refs['tdRef' + index].offsetHeight + 'px';
            });
        },
        handleCurrentChange(curPage) {
            this.loadDataBy(this.order_type, curPage);
        },
        loadBatchOrderData(status, searchByNo = false) {
            let searchData,
                pagination = this.batch_order.pagination;

            searchData = {
                page_no: pagination.curPage,
                page_size: pagination.pageSize,
                order_no: this.getValueByType('no', 0),
                booking_no: this.getValueByType('no', 1),
                related_booking_number: this.getValueByType('no', 2),
                booking_agent_id: this.agentId,
                activity_id: this.activityInfo.inputValue,
                payment_gateway: this.paymentTypes,
            };
            Object.assign(searchData, this.originParam);
            if (
                this.order_type === 'batch' &&
                this.participate_start_time &&
                this.participate_end_time
            ) {
                searchData.participate_start_time = this.formatDate(
                    this.participate_start_time,
                );
                searchData.participate_end_time = this.formatDate(
                    this.participate_end_time,
                );
            }
            if (!searchByNo) {
                searchData.start_time = this.formatDate(
                    this.bookingDate.dateStart,
                );
                searchData.end_time = this.formatDate(this.bookingDate.dateEnd);
            }

            status
                ? (searchData.batch_status = status)
                : (this.order_status = 0);

            let self = this;

            function processData(data) {
                //为了把对象转换成表格列表的形式
                let list = [];

                function getActivityRowSpan(activityItem) {
                    let rowspan = 0;
                    activityItem.packages.forEach(function(packageItem) {
                        packageItem.rowspan =
                            packageItem.ticket_price_count.length;
                        rowspan += packageItem.rowspan;
                    });
                    return rowspan;
                }
                function getPaymentGateway(payment_gateway) {
                    payment_gateway =
                        payment_gateway === 'newpaypal'
                            ? 'paypal'
                            : payment_gateway;
                    payment_gateway = payment_gateway
                        ? (
                              self.paymentTypeList.find(
                                  item => item.value === payment_gateway,
                              ) || {}
                          ).label || ''
                        : '';
                    return payment_gateway;
                }
                function getRowData(
                    activityItem,
                    packageItem,
                    repeatActivity,
                    repeatPackage,
                    unitsInfoItem,
                    row,
                ) {
                    const baseRow = {
                        settlementPrice: unitsInfoItem.settlementPrice,
                        units: unitsInfoItem.units,
                        expiryDate: packageItem.expire_date,
                    };
                    if (+repeatActivity === 1) {
                        row = {
                            show_package: true,
                            show_activity: true,
                            batch_status: activityItem.batch_status,
                            totalAmount: activityItem.total_amount,
                            poundage: activityItem.poundage
                                ? activityItem.poundage
                                : 0,
                            paymentAmount:
                                self.currencySymbol +
                                activityItem.payment_amount,
                            paymentGateway: activityItem.payment_gateway,
                            activity_rowspan: activityItem.rowspan,
                            package_rowspan: packageItem.rowspan,
                            createDate: format(
                                activityItem.booking_date,
                                'YYYY-MM-DD HH:mm:ss',
                            ),
                            bookingNumber: activityItem.order_no,
                            orderId: activityItem.order_id,
                            activityName: activityItem.activity_name,
                            activityId: activityItem.activity_id,
                            agentId: activityItem.agent_id,
                            packages: activityItem.packages,
                            packageName: packageItem.package_name,
                            relatedBookingNumber:
                                activityItem.agent_booking_ref_no,
                            statusStr:
                                (
                                    self.order_status_list.find(
                                        status =>
                                            status.value ===
                                            activityItem.batch_status,
                                    ) || {}
                                ).label || '',
                            voucherUrl:
                                activityItem.batch_status === 4
                                    ? `/v1/agentwebserv/batchorder/download?orderId=${activityItem.order_id}`
                                    : '',
                        };
                    } else {
                        if (+repeatPackage === 1) {
                            row = {
                                show_package: true,
                                show_activity: false,
                                package_rowspan: packageItem.rowspan,
                                packages: activityItem.packages,
                                packageName: packageItem.package_name,
                            };
                        } else {
                            row = {
                                show_package: false,
                                show_activity: false,
                            };
                        }
                    }
                    return Object.assign(row, baseRow);
                }
                data.forEach(function(activityItem) {
                    let repeatActivity = 1;
                    activityItem.rowspan = getActivityRowSpan(activityItem);
                    activityItem.payment_gateway = getPaymentGateway(
                        activityItem.payment_gateway,
                    );
                    activityItem.packages.forEach(function(packageItem) {
                        packageItem.rowspan =
                            packageItem.ticket_price_count.length;
                        let repeatPackage = 1; //控制td的显示
                        packageItem.ticket_price_count.forEach(function(
                            unitsInfoItem,
                        ) {
                            let row = {};
                            unitsInfoItem.units =
                                unitsInfoItem.price_name +
                                ' x ' +
                                unitsInfoItem.count;
                            unitsInfoItem.settlementPrice =
                                self.currencySymbol +
                                (unitsInfoItem.selling_price
                                    ? unitsInfoItem.selling_price
                                    : 0);
                            row = getRowData(
                                activityItem,
                                packageItem,
                                repeatActivity,
                                repeatPackage,
                                unitsInfoItem,
                                row,
                            );
                            row.batchOrderId = activityItem.id; //重新把bookingId补上
                            row.bookingId = activityItem.id; //重新把bookingId补上

                            list.push(row);
                            repeatActivity++;
                            repeatPackage++;
                        });
                    });
                    // activity.rowsplan = item.package.length ;
                });

                return list;
            }

            klook.ajaxGet(
                urlObj.agent_account_batch_order_bookings,
                searchData,
                resp => {
                    if (resp.success) {
                        const result = resp.result || {};
                        this.batch_order.pagination.total = result.total_count;
                        this.batch_order.originalData = JSON.parse(
                            JSON.stringify(result.orders),
                        );
                        // 前端根据booking_date降序排序
                        this.batch_order.tableData = processData.call(
                            this,
                            orderBy(
                                result.orders,
                                ['booking_date'],
                                ['desc'],
                            ) || [],
                        );
                    } else {
                        this.batch_order.tableData = [];
                        console.log(resp);
                    }
                },
            );
        },
        loadVoucherData() {
            const pagination = this.batch_order.pagination;
            const searchData = {
                page_no: pagination.curPage,
                page_size: pagination.pageSize,
                order_no: this.getValueByType('no', 0),
                booking_number: this.getValueByType('no', 1),
                related_booking_number: this.getValueByType('no', 2),
                booking_agent_id: this.agentId,
                activity_id: this.activityInfo.inputValue,
                participate_start_time: this.formatDate(
                    this.participate_start_time,
                ),
                participate_end_time: this.formatDate(
                    this.participate_end_time,
                ),
                start_time: this.formatDate(this.bookingDate.dateStart),
                end_time: this.formatDate(this.bookingDate.dateEnd),
                status: this.voucherBookingStatus,
            };

            klook.ajaxGet(urlObj.unitorder_search, searchData, resp => {
                if (resp.success) {
                    const result = resp.result || {};
                    this.batch_order.pagination.total = result.total_count;
                    this.batch_order.originalData = JSON.parse(
                        JSON.stringify(result.orders),
                    );
                    // 前端根据booking_date降序排序
                    this.batch_order.tableData = result.orders || [];
                } else {
                    this.batch_order.tableData = [];
                }
            });
        },
        loadData(searchByNo = false) {
            // 普通订单分页根据接口调整
            let searchData,
                pagination = this.ordinary_order.pagination;
            searchData = {
                page_no: pagination.curPage,
                page_size: pagination.pageSize,
                activity_id: this.activityInfo.inputValue,
                country_id: this.destinationInfo.selectType,
                city: this.destinationInfo.inputValue,
                participation_date_start_time: this.travelDate.dateStart
                    ? startOfDay(this.travelDate.dateStart)
                    : '',
                participation_date_end_time: this.travelDate.dateEnd
                    ? startOfDay(addDays(this.travelDate.dateEnd, 1))
                    : '',
                traveler_email: this.getValueByType('traveler', 1),
                traveler_phone: this.getValueByType('traveler', 0),
                has_refund: this.refundOfNot,
                agent_id: this.agentId,
                payment_gateway: this.paymentTypes,
                booking_channel: this.bookingChannel,
                order_no: this.getValueByType('no', 0),
                booking_number: this.getValueByType('no', 1),
                related_booking_number: this.getValueByType('no', 2),
                ticket_status: this.bookingStatus,
            };
            Object.assign(searchData, this.originParam);
            if (!searchByNo) {
                searchData.start_time = this.bookingDate.dateStart;
                //+1天
                searchData.end_time = addDays(this.bookingDate.dateEnd, 1);
            }
            function processData(data) {
                data.forEach(obj => {
                    obj.payment_gateway =
                        obj.payment_gateway === 'newpaypal'
                            ? 'paypal'
                            : obj.payment_gateway;
                    obj.payment_gateway =
                        (
                            this.paymentTypeList.find(
                                item => item.value === obj.payment_gateway,
                            ) || {}
                        ).label || '';
                    obj.total_amount = this.currencySymbol + obj.total_amount;
                    obj.tickets.forEach(ticket => {
                        ticket.participation_date = ticket.participation_date
                            ? format(ticket.participation_date, 'YYYY-MM-DD')
                            : '';
                        ticket.sub_total =
                            this.currencySymbol +
                            klook.formatPriceThousands(ticket.sub_total_amount);
                        ticket.voucher_url =
                            +ticket.status === 4 ? ticket.voucher_url : ''; //4表示confirmed,只有confirmed的ticket才有voucher下载
                        ticket.refunded_total =
                            this.currencySymbol +
                            klook.formatPriceThousands(ticket.refunded_amount);
                        ticket.status_label =
                            (
                                this.bookingStatusList.find(
                                    item => item.value === ticket.status,
                                ) || {}
                            ).label || '';
                    });
                    // 是否展开
                    obj.is_more = false;
                });
                return data;
            }
            this.loading = true;
            klook.ajaxGet(urlObj.agent_account_bookings, searchData, resp => {
                this.loading = false;
                if (resp.success) {
                    //解码货币类型
                    const result = resp.result || {};
                    this.ordinary_order.pagination.total = result.count;
                    this.currencySymbol = find(
                        this.currencyList,
                        item => item.value === result.currency_code,
                    ).title.split(' | ')[1];
                    this.ordinary_order.tableData = processData.call(
                        this,
                        result.list || [],
                    );
                } else {
                    this.ordinary_order.tableData = [];
                    console.log(resp);
                }
            });
        },
        loadAgentAccountEmails() {
            // 获取的agent email
            klook.ajaxGet(urlObj.agent_account_emails, resp => {
                if (resp.success) {
                    this.agentAccountInfoList = resp.result || [];
                } else {
                    console.error(resp);
                }
            });
        },
        loadCountryCity() {
            klook.ajaxGet(urlObj.country_city, resp => {
                if (resp.success) {
                    this.country_city_list = resp.result || [];
                    this.country_city_list.unshift({
                        country_id: '',
                        country_name: this.$t('country_or_regional'),
                    });
                } else {
                    console.error(resp);
                }
            });
        },
        formatDate(date) {
            return date ? format(date, 'YYYY-MM-DD') : '';
        },
        getActivityList(value) {
            this.activityLoading = true;
            let searchParam = {
                sales_channel: 2,
                query: value,
            };
            klook.ajaxGet(urlObj.search_common, searchParam, resp => {
                this.activityLoading = false;
                if (resp.success) {
                    this.activityList = resp.result.activities || [];
                } else {
                    this.activityList = [];
                }
            });
        },
        getHotelSearchRef(property) {
            return this.$refs.hotelSearch && this.$refs.hotelSearch[property];
        },
        getHotelParams(searchByNo) {
            let deleteData = [
                'display_status',
                'hotel_id',
                'klook_order_no',
                'booking_no',
                'agent_booking_ref_no',
                'booking_date_start',
                'booking_date_end',
                'check_in_start',
                'check_in_end',
                'check_out_start',
                'check_out_end',
            ];
            let searchData;
            searchData = {
                page_no: this.hotel_order.pagination.curPage,
                page_size: this.hotel_order.pagination.pageSize,
                display_status: this.getHotelSearchRef('order_status'),
                hotel_id: this.getHotelSearchRef('hotel_id'),
                klook_order_no: this.getValueByType('no', 0),
                booking_no: this.getValueByType('no', 1),
                agent_booking_ref_no: this.getValueByType('no', 2),
                is_history: this.origin === 'history',
            };
            // 如果不是订单搜索 加上时间参数
            if (!searchByNo) {
                // 处理date参数

                this.$refs.hotelSearch &&
                    this.$refs.hotelSearch.handleDateRange();
                Object.keys(this.getHotelSearchRef('date') || []).forEach(
                    key => {
                        searchData[key] = this.$refs.hotelSearch.date[key];
                    },
                );
            }

            deleteData.forEach(key => {
                const value = searchData[key];
                if (key === 'display_status') {
                    // value === -1 ? delete searchData[key] : '';
                } else {
                    !value && delete searchData[key];
                }
            });
            return searchData;
        },
        loadHotelData(searchByNo = false) {
            const searchData = this.getHotelParams(searchByNo);
            const processData = tableData => {
                tableData = tableData.map(item => {
                    item.booking_time_utc = item.booking_time;
                    item.order_no = item.klook_order_no;
                    item.total_amount = `${
                        find(
                            this.currencyList,
                            code => code.value === item.currency,
                        ).title.split(' | ')[1]
                    }${item.total_amount}`;
                    return item;
                });

                return tableData;
            };
            this.loading = true;
            klook.ajaxPostJSON(urlObj.hotel.list, searchData, resp => {
                this.loading = false;
                if (
                    resp.success &&
                    resp.result.data &&
                    resp.result.data.length
                ) {
                    this.hotel_order.tableData =
                        processData(resp.result.data) || [];
                    this.hotel_order.pagination.total = (
                        resp.result || {}
                    ).total_size;
                } else {
                    this.hotel_order.tableData = [];
                    console.log(resp.error);
                }
            });
        },
        handleReset() {
            // 清空hotel搜索数据
            this.hotel_order.pagination.curPage = 1;
            this.hotel_order.pagination.pageSize = 5;
            this.searchObj.searchType = 0;
            this.searchObj.searchNo = '';
            this.isReset = true;
        },
        handleHotelReset() {
            this.isReset = false;
            this.loadHotelData();
        },
        handleExportData() {
            this.hotelParam = this.getHotelParams();
        },
        disabledDateBooking(day) {
            if (!this.participate_start_time) {
                return false;
            }
            const date = parse(this.participate_start_time);
            if (!isValid(date)) {
                return false;
            }
            return isBefore(day, date);
        },
    },
};
</script>

<style lang="scss">
.my_bookings {
    .history_tips {
        color: #ff9d26;
        margin: 10px 0;
        .tips_nav {
            color: var(--primary-color);
            margin: 0 2px;
        }
    }
    .content {
        .input_wrapper {
            display: inline-block;
            margin-right: 10px;
            width: 250px;
            .input_label {
                padding: 8px 0;
            }
        }
        .filters_detail {
            > div {
                display: inline-block;
            }
        }
        .filters_show {
            display: flex;
            justify-content: center;
            align-items: center;
            margin-top: 20px;
            color: #b2b2b2;
            cursor: pointer;
            i {
                margin-left: 5px;
                border-left: 8px solid transparent;
                border-right: 8px solid transparent;
            }
            .less_filters {
                border-bottom: 8px solid #b2b2b2;
            }
            .more_filters {
                border-top: 8px solid #b2b2b2;
            }
        }
        .payment_type {
            display: inline-block;
        }
        .search-btn {
            margin: 24px 0 30px 0;
            .search {
                margin-right: 6px;
            }
        }
    }
    .clickable_text {
        color: var(--primary-color);
    }
    input[disabled] {
        background: #fafafa;
        cursor: not-allowed;
        color: #333;
    }
}
</style>
