<template>
    <table-template
        ref="hotelList"
        class="hotel-list"
        :tableData="tableData"
        :cols="cols"
        :is-hotel="true"
        :currencySymbol="currencySymbol"
        @move-to-detail="moveToDetail"
        @submit-booking-no="sbumitBookingNo"
    >
        <template v-slot:default="slotProps">
            <div class="klk-booking-tbody">
                <div class="klk-tr-body">
                    <div class="klk-tr">
                        <div style="width: 180px" class="hotel-info">
                            <span
                                @click="MoveToActivity(slotProps.row.hotel_id)"
                                >{{ slotProps.row.hotel_name }}</span
                            >
                        </div>
                        <div style="width: 180px" class="guest-rooms">
                            <p>
                                {{
                                    `${$t(
                                        slotProps.row.adult > 1
                                            ? 'hotel.adults_with_number'
                                            : 'hotel.adultone_with_number',
                                        [slotProps.row.adult + ' x '],
                                    )}${
                                        slotProps.row.child > 0
                                            ? ', ' +
                                              $t(
                                                  slotProps.row.child > 1
                                                      ? 'hotel.children_with_number'
                                                      : 'hotel.childone_with_number',
                                                  [slotProps.row.child + ' x '],
                                              ) +
                                              ' ( ' +
                                              $t(
                                                  'hotel.confirm_booking_info_age',
                                              ) +
                                              ': ' +
                                              slotProps.row.ages +
                                              ' )'
                                            : ''
                                    }`
                                }}
                            </p>
                            <p>
                                {{
                                    `${slotProps.row.rooms} x ${slotProps.row.room_name}`
                                }}
                            </p>
                        </div>
                        <div style="width: 180px" class="check-in-out">
                            {{ slotProps.row.check_in }} ~
                            {{ slotProps.row.check_out }}
                            <p>
                                {{
                                    `(${$t(
                                        slotProps.row.nights > 1
                                            ? 'hotel.nights_with_number'
                                            : 'hotel.night_one',
                                        [slotProps.row.nights],
                                    )})`
                                }}
                            </p>
                        </div>
                        <!-- <div style="width: 180px">
                            {{ slotProps.row.guest_name }}
                        </div> -->
                        <div style="width: 180px" class="display-status">
                            <span
                                :class="
                                    `status-${slotProps.row.display_status}`
                                "
                            >
                                {{
                                    getStatusText(slotProps.row.display_status)
                                }}
                            </span>
                        </div>
                        <div style="width: 180px" class="voucher_url">
                            <a
                                href="javascript:;"
                                @click="
                                    openVouch(
                                        slotProps.row.display_status === 4
                                            ? slotProps.row.voucher_url
                                            : '',
                                    )
                                "
                                :class="
                                    slotProps.row.display_status === 4 &&
                                    slotProps.row.voucher_url
                                        ? 'clickable_text'
                                        : 'disabled-tap'
                                "
                            >
                                {{ $t('view_voucher') }}</a
                            >
                        </div>
                    </div>
                </div>
            </div>
        </template>
    </table-template>
</template>

<script>
import { booking_status_list } from '@/common/const_data';
import { openVouch } from '@/common/util';
import urlObj from '@/common/url';
import tableTemplate from '../components/table-template.vue';

export default {
    name: 'klk-booking-table',
    data() {
        return {};
    },
    props: {
        tableData: {
            type: Array,
            required: true,
        },
        currencySymbol: {
            type: String,
        },
        origin: {
            type: String,
            required: true,
            default: 'present',
        },
    },
    components: {
        tableTemplate,
    },
    computed: {
        statusList() {
            return booking_status_list();
        },
        cols() {
            return [
                {
                    label: this.$t('hotel.hotel_info'),
                    itemStyle: {
                        width: '180px',
                    },
                },
                {
                    label: this.$t('hotel.room_and_quantity'),
                    itemStyle: {
                        width: '180px',
                    },
                },
                {
                    label: this.$t('hotel.check_in_out_date'),
                    itemStyle: {
                        width: '180px',
                    },
                },
                // {
                //     label: this.$t('hotel.guest_name'),
                //     itemStyle: {
                //         width: '150px',
                //     },
                // },
                {
                    label: this.$t('status'),
                    itemStyle: {
                        width: '180px',
                    },
                },
                {
                    label: this.$t('actions'),
                    itemStyle: {
                        width: '180px',
                    },
                },
            ];
        },
    },
    mounted() {},
    methods: {
        openVouch,
        getStatusText(status) {
            return (
                (
                    this.statusList.find(item => {
                        return item.value === status;
                    }) || {}
                ).label || ''
            );
        },
        moveToDetail(book) {
            const url = `${window.location.origin}${window.KLK_LANG_PATH}/hotels/bookings_details?guid=${book.klook_order_no}&bookingNo=${book.booking_no}&userId=${book.user_id}&vertical_type=hotel_api&origin=${this.origin}`;

            window.open(url, '_blank');
        },
        MoveToActivity(id) {
            window.location.href = `${window.location.origin}${window.KLK_LANG_PATH}/hotels/detail/${id}`;
        },
        sbumitBookingNo(data) {
            const { index, value } = data;
            klook.ajaxPostJSON(
                urlObj.hotel.updateBookRefNo,
                {
                    ticket_guid: this.tableData[index].ticket_guid,
                    agent_booking_ref_no: value,
                    is_history: this.origin === 'history',
                },
                resp => {
                    if (resp.success) {
                        this.tableData[index].agent_booking_ref_no = value;
                        this.$refs.hotelList.edit_booking_row_index = -1;
                        this.$refs.hotelList.edit_booking_number_show = false;
                        this.$message({
                            message: this.$t('success'),
                            type: 'success',
                        });
                    } else {
                        this.$refs.hotelList.error_tip = resp.error.message;
                        console.log(resp.error);
                    }
                },
            );
        },
    },
};
</script>

<style lang="scss">
$table-border: 1px solid #d5d5d5;
.hotel-list {
    .hotel-info span {
        cursor: pointer;
    }
    .guest-rooms,
    .check-in-out {
        display: flex;
        flex-direction: column;
        > p {
            margin: 4px 0;
        }
    }
    .display-status {
        span {
            padding: 6px;
            border-radius: 2px;
        }
        .status-0 {
            background-color: #fffbf2;
            color: #ffab00;
        }
        .status-1 {
            background-color: #f5f5f5;
            color: #565656;
        }
        .status-4 {
            background-color: #f5fbf8;
            color: #36b37e;
        }
        .status-8 {
            background-color: #fff6f4;
            color: #ff5630;
        }
    }
    .voucher_url {
        .disabled-tap {
            color: #d5d5d5;
            cursor: not-allowed;
        }
        .clickable_text {
            color: var(--primary-color);
        }
    }
}
</style>
