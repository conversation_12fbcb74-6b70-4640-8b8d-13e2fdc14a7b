<template>
    <div class="hotel-search">
        <div class="booking_date_wrapper">
            <div class="input_wrapper">
                <div class="input_label">
                    <span>{{ $t('date_range') }}</span>
                </div>
                <klk-select
                    v-model="dateType"
                    :placeholder="$t('global.select.palceholder')"
                    @change="handleDateRange"
                >
                    <klk-option
                        v-for="(item, i) in BookingDateRange"
                        :key="i"
                        :value="i"
                        :label="item"
                    ></klk-option>
                </klk-select>
            </div>
            <double-datepicker
                v-model="bookingDate"
                @input="updateBookingDate"
            ></double-datepicker>
            <ul class="range_select">
                <li
                    v-for="(item, index) in dateShortCuts"
                    @click="setBookingDateRange(item)"
                    :key="index"
                    :class="
                        (activatedBookingDateShortCut === item.key &&
                            'option active') ||
                            'option'
                    "
                >
                    {{ item.label }}
                </li>
            </ul>
        </div>
        <div class="input_wrapper">
            <div class="input_label">
                <span>{{ $t('status') }}</span>
            </div>
            <klk-select
                :placeholder="$t('global.select.palceholder')"
                filterable
                v-model="order_status"
            >
                <klk-option
                    v-for="item in statusList"
                    :key="item.value"
                    :value="item.value"
                    :label="item.label"
                ></klk-option>
            </klk-select>
        </div>
        <div class="input_wrapper">
            <div class="input_label">
                <span>{{ $t('hotel.hotel_info') }}</span>
            </div>
            <klk-select
                v-loading="loading"
                ref="hotelInfo"
                :placeholder="$t('global.select.palceholder')"
                :filterable="filterable"
                v-model="hotel_id"
            >
                <klk-option
                    v-for="(option, i) in hotelList"
                    :key="i"
                    :value="option.svalue"
                    :label="option.title"
                ></klk-option>
            </klk-select>
        </div>
    </div>
</template>
<script>
import { booking_status_list } from '@/common/const_data';
import urlObj from '@/common/url';
import { subMonths, startOfDay, format, addDays, subYears } from 'date-fns';
import doubleDatepicker from '../components/booking-double-datepicker.vue';
import datePicker from '../../ticket/activity/package/package-sku-calendar.vue';

export default {
    name: 'hotel-search',
    props: {
        isReset: {
            type: Boolean,
            default: false,
        },
    },
    data() {
        return {
            isComposing: false,
            dateType: 0,
            bookingDate: [
                subMonths(startOfDay(new Date()), 1),
                startOfDay(new Date()),
            ],
            activatedBookingDateShortCut: 'last_1_month',
            hotel_id: '',
            order_status: -1,
            hotelList: [],
            date: {
                booking_date_start: '',
                booking_date_end: '',
                check_in_start: '',
                check_in_end: '',
                check_out_start: '',
                check_out_end: '',
            },
            loading: false,
            filterable: true,
        };
    },
    watch: {
        hotelList(val) {
            this.hotelList = val;
        },
        isReset(val) {
            // 重置搜索条件
            if (val) {
                this.dateType = 0;
                // reset 时间设为初始的last_1_month
                this.bookingDate = [
                    subMonths(startOfDay(new Date()), 1),
                    startOfDay(new Date()),
                ];
                this.activatedBookingDateShortCut = 'last_1_month';
                this.hotel_id = '';
                this.order_status = -1;
                this.handleDateRange();
                // 重置reset
                this.$emit('reset');
            }
        },
    },
    computed: {
        BookingDateRange() {
            return [
                this.$t('booking_date'),
                this.$t('hotel.check_in_date'),
                this.$t('hotel.check_out_date'),
            ];
        },
        statusList() {
            return booking_status_list();
        },
        dateShortCuts() {
            return [
                //日期范围选择快捷键
                {
                    key: 'last_1_month',
                    label: this.$t('last_1_month'),
                    startDate: subMonths(startOfDay(new Date()), 1),
                    endDate: startOfDay(new Date()),
                },
                {
                    key: 'last_3_month',
                    label: this.$t('last_3_month'),
                    startDate: subMonths(startOfDay(new Date()), 3),
                    endDate: startOfDay(new Date()),
                },
                {
                    key: 'last_1_year',
                    label: this.$t('last_1_year'),
                    startDate: subYears(startOfDay(new Date()), 1),
                    endDate: startOfDay(new Date()),
                },
            ];
        },
    },
    mounted() {
        this.loadHotelList = this.debounce(this.loadHotelList, 1000);
        // hotel-info 监听click、input模拟远程搜索
        this.$nextTick(() => {
            const selectInput = this.$refs.hotelInfo.$refs.input || '';
            const selsctDiv = this.$refs.hotelInfo.$refs.reference || '';
            if (selsctDiv) {
                selsctDiv.addEventListener('click', () => {
                    this.filterable = true;
                    this.hotel_id = '';
                });
            }
            if (selectInput) {
                selectInput.addEventListener('input', e => {
                    this.onInput(e);
                });

                //兼容输入中文还在输入时候就出发input事件
                selectInput.addEventListener('compositionstart', () => {
                    this.isComposing = true;
                });

                selectInput.addEventListener('compositionend', e => {
                    if (this.isComposing) {
                        this.isComposing = false;
                        this.onInput(e);
                    }
                });
                selectInput.addEventListener('blur', () => {
                    this.$refs.hotelInfo.isFocus = false;
                });
            }
        });
    },
    components: {
        doubleDatepicker,
        datePicker,
    },
    methods: {
        onInput(e) {
            if (this.isComposing) return;
            const text = e.target.value;
            if (text) {
                this.loadHotelList(text);
            }
        },
        debounce(fn, delay) {
            let timer;
            return function(...args) {
                if (timer) clearTimeout(timer);
                timer = setTimeout(() => fn.apply(this, args), delay);
            };
        },
        setBookingDateRange(obj) {
            this.activatedBookingDateShortCut = obj.key;
            this.bookingDate = [obj.startDate, obj.endDate];
        },
        updateBookingDate(date) {
            this.bookingDate = [...date];
            // this.searchChange()
        },
        loadHotelList(keyword) {
            this.loading = true;
            klook.ajaxGet(
                urlObj.hotel.suggest,
                {
                    search_type: 'hotel',
                    keyword: keyword,
                },
                resp => {
                    this.loading = false;
                    if (resp && resp.success) {
                        this.hotelList =
                            (resp.result && resp.result.suggests) || [];
                        this.filterable = false;
                    } else {
                        this.hotelList = [];
                    }
                },
            );
        },
        handleDateRange() {
            // 清空日期
            Object.keys(this.date).map(key => {
                this.date[key] = '';
            });
            let [startDate, endDate] = this.bookingDate;
            // 到下一天的0点
            endDate = addDays(endDate, 1);
            // booking_date格式不一样 0时区
            if (this.dateType === 0) {
                this.date.booking_date_start = format(
                    this.getZoreTime(startDate),
                    'YYYY-MM-DD HH:mm:ss',
                );
                this.date.booking_date_end = format(
                    this.getZoreTime(endDate),
                    'YYYY-MM-DD HH:mm:ss',
                );
            }
            if (this.dateType === 1) {
                this.date.check_in_start = format(startDate, 'YYYY-MM-DD');
                this.date.check_in_end = format(endDate, 'YYYY-MM-DD');
            }
            if (this.dateType === 2) {
                this.date.check_out_start = format(startDate, 'YYYY-MM-DD');
                this.date.check_out_end = format(endDate, 'YYYY-MM-DD');
            }
        },
        getZoreTime(time) {
            // 0时区
            let len = time.getTime();
            let offset = time.getTimezoneOffset() * 60000;
            let utcTime = len + offset;
            return new Date(utcTime);
        },
    },
};
</script>

<style lang="scss">
.booking_date_wrapper {
    display: flex;
    align-items: flex-end;
}

.input_wrapper {
    display: inline-block;
    margin-right: 10px;
    width: 250px;
    .input_label {
        padding: 8px 0;
    }
}
.range_select {
    display: flex;
    align-items: center;
    font-size: 14px;
    color: #333333;
    .option {
        display: inline-block;
        cursor: pointer;
        margin-left: 16px;
        line-height: 44px;
    }
    .active {
        color: var(--primary-color);
    }
}
</style>
