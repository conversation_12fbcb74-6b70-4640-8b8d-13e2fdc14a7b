<template lang="html">
    <booking-list-comp origin="present">
        <p v-if="showHistoryList" class="history_tips">
            <span v-html="tips"></span>
        </p>
    </booking-list-comp>
</template>

<script>
import bookingListComp from './components/booking-list-comp.vue';

export default {
    name: 'Bookings',
    data() {
        return {};
    },
    components: {
        bookingListComp,
    },
    computed: {
        tips() {
            return this.$t('booking_after_this_date', {
                0: `<a
                class="tips_nav"
                href="${
                    window.KLK_LANG === 'en' ? '' : '/' + window.KLK_LANG
                }/history_bookings"
                >${this.$t('history_bookings')}</a
            >`,
            });
        },
        showHistoryList() {
            const { tab } = this.$route.query;
            return klook.showHistoryList && tab !== 'voucher';
        },
    },
};
</script>

<style lang="scss"></style>
