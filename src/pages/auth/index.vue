<template>
    <div class="auth-page" v-loading="loading">
        <div class="auth-content">
            <div class="header">
                <h2>{{ $t('znd_welcome') }}</h2>
            </div>
            <div class="desc">
                <p class="desc_1" v-html="$t('znd_auth_tips_1')"></p>
                <p class="desc_2" v-html="$t('znd_auth_tips_2')"></p>
            </div>
            <div class="znd_auth_settlement">
                <p
                    class="settlement_tip_1"
                    v-html="$t('znd_auth_settlement_desc_1')"
                ></p>
                <p
                    class="settlement_tip_2"
                    v-html="$t('znd_auth_settlement_desc_2')"
                ></p>
                <p class="settlement_tip_2" v-html="$t('znd_auth_remind')"></p>
            </div>
            <div class="action">
                <klk-button @click="handleCancel" type="outlined"
                    >{{ $t('cancel') }}
                </klk-button>
                <klk-button @click="handleAuth">{{
                    $t('znd_auth_login')
                }}</klk-button>
            </div>
        </div>
    </div>
</template>

<script>
import urlObj from '@/common/url';
import { ZND_HOST } from '@/common/const_data';

export default {
    name: 'auth',
    data() {
        return {
            loading: false,
            client_id: '',
            redirect_uri: '',
            status: '',
            state: '',
        };
    },
    components: {},
    computed: {},
    methods: {
        async checkAuth() {
            this.loading = true;
            await klook.ajaxGet(urlObj.check_auth_status, {}, resp => {
                if (resp.success) {
                    this.client_id = resp.result.client_id;
                    this.redirect_uri = resp.result.redirect_uri;
                    this.status = resp.result.status;
                    if (resp.result.status === 'authorized') {
                        this.handleAuth();
                    } else {
                        this.loading = false;
                    }
                } else {
                    this.loading = false;
                    this.$message({
                        type: 'error',
                        message: resp.error.message || 'failed',
                    });
                }
            });
        },
        handleAuth() {
            try {
                klook.ajaxGet(
                    urlObj.action_authorize,
                    {
                        response_type: 'json',
                        client_id: this.client_id,
                        redirect_uri: this.redirect_uri,
                        state:
                            this.state ||
                            '0ac55248-9601-43d5-9b5c-634ed64051ad',
                    },
                    resp => {
                        if (resp.success && resp.result) {
                            // 对redirect url做白名单限制
                            const redirectURL = (
                                new URL(resp.result.redirect_uri) || {}
                            ).host;
                            if (ZND_HOST.includes(redirectURL)) {
                                window.open(resp.result.redirect_uri, '_blank');
                            }
                            if (this.$route.query.lastPageName) {
                                this.$router.push(
                                    this.$route.query.lastPageName,
                                );
                            } else {
                                this.$router.push({ name: 'agent_index' });
                            }
                        } else {
                            this.$message({
                                type: 'error',
                                message:
                                    (resp.error && resp.error.message) ||
                                    'failed',
                            });
                        }
                    },
                );
            } catch (error) {
                this.loading = false;
                this.$message({
                    type: 'error',
                    message: error.message || 'failed',
                });
            }
        },
        handleCancel() {
            if (this.$route.query.lastPageName) {
                this.$router.push(this.$route.query.lastPageName);
            } else {
                this.$router.push({
                    name: 'agent_index',
                });
            }
            return;
        },
    },
    created() {
        if (this.$route.query.auth_redirect_url) {
            this.redirect_uri = this.$route.query.auth_redirect_url;
        }
        if (this.$route.query.state) {
            this.state = this.$route.query.state;
        }
        this.checkAuth();
    },
};
</script>

<style lang="scss">
.auth-content {
    margin: 20px auto;
    width: 760px;
    padding: 24px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-items: center;
    background-color: #f5f5f5;

    .header {
        text-align: center;
        color: #000;

        p {
            margin: 20px auto;
        }
    }

    .desc {
        width: 100%;
        margin-bottom: 20px;

        .desc_1,
        .desc_2 {
            margin: 16px 0;
        }
    }

    .settlement_tip_1 {
        font-size: 18px;
        font-weight: 600;
        margin-bottom: 10px;
    }

    .action {
        margin-top: 40px;
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: space-evenly;
    }

    p {
        a {
            color: var(--primary-color);
        }
    }

    .settlement_tip_2 {
        margin: 4px 0;
    }
}
</style>
