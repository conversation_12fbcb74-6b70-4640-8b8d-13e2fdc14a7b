<template lang="html">
    <article class="g_main agent_404">
        <div class="err_message">
            <h1>{{ $t('page_404_message') }}</h1>
            <p class="message_404" v-html="$t('page_404_tips', [count])"></p>
            <img src="@/assets/imgs/404.png" alt="404 Not Found!" />
        </div>
    </article>
</template>

<script>
export default {
    name: 'Agent404',
    data() {
        return {
            count: 5,
        };
    },
    mounted() {
        setTimeout(this.handler, 1000);
    },
    methods: {
        handler() {
            if (this.count === 0) {
                this.$router.push({ name: 'agent_index' });
                return;
            }
            this.count = this.count - 1;
            setTimeout(this.handler, 1000);
        },
    },
};
</script>

<style lang="scss">
.agent_404 {
    .err_message {
        margin-top: 116px;
        margin-bottom: 120px;
        text-align: center;

        h1 {
            font-size: 38px;
        }
        img {
            width: 660px;
            height: 180px;
        }
        a {
            color: var(--primary-color);
            text-decoration: underline;
        }
        .count {
            color: var(--primary-color);
            font-weight: bold;
        }
    }
    .message_404 {
        margin-bottom: 70px;
        line-height: 1.5em;
        font-size: 18px;
    }
}
</style>
