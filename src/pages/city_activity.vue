<template lang="html">
    <div class="city_activity">
        <div class="title">{{ city_name }}</div>
        <div class="main">
            <div class="search_area" ref="side_search">
                <div
                    :class="{
                        selected: search_type === 'search_all',
                        browse_all: true,
                    }"
                    @click="loadDataProxy('search_all')"
                >
                    {{ $t('city.category.all_activity') }}
                </div>
                <div class="menu_wrapper">
                    <side-menu
                        ref="side_menu"
                        :category-list="categoryList"
                        @on-search-by-category="searchByCategory"
                        :city_id="city_id"
                    ></side-menu>
                </div>
                <klk-input
                    class="search_input"
                    v-model="search_model"
                    :placeholder="$t('city.search.place_holder')"
                    @enter="loadDataProxy('search_mode')"
                >
                    <span slot="prepend" @click="loadDataProxy('search_mode')">
                        <klk-icon
                            type="icon_edit_search"
                            class="search_icon"
                        ></klk-icon>
                    </span>
                </klk-input>
            </div>
            <div
                class="activity_list"
                ref="activity_list"
                v-loading="loading_activity_list"
            >
                <div
                    class="activity_card_wrapper"
                    v-show="activity_list.length"
                    :key="index"
                    v-for="(activity, index) in activity_list"
                >
                    <activity-card
                        :class="{ disabled_card: activity.is_blocked }"
                        :activity="activity"
                        :key="activity.id"
                        :show-collect-icon="true"
                        @clickWish="clickWish(activity.is_blocked)"
                    ></activity-card>
                </div>
                <klk-no-data
                    v-show="!activity_list.length && !loading_activity_list"
                ></klk-no-data>
                <div class="klk-pagination-wrapper">
                    <klk-pagination
                        :total="pagination.total"
                        :page-size="pagination.pageSize"
                        :current.sync="pagination.curPage"
                        @change="handleCurrentChange"
                    ></klk-pagination>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import urlObj from '@/common/url';
import throttle from 'lodash/throttle';
import { processActivityInfo, expansionFn } from '@/common/util';
import SideMenu from './tpl/side_menu.vue';
import ActivityCard from './tpl/ActivityCard.vue';
/**
 * search_model : search_all  search_mode search_by_category
 */

export default {
    name: 'CityActivity',
    data() {
        return {
            search_type: 'search_all',
            frontend_id: '',
            fromSearchBar: false,
            activity_list: [],
            pagination: {
                total: 0,
                pageSize: 24,
                curPage: 1,
            },
            search: {},
            loading_activity_list: false,
            search_model: '',
            city_id: -1,
            city_name: '',
            categoryList: [],
        };
    },
    watch: {
        $route: function() {
            this.getIdAndNameFromUrl();
            this.loadData('search_all');
        },
    },
    created() {
        this.currencySymbol = '';
        this.getIdAndNameFromUrl();
        this.setSearchData();
    },
    mounted() {
        // this.loadData('search_all', 'onPage');
        this.fromSearchBar = !!this.$route.params.from;
        this.loadData('search_all');
    },
    methods: {
        initEvent() {
            let $side_search = this.$refs['side_search'],
                $activity_list = this.$refs['activity_list'];
            // getBoundingClientRect
            function adjustSideMenu() {
                let top = $activity_list.getBoundingClientRect().top;
                if (top <= 0) {
                    $side_search.style.position = 'absolute';
                    $side_search.style.top = '-80px';
                } else {
                    $side_search.style.position = 'fixed';
                    $side_search.style.top = '210px';
                }
            }
            window.addEventListener('scroll', throttle(adjustSideMenu, 300));
        },
        setCityName(cities) {
            this.city_name = (
                cities.find(item => item.id == this.city_id) || {}
            ).name;
            this.$root.$emit('setCityHolderName', this.city_name);
        },
        getIdAndNameFromUrl() {
            //主要是做兼容URL处理  /city/2-hong-kong => /city/2  现在老页面还存在第一种url
            let id_name = ('' + this.$route.params.city_id).match(
                /([^-]*)-?(.+)?/,
            );
            this.city_id = id_name[1];
        },
        setSearchData(extraData) {
            if (this.search_type === 'search_mode') {
                extraData = {
                    version: 160113,
                };
            }
            this.search = Object.assign(
                {
                    city_ids: this.city_id,
                    page_size: this.pagination.pageSize,
                    page_num: this.pagination.curPage,
                    frontend_ids: this.frontend_id,
                    query: this.search_model,
                    // sort:'most_relevant'
                },
                extraData || {},
            );
        },
        handleCurrentChange(curPage) {
            this.pagination.curPage = curPage;
            this.loadData(this.search_type);
        },

        loadDataProxy(search_type) {
            this.pagination.curPage = 1;
            if (search_type === 'search_mode') {
                if (!this.search_model) {
                    this.search_type = search_type = 'search_all';
                }
            }
            this.loadData(search_type);
        },
        loadData(search_type) {
            this.search_type = search_type;
            if (this.search_type !== 'search_by_category') {
                //去掉side menu的高亮
                this.$refs['side_menu'].resetData();
                this.frontend_id = '';
            }
            this.loading_activity_list = true;
            this.setSearchData();

            klook.ajaxGet(urlObj.search.search_by_city, this.search, resp => {
                this.loading_activity_list = false;
                if (resp && resp.success && resp.result) {
                    const { frontend_tree, destinations } =
                        resp.result.aggr_condition || {};
                    const search_result = resp.result.search_result || {};
                    this.categoryList = frontend_tree;
                    this.activity_list = processActivityInfo.call(
                        this,
                        search_result.activity_info,
                    );
                    this.pagination.total = search_result.total || 0;
                    this.setCityName(expansionFn(destinations));
                } else {
                    this.activity_list = [];
                    this.total = 0;
                }
            });
        },
        searchByCategory(id) {
            this.frontend_id = id;
            this.search_type = 'search_by_category';
            this.loadDataProxy(this.search_type);
        },
        clickWish(is_blocked) {
            if (is_blocked) {
                return;
            }
        },
    },
    components: {
        SideMenu,
        ActivityCard,
    },
};
</script>

<style lang="scss">
$border_radius: 2px;

.city_activity {
    .klk-pagination-wrapper {
        clear: both;
        float: right;
    }
    div,
    ul,
    li {
        box-sizing: border-box;
    }
    background: #fff;
    width: 100%;

    text-align: center;
    .title {
        width: 100%;
        height: 150px;
        line-height: 150px;
        font-size: 30px;
        font-weight: 500;
        text-align: center;
        color: #424242;
    }
    .main {
        width: 1000px;
        position: relative;
        display: table;
        margin: 0 auto;
        min-height: 800px;
        display: flex;
        .search_area {
            width: 310px;
            cursor: pointer;
            display: table-cell;
            text-align: left;
            z-index: 2;
            transition: all 0.5s ease;
            .browse_all {
                width: 280px;
                height: 46px;
                line-height: 46px;
                background: #fff;
                margin-bottom: 24px;
                border: solid 1px #e0e0e0;
                display: inline-block;
                font-size: 14px;
                text-align: left;
                padding-left: 14px;
                border-radius: $border_radius;
                &.selected {
                    color: var(--primary-color);
                }
                &:hover {
                    border-color: var(--primary-color);
                }
            }
            .search_input {
                width: 280px;
                margin-top: 20px;
                .search_icon {
                    margin-left: 12px;
                }
            }
        }
        .activity_list {
            width: 690px;
            display: table-cell;
            padding-bottom: 30px;
            .activity_card_wrapper {
                margin: 0 0 20px 20px;
                width: 320px;
                text-align: left;
                float: left;
            }
        }
    }
    .disabled_card {
        .card-content {
            color: grey;
            cursor: not-allowed;
            .card-content-title,
            .sell-price,
            .klk-icon-fast {
                color: grey;
            }
        }
        .image-wrapper {
            cursor: not-allowed;
        }
    }
}
</style>
