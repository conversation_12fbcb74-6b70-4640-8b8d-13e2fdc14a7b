<template lang="html">
    <div class="voucher_info">
        <div class="header">
            <div class="title">{{ $t('voucher_details') }}</div>
            <div class="desc">
                <span>{{ $t('voucher_info_desc') }}</span>
                <a
                    v-show="is_master_agent"
                    class="text_clickable"
                    target="_blank"
                    :href="voucher_logo_url"
                >
                    {{ $t('click_view_voucher_email_template') }}
                </a>
            </div>
        </div>
        <div class="logo_upload">
            <div v-show="is_master_agent" class="logo_tip">
                {{ $t('upload_company_logo') }}({{ $t('optional') }})
            </div>
            <div v-show="logoImgUrl" class="company_logo">
                <img :src="logoImgUrl" alt="" />
            </div>
            <div
                v-show="is_master_agent"
                class="upload_area"
                @click="uploadFile"
            >
                {{ $t('select_file') }}
            </div>
            <input type="hidden" class="logo_img_url" v-model="logoImgUrl" />
            <div v-show="is_master_agent" class="upload_rules">
                <p>{{ $t('upload_company_logo_tip1') }}</p>
                <p>{{ $t('upload_company_logo_tip2') }}</p>
            </div>
        </div>

        <klk-form inline class="voucher-form" v-loading="loading">
            <klk-form-item
                :label="getOptionalTitle($t('contact_number'))"
                class="width-250"
            >
                <klk-input :diabled="!is_master_agent" v-model="contactNumber">
                </klk-input>
            </klk-form-item>
            <klk-form-item
                :label="getOptionalTitle($t('email_address'))"
                class="width-250"
            >
                <klk-input :diabled="!is_master_agent" v-model="emailAddress">
                </klk-input>
            </klk-form-item>
            <klk-form-item
                :label="getOptionalTitle($t('company_name'))"
                class="width-520"
            >
                <klk-input :diabled="!is_master_agent" v-model="companyName">
                </klk-input>
            </klk-form-item>
            <klk-form-item
                :label="getOptionalTitle($t('company_address'))"
                class="width-520"
            >
                <klk-input :diabled="!is_master_agent" v-model="companyAddress">
                </klk-input>
            </klk-form-item>
            <br />
            <klk-form-item>
                <klk-button v-show="is_master_agent" @click="submitVoucherInfo">
                    {{ $t('save') }}
                </klk-button>
            </klk-form-item>
        </klk-form>
    </div>
</template>

<script>
import urlObj from '@/common/url';
import fileInput from '@/common/fileInput';

const img_lang_map = {
    'zh-CN':
        'https://res.klook.com/image/upload/v1590033285/klook-agent-web/voucher_zh-CN.png',
    'zh-TW':
        'https://res.klook.com/image/upload/v1590033821/klook-agent-web/voucher_zh-TW.png',
    en:
        'https://res.klook.com/image/upload/v1590033620/klook-agent-web/voucher_en.jpg',
    ko:
        'https://res.klook.com/image/upload/v1590033284/klook-agent-web/voucher_ko.jpg',
};

export default {
    name: 'VoucherInfo',
    data() {
        return {
            loading: false,
            is_master_agent: !window.KLK_USER_INFO.agent_type, // 0 master 1 SubAccount
            logoImgUrl: '',
            contactNumber: '',
            emailAddress: '',
            companyName: '',
            companyAddress: '',
            voucher_logo_url: img_lang_map[window.KLK_LANG],
        };
    },
    mounted() {
        this.loadVoucherInfoDetail();
    },
    methods: {
        getOptionalTitle(title) {
            return `${title}(${this.$t('optional')})`;
        },
        uploadFile() {
            if (!window.FormData) {
                return;
            }

            fileInput.onchange(uploadFile.bind(this)).click();

            function uploadFile(e) {
                if (!e.target.files[0]) {
                    return;
                }
                const formData = new FormData();
                formData.append('uploadFile', e.target.files[0]);

                klook.globalLoading.show('loading...');
                return klook.ajaxPostJSON(
                    urlObj.upload_logo,
                    formData,
                    response => {
                        klook.globalLoading.hide();
                        if (response.success) {
                            if (response.success && response.result) {
                                this.logoImgUrl = response.result.logo_url;
                                this.showDialogSuccess(
                                    this.$t('upload_success'),
                                );
                            } else {
                                this.showDialogFail(
                                    (response.error &&
                                        response.error.message) ||
                                        'Something was wrong!',
                                );
                            }
                        } else {
                            klook.globalLoading.hide();
                            this.showDialogFail(
                                (e && e.message) || 'Something was wrong!',
                            );
                        }
                    },
                );
            }
        },
        showDialogSuccess(tip) {
            this.$message({
                type: 'success',
                message: tip || 'success',
            });
        },
        showDialogFail(tip) {
            this.$message({
                type: 'error',
                message: tip || 'error',
            });
        },
        submitVoucherInfo() {
            this.loading = true;
            klook.ajaxPostJSON(
                urlObj.agent_account_voucher_detail,
                {
                    contactNumber: this.contactNumber,
                    emailAddress: this.emailAddress,
                    companyName: this.companyName,
                    companyAddress: this.companyAddress,
                    voucherLogo: this.logoImgUrl,
                },
                resp => {
                    this.loading = false;
                    if (resp.success) {
                        this.showDialogSuccess(this.$t('submit_success'));
                    } else {
                        this.showDialogFail(resp.error && resp.error.message);
                    }
                },
            );
        },
        loadVoucherInfoDetail() {
            klook.ajaxGet(urlObj.agent_account_voucher_detail, resp => {
                let result;
                if (resp.success) {
                    result = resp.result || {};
                    this.contactNumber = result.contactNumber;
                    this.emailAddress = result.emailAddress;
                    this.companyName = result.companyName;
                    this.companyAddress = result.companyAddress;
                    this.logoImgUrl = result.voucherLogo;
                }
            });
        },
    },
};
</script>
<style lang="scss">
.voucher_info {
    .header {
        width: 100%;
        border-bottom: solid 1px #e0e0e0;

        .title {
            height: 33px;
            font-size: 24px;
            color: #333333;
        }

        .desc {
            padding-bottom: 5px;
            font-size: 14px;
            color: #757575;
        }
    }

    .logo_upload {
        height: auto;
        padding: 30px 0;
        border-bottom: solid 1px #e0e0e0;

        .logo_tip {
            height: 22px;
            font-size: 16px;
            color: #333333;
        }

        .company_logo,
        .upload_area {
            width: 160px;
            height: 40px;
            border-radius: 2px;
            margin: 16px 40px 24px 0;
            display: inline-block;
            vertical-align: top;
        }

        .upload_area {
            background-color: #fafafa;
            border: dashed 1px var(--primary-color);
            color: var(--primary-color);
            text-align: center;
            line-height: 40px;
            cursor: pointer;
        }

        .upload_rules {
            height: 48px;
            font-size: 14px;
            line-height: 1.71;
            color: #888888;
        }
    }

    .voucher-form {
        padding: 32px 0;
    }
}
</style>
