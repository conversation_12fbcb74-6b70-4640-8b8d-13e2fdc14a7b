import markdown from 'marked';
import { format, isDate, isBefore, startOfDay } from 'date-fns';

const imageUrl = (width = 1920, height = 720, hasWater = false) =>
    `https://res.klook.com/images/fl_lossy.progressive,q_65/c_fill,w_${width},h_${height}${
        hasWater ? '/w_80,x_15,y_15,g_south_west,l_klook_water' : ''
    }/activities/`;

/**
 * 返回地区格式的日期或时间（type = 1), 时间format暂时没有写在多语言文档中，先在这里处理下
 * @param {*} date
 * @param {*} type
 */
const dateFormatStr = {
    en: 'D MMM YYYY',
    'zh-CN': 'YYYY年M月D日',
    'zh-TW': 'YYYY年M月D日',
};

function standardFormatDate(date, type) {
    if (!type) {
        return format(date, dateFormatStr[window.KLK_LANG] || 'YYYY-MM-DD');
    }
    let strTimeFormat = 'h:mma';
    switch (window.KLK_LANG) {
        case 'zh-CN':
        case 'zh-TW':
        case 'zh-HK':
            strTimeFormat = 'h:mm A';
            break;
        case 'ko':
            strTimeFormat = 'A h:mm';
            break;
        case 'th':
        case 'vi':
        case 'id':
            strTimeFormat = 'HH:mm';
            break;
    }
    return format(date, strTimeFormat);
}

const markdownArrToHtml = renderArr => {
    const handleLink = htmlStrArr => {
        return htmlStrArr.map(str =>
            str.replace(
                /href\s*=\s*(['"])(https?:\/\/)?www\.klook\.com.*?\1/gi,
                'href="javascript:void(0);"',
            ),
        );
    };
    const strformat = (...strings) => {
        const format = /{([\d\w.]+)}/g;
        let args = [...strings];
        let v;
        const str = args.shift() + '';
        if (args.length == 1 && typeof args[0] == 'object') {
            args = args[0];
        }
        format.lastIndex = 0;
        return str.replace(format, function(m, n) {
            v = args[n];
            return v === undefined ? m : v;
        });
    };
    const htmlTag = (tag, content, className) => {
        if (className) {
            return strformat(
                '<{0} class="{1}">{2}</{0}>',
                tag,
                className,
                content,
                tag,
            );
        } else {
            return strformat('<{0}>{1}</{2}>', tag, content, tag);
        }
    };
    const parseParagraph = renderItem => {
        if (renderItem.type == 'paragraph') {
            const strongMatch = /^<strong>(.+)<\/strong>$/.exec(
                renderItem.content,
            );
            const emMatch = /^<em>(.+)<\/em>$/.exec(renderItem.content);
            if (strongMatch) {
                return {
                    type: 'title',
                    content: strongMatch[1].replace(/<\/?strong>/g, ''),
                };
            } else if (emMatch) {
                return {
                    type: 'sub_title',
                    content: emMatch[1].replace(/<\/?em>/g, ''),
                };
            }
        }
        return renderItem;
    };
    const renderObj2Markdown = renderArr => {
        if (!Array.isArray(renderArr)) return '';
        const htmlArr = [];
        (renderArr || []).forEach((renderItem, i) => {
            switch (renderItem.type) {
                case 'title':
                    htmlArr.push(
                        htmlTag(
                            'h4',
                            renderItem.content,
                            renderItem.props && renderItem.props.has_style
                                ? 'has_style'
                                : undefined,
                        ),
                    );
                    break;
                case 'sub_title':
                    htmlArr.push(htmlTag('h5', renderItem.content));
                    break;
                case 'section_title':
                    htmlArr.push(htmlTag('h6', renderItem.content));
                    break;
                case 'item':
                    if (renderItem.props && renderItem.props.is_first) {
                        htmlArr.push('<ul>');
                    }
                    htmlArr.push(htmlTag('li', renderItem.content));
                    if (renderItem.props && renderItem.props.is_last) {
                        htmlArr.push('</ul>');
                    }
                    break;
                case 'order_item':
                    if (renderItem.props && renderItem.props.is_first) {
                        htmlArr.push('<ol>');
                    }
                    htmlArr.push(htmlTag('li', renderItem.content));
                    if (renderItem.props && renderItem.props.is_last) {
                        htmlArr.push('</ol>');
                    }
                    break;
                case 'paragraph':
                    if (
                        renderArr[i + 1] &&
                        parseParagraph(renderArr[i + 1]).type == 'paragraph'
                    ) {
                        htmlArr.push(
                            htmlTag('p', renderItem.content, 'p_followed'),
                        );
                    } else {
                        htmlArr.push(htmlTag('p', renderItem.content));
                    }
                    break;
                case 'image': {
                    htmlArr.push(`<img src="${renderItem.props.src}" `);
                    if (renderItem.props) {
                        renderItem.props.alt &&
                            htmlArr.push(`alt="${renderItem.props.alt}" `);
                        renderItem.props.title &&
                            htmlArr.push(` title="${renderItem.props.title}" `);
                    }
                    htmlArr.push(' >');
                }
            }
        });
        return handleLink(htmlArr).join('');
    };
    return renderObj2Markdown(renderArr);
};

const UtilMixin = {
    methods: {
        markdownToHtml: function(str) {
            if (typeof str !== 'string') {
                return '';
            }
            const strHtml = str ? markdown(str) : '';
            return strHtml.replace(
                /href\s*=\s*(['"])(https?:\/\/)?www\.klook\.com.*?\1/gi,
                'href="javascript:void(0);"',
            );
        },
        getImageUrl: function(imgUrl, width = 1295, height = 720, hasWater) {
            if (!imgUrl) {
                return '';
            }
            const imgReg = /(.*)(\.jpg|\.jpeg|\.png)$/i;
            const matches = imgReg.exec(imgUrl);
            const imageName = matches
                ? matches[1]
                : String(imgUrl).split('.')[0];
            return imageName
                ? imageUrl(width, height, hasWater) +
                      escape(imageName) +
                      '/' +
                      escape(this.activityInfo.title || arguments[1])
                : '';
        },
        markdownArrToHtml,
    },
    filters: {
        formatPrice: function(price) {
            return klook.formatPriceThousands(price);
        },
    },
};
/**
 * 根据shedules数据判断当前日期是否可用
 * schedules = {
 *      2017-12-27:{6034: 9902, 6035: 9988},
 *      2017-12-28:{6034: 9908, 6035: 9996}
 *      ...
 * }
 */
function isDisabledDate(date, schedules) {
    if (!isDate(date) || !schedules) {
        return;
    }

    let dateStr = format(date, 'YYYY-MM-DD');
    let currentSchedule = schedules[dateStr];

    if (currentSchedule) {
        let stocks = Object.keys(currentSchedule).reduce(function(
            preValue,
            curValue,
        ) {
            return preValue + currentSchedule[curValue];
        },
        0);
        return stocks <= 0;
    }
    return true;
}

/**
 * schedules = {
 *      2017-12-27:{6034: 9902, 6035: 9988},
 *      2017-12-28:{6034: 9908, 6035: 9996}
 *      ...
 * }
 */
function getEarliestAvailableDate(package_id, schedules) {
    if (!schedules || !package_id) {
        return '';
    }
    let firstDate = '';
    Object.keys(schedules).every(function(item) {
        if (schedules[item][package_id]) {
            firstDate = item;
        }
        return !firstDate;
    });
    return firstDate;
}

function formatDate(date, formatStr) {
    return format(date, formatStr);
}

function isBeforeNow(date) {
    return isBefore(date, new Date());
}

function getTimesFromSchedules(date, schedules) {
    if (!date || !schedules) {
        return [];
    }
    const dateKey = startOfDay(date).valueOf();
    return schedules[dateKey] || [];
}

function getPriceFromSchedules(date, schedules) {
    if (!date || !schedules) {
        return '';
    }
    const dateKey = startOfDay(date).valueOf();
    // 有库存
    const schedule = (schedules[dateKey] || []).filter(item => item.stock > 0);
    if (schedule.length === 0) return '';
    schedule.sort((a, b) => a.selling_price - b.selling_price);
    // 日历上显示有库存的timeslot的最低价
    return (schedule[0] || {}).selling_price;
}

/**
 * 处理package schedules : 这里对package schedules的数据结构进行转换[] => map
 * schedules = [{date: "2017-12-27 00:00:00", package_id: 6034, arrangement_id: 2961112, stock: 9902}]
 */
function handlePackageSchedules(schedules) {
    let result = {};
    if (Array.isArray(schedules)) {
        schedules.forEach(function(item) {
            let dateKey = startOfDay(item.date).valueOf();
            if (!result[dateKey]) {
                result[dateKey] = [item];
            } else {
                result[dateKey].push(item);
            }
        });
    }
    return result;
}

function escape(str) {
    return str && String(str).replace(/[.'"/\\&%]/g, '');
}

function isVisible(el) {
    return (
        isElement(el) &&
        document.body.contains(el) &&
        el.getBoundingClientRect().width > 0 &&
        el.getBoundingClientRect().height > 0
    );
}

function isElement(el) {
    return el && el.nodeType === 1;
}

// Return the Bounding Client Rec of an element. Retruns null if not an element
function getBCR(el) {
    return isElement(el) ? el.getBoundingClientRect() : null;
}

// Return an element's offset wrt document element
// https://j11y.io/jquery/#v=git&fn=jQuery.fn.offset
function offset(el) {
    if (isElement(el)) {
        if (!el.getClientRects().length) {
            return { top: 0, left: 0 };
        }
        let bcr = getBCR(el);
        let win = el.ownerDocument.defaultView;
        return {
            top: bcr.top + win.pageYOffset,
            left: bcr.left + win.pageXOffset,
        };
    }
}

function addClass(el, name) {
    if (!isElement(el)) {
        return;
    }
    return el.classList && el.classList.add(name);
}

function removeClass(el, name) {
    if (!isElement(el)) {
        return;
    }
    return el.classList && el.classList.remove(name);
}

const scrollspy = {
    init: function() {
        this.listen();
        return this;
    },
    refresh: function() {
        let menuContainer = document.getElementById('topmenu');
        if (!menuContainer) {
            return;
        }
        this.$targets = [];
        this.$offsets = [];
        this.$scrollHeight = this.getScrollHeight();
        let childs = menuContainer.childNodes;
        for (let i = 0; i < childs.length; i++) {
            if (childs[i].nodeType !== 1) {
                continue;
            }
            let child = childs[i];
            let link = childs[i].getAttribute('data-nav');
            let el = document.getElementsByClassName(link)[0];
            if (el && isVisible(el)) {
                this.$targets.push(child);
                this.$offsets.push(offset(el).top - 160);
                child.onclick = (function(offset) {
                    return function(event) {
                        if (offset > 1) {
                            window.scrollTo({
                                top: offset + 50,
                                behavior: 'smooth',
                            });
                            event.preventDefault();
                            return false;
                        }
                    };
                })(this.$offsets[this.$targets.length - 1]);
            }
        }
    },
    process: function() {
        let scrollTop = window.pageYOffset;
        let scrollHeight = this.getScrollHeight();
        let maxScroll = scrollHeight - window.innerHeight;

        if (this.$scrollHeight !== scrollHeight || !this.$offsets) {
            this.refresh();
        }

        // 处理右侧booking区域的显示与隐藏
        let mealEl = document.getElementById('meal'),
            bookingEl = document.getElementById('intro-booking'),
            overviewEl = document.getElementById('overview'),
            howtouse = document.getElementById('use');
        let menuContainer = document.getElementById('topmenu');

        if (!mealEl || !bookingEl || !overviewEl || !howtouse) {
            return;
        }

        // var mealTop = offset(mealEl).top;
        // var overviewTop = offset(overviewEl).top;

        if (scrollTop <= 500) {
            // removeClass(bookingEl, 'is-fixed');
            // bookingEl.style.left = '800px';
            if (menuContainer) {
                menuContainer.style.display = 'none';
            }
        } else {
            // addClass(bookingEl, 'is-fixed');
            if (menuContainer) {
                menuContainer.style.display = 'block';
            }
            // bookingEl.style.left = (window.innerWidth - 1160) / 2 + 800 + 'px';
            // if (scrollTop < Math.max(mealTop - 250, 501) ||
            //     (scrollTop > overviewTop)) {
            //     bookingEl.style.display = 'block';
            // } else {
            //     bookingEl.style.display = 'none';
            // }
        }

        if (scrollTop >= maxScroll) {
            this.activate(this.$targets[this.$targets.length - 1]);
            return;
        }

        if (scrollTop < this.$offsets[0] && this.$offsets[0] > 0) {
            this.clear();
            return;
        }

        for (let i = this.$offsets.length; i >= 0; i--) {
            let isActiveTarget =
                this.$activeTarget !== this.$targets[i] &&
                scrollTop >= this.$offsets[i] &&
                (typeof this.$offsets[i + 1] === 'undefined' ||
                    scrollTop < this.$offsets[i + 1]);

            if (isActiveTarget) {
                this.activate(this.$targets[i]);
            }
        }
    },
    getScrollHeight: function() {
        return Math.max(
            document.body.scrollHeight,
            document.documentElement.scrollHeight,
        );
    },
    clear: function() {
        for (let i = 0; i < this.$targets.length; i++) {
            removeClass(this.$targets[i], 'active');
        }
    },
    activate: function(el) {
        this.clear();
        addClass(el, 'active');
    },
    listen: function() {
        window.addEventListener('scroll', this);
        window.addEventListener('resize', this);
        window.addEventListener('orientationchange', this);
        this.handleEvent('refresh');
    },
    unlisten: function() {
        window.removeEventListener('scroll', this);
        window.removeEventListener('resize', this);
        window.removeEventListener('orientationchange', this);
    },
    dispose: function() {
        this.unlisten();
        clearTimeout(this.$timer);
        this.$targets = null;
        this.$offsets = null;
    },
    handleEvent: function(evt) {
        let type = typeof evt === 'string' ? evt : evt.type;
        let that = this;
        let resizeThrottle = function() {
            if (!that.$timer) {
                that.$timer = setTimeout(function() {
                    that.refresh();
                    that.process();
                    that.$timer = null;
                }, 75);
            }
        };
        if (type === 'scroll') {
            this.process();
        } else if (/(resize|orientationchange|mutation|refresh)/.test(type)) {
            resizeThrottle();
        }
    },
};

/**
 * Is promotion :
 * market_price < original_sell_price  return original_sell_price
 * market_price >= original_sell_price return market_price
 * Is not promotion:
 * return market_price
 */
const countOriginalSellPrice = function({
    market_price,
    original_sell_price,
    is_promotion,
}) {
    if (is_promotion && +original_sell_price > +market_price) {
        return original_sell_price;
    } else {
        return market_price;
    }
};

const formatLongImagesObj = (img, width, height) => {
    width = width || img.width;
    height = height || img.height;
    return {
        image_desc: img.image_desc,
        width,
        height,
        image_url: `${img.prefix}${img.pre_process}${
            img.has_water_mark
                ? 'w_80,x_15,y_15,g_south_west,l_klook_water/'
                : ''
        }${img.suffix}`,
    };
};

export {
    offset,
    standardFormatDate,
    UtilMixin,
    isDisabledDate,
    getEarliestAvailableDate,
    formatDate,
    isBeforeNow,
    getTimesFromSchedules,
    getPriceFromSchedules,
    handlePackageSchedules,
    isVisible,
    scrollspy,
    countOriginalSellPrice,
    imageUrl,
    escape,
    getBCR,
    markdownArrToHtml,
    formatLongImagesObj,
};
