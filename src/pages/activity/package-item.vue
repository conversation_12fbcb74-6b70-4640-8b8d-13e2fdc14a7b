<template lang="html">
    <li
        ref="ref-package-item"
        :class="[
            'package-list-item',
            {
                'package-list-item_opened': isActive,
                'package-sold-out': !packageInfo.stock,
            },
        ]"
    >
        <!-- default slot for package detail -->
        <slot></slot>
        <div class="package-list-item_header">
            <div
                class="package-list-item_left"
                :class="{ is_promotion: packageInfo.is_promotion }"
            >
                <header>
                    <b>{{ packageInfo.package_name }}</b>
                </header>
                <div class="price">
                    <div class="price_sell">
                        {{ currencySymbol }}&nbsp;{{
                            packageInfo.sell_price | formatPrice
                        }}
                    </div>
                    <div
                        class="price_market"
                        v-show="
                            +packageInfo.market_price > +packageInfo.sell_price
                        "
                    >
                        {{ packageInfo.market_price | formatPrice }}
                    </div>
                    <div
                        v-if="packageInfo.is_promotion"
                        class="flash-sale-icon"
                    >
                        {{ $t('flash_sale') }}
                    </div>
                </div>
            </div>
            <div class="package-list-item_right">
                <cut-off-time
                    v-if="getDay && !isShowTimepicker && !selectedDate"
                    :get-day="getDay"
                ></cut-off-time>
                <span
                    :class="[
                        'booking',
                        {
                            booking_selected: isActive,
                            unavailable: !packageInfo.has_stocks,
                        },
                    ]"
                    @click="toggleActive"
                    v-show="!onlyOnePackage"
                >
                    {{ chooseBtnText }}
                </span>
            </div>
        </div>

        <transition name="fade">
            <div class="package-list-item_detail" v-show="isActive">
                <div class="select-datetime">
                    <div class="select-date">
                        <label>{{ $t('activity.label.select_date') }}</label>
                        <div class="datetime-button">
                            <klk-single-date-picker
                                :tip="
                                    `${$t('price_currency')} ${currencySymbol}`
                                "
                                :lang="datepickerLang"
                                class="datetime"
                                :isDayBlocked="disabledDate"
                                :view-switchable="false"
                                :get-price="getPrice"
                                :placeholder="
                                    $t('activity.tips.please_select_date')
                                "
                                v-model="selectedDate"
                            >
                            </klk-single-date-picker>
                        </div>
                    </div>
                    <cut-off-time
                        v-if="getDay && !isShowTimepicker && selectedDate"
                        :get-day="getDay"
                    ></cut-off-time>
                    <div class="select-time" v-show="isShowTimepicker">
                        <label>{{ $t('activity.label.select_time') }}</label>
                        <time-picker
                            v-model="selectedTime"
                            :times="timesArrangement"
                        >
                        </time-picker>
                        <cut-off-time
                            v-if="getDay"
                            :get-day="getDay"
                        ></cut-off-time>
                    </div>
                </div>
                <div class="select-unit">
                    <label>{{ $t('activity.v2.label.select_quantity') }}</label>
                    <unit-picker
                        :units="units"
                        :inventories="inventories"
                        v-model="selectedUnits"
                        :min-pax="packageInfo.min_pax"
                        :max-pax="packageInfo.max_pax"
                        :currency-symbol="currencySymbol"
                    >
                    </unit-picker>
                </div>
                <package-booking-detail
                    :package_id="packageInfo.package_id"
                ></package-booking-detail>
            </div>
        </transition>

        <div
            class="package-list-item_tip"
            v-show="!packageInfo.stock && packageInfo.firstAvailableDate"
        >
            {{
                $t('activity.v2.earliest_available', [
                    standardFormatDate(packageInfo.firstAvailableDate),
                ])
            }}
        </div>
    </li>
</template>

<script>
import { mapState } from 'vuex';
import { differenceInDays } from 'date-fns';
import urlObj from '@/common/url';
import {
    UtilMixin,
    handlePackageSchedules,
    getTimesFromSchedules,
    getPriceFromSchedules,
    standardFormatDate,
    formatDate,
    offset,
    countOriginalSellPrice,
} from './util';
import PackageBookingDetail from './components/package-booking-detail.vue';
import TimePicker from './package-timepicker.vue';
import UnitPicker from './package-unit.vue';
import CutOffTime from './components/cut-off-time.vue';

export default {
    props: [
        'packageInfo',
        'name',
        'initialDate',
        'onlyOnePackage',
        'currencySymbol',
    ],
    data: function() {
        return {
            selectedDate: '',
            selectedTime: {},
            selectedUnits: [],
            showDescription: false,
            units: [],
            timesArrangement: [],
            inventories: {},
            block_out_time: '',
        };
    },
    components: {
        PackageBookingDetail,
        TimePicker,
        UnitPicker,
        CutOffTime,
    },
    mixins: [UtilMixin],
    computed: {
        ...mapState('activity', ['active_package_id']),
        isActive() {
            return this.active_package_id === this.packageInfo.package_id;
        },
        getDay() {
            let date = this.packageInfo.available_date || this.initialDate,
                block_out_time = this.block_out_time;
            if (Object.keys(this.selectedTime).length) {
                date = this.selectedTime.date;
                block_out_time = this.selectedTime.block_out_time;
            }
            if (!date || !block_out_time) {
                return;
            }
            let time = block_out_time.split(' ')[1];
            date = formatDate(date, 'YYYY-MM-DD');
            block_out_time = formatDate(block_out_time, 'YYYY-MM-DD');

            let day = differenceInDays(date, block_out_time);
            if (!day && time === '00:00:00') {
                time = '23:59:00';
                day = 1;
            }
            day = day
                ? this.$t('before_travel', [day])
                : this.$t('day_of_travel');
            this.$emit('get-time', [day, time]);
            return [day, time];
        },
        chooseBtnText() {
            if (!this.packageInfo.has_stocks) {
                return this.$t('activity.v2.btn.sold_out');
            }
            return this.packageInfo.stock
                ? this.isActive
                    ? this.$t('activity.v2.btn.select_package1')
                    : this.$t('activity.v2.btn.select_package')
                : this.$t('activity.v2.btn.select_package2');
        },
        datepickerLang() {
            return window.KLK_LANG || 'en';
        },
        isShowTimepicker() {
            return (
                this.timesArrangement[0] &&
                (this.timesArrangement.length > 1 ||
                    formatDate(this.timesArrangement[0].date, 'HH:mm') !==
                        '00:00')
            );
        },
    },
    created() {
        this.fetchSchedules();
        this.block_out_time = this.packageInfo.block_out_time || '';
    },
    watch: {
        onlyOnePackage: {
            immediate: true,
            handler: function(newVal) {
                if (newVal) {
                    this.$store.commit(
                        'activity/UPDATE_ACTIVE_PACKAGE_ID',
                        this.packageInfo.package_id,
                    );
                }
            },
        },
        selectedDate(newVal) {
            this.timesArrangement = getTimesFromSchedules(
                newVal,
                this.schedules,
            );
            if (this.timesArrangement.length >= 1) {
                this.selectedTime = this.timesArrangement[0] || {};
            } else {
                this.selectedTime = {};
            }
            this.$parent.fetchSubPackageInfo(newVal);
        },
        selectedTime(newVal, oldVal) {
            if (
                newVal &&
                newVal.arrangement_id &&
                newVal.arrangement_id !== oldVal.arrangement_id
            ) {
                this.units = [];
                this.selectedUnits = [];
                this.fetchUnits(newVal.arrangement_id);
            }
        },
        selectedUnits() {
            this.$store.commit('activity/UPDATE_PACKAGE_BOOKING_INFO', {
                package_name: this.packageInfo.package_name,
                package_id: this.packageInfo.package_id,
                selectedTime: this.selectedTime,
                selectedUnits: this.selectedUnits,
            });
        },
        initialDate(newVal) {
            if (
                Object.prototype.toString.call(newVal) === '[object Date]' &&
                this.packageInfo.stock
            ) {
                this.selectedDate = newVal;
            }
        },
    },
    methods: {
        standardFormatDate,
        toggleDesciption() {
            if (this.isActive) {
                return;
            }
            this.showDescription = !this.showDescription;
        },
        toggleActive() {
            if (!this.packageInfo.has_stocks) {
                return;
            }

            if (this.isActive) {
                this.$store.commit('activity/UPDATE_ACTIVE_PACKAGE_ID', -1);
            } else {
                this.$store.commit(
                    'activity/UPDATE_ACTIVE_PACKAGE_ID',
                    this.packageInfo.package_id,
                );
                this.$emit('get-time', this.getDay);
                setTimeout(() => {
                    window.scrollTo({
                        top:
                            offset(this.$refs['ref-package-item']).top -
                            60 -
                            50 -
                            10, // 110 为详情页中搜索header+详情pin住header的高度
                        behavior: 'smooth',
                    });
                }, 500);
            }
        },
        fetchSchedules() {
            klook.ajaxGet(
                urlObj.get_packages_schedules,
                {
                    package_ids: this.packageInfo.package_id,
                },
                res => {
                    if (res.success) {
                        this.schedules = handlePackageSchedules(
                            res.result &&
                                res.result[this.packageInfo.package_id],
                        );
                        this.timesArrangement = getTimesFromSchedules(
                            this.selectedDate,
                            this.schedules,
                        );
                    }
                },
            );
        },
        fetchUnits(arrangement_id) {
            klook.ajaxGet(
                urlObj.get_arrangements_by_arrangement_id(arrangement_id),
                {},
                res => {
                    if (res.success && res.result) {
                        const prices = res.result.prices || [];
                        this.inventories = res.result.inventories || {};
                        this.units = prices.map(unit => {
                            // format market_price if unit is promotion
                            if (unit.is_promotion) {
                                unit.market_price = countOriginalSellPrice(
                                    unit,
                                );
                            }
                            return unit;
                        });
                    }
                },
            );
        },
        disabledDate(date) {
            const _arrangements = getTimesFromSchedules(date, this.schedules);
            return !(
                _arrangements.length &&
                _arrangements.some(item => item.stock > 0)
            );
        },
        getPrice(date, disabled) {
            if (disabled) return '';
            return getPriceFromSchedules(date, this.schedules);
        },
    },
};
</script>

<style lang="scss">
.package-list-item {
    margin-bottom: 10px;
    position: relative;
    width: 760px;
    background-color: #f1f6fd;

    .flash-sale-icon {
        margin-left: 20px;
    }

    .package-list-item_header {
        display: flex;
        width: 100%;
        padding: 20px;
        position: relative;
        justify-content: space-between;

        .package-list-item_left {
            display: flex;
            flex-direction: column;
            max-width: 450px;

            header {
                font-size: 16px;
                line-height: 1.5;
                color: #424242;
                font-weight: 600;
                margin-bottom: 4px;

                b {
                    word-break: break-word;
                }
            }

            .subname {
                padding: 6px 0 10px;
            }

            .price {
                margin-right: 20px;
                display: flex;
                align-items: center;
                align-content: center;

                .price_sell {
                    font-size: 16px;
                    font-weight: 600;
                    color: #333;
                    margin-right: 8px;
                }

                .price_market {
                    display: flex;
                    align-items: center;
                    font-size: 14px;
                    color: #888;
                    text-decoration: line-through;
                }
            }
        }

        .package-list-item_right {
            position: relative;

            .flash-sale-icon {
                position: absolute;
                right: 120px;
                top: 10px;
            }

            display: flex;
            align-items: center;
            .booking {
                border-radius: 2px;
                border: solid 1px var(--primary-color);
                color: var(--primary-color);
                font-size: 14px;
                font-weight: 600;
                display: flex;
                justify-content: center;
                align-items: center;
                height: 40px;
                padding: 12px 16px;
                min-width: 85px;
                background: #fff;
                cursor: pointer;

                &:hover {
                    background: var(--primary-color);
                    color: #fff;
                }
            }

            .booking_selected {
                background: var(--primary-color);
                color: #fff;
            }
        }
    }

    .package-list-item_detail {
        padding: 20px;
        border-top: 1px solid #fff;

        label {
            font-size: 14px;
            line-height: 1.43;
            color: #999;
            display: block;
            margin-bottom: 8px;
        }

        .select-datetime {
            .cut-off-time {
                display: inline-block;
            }
            .select-date {
                display: inline-block;
                margin-right: 20px;

                .datetime-button {
                    display: block;
                    width: 300px;
                    height: 42px;

                    .icon {
                        margin-right: 10px;
                    }
                }
            }

            .select-time {
                display: inline-block;
                vertical-align: top;
            }
        }

        .select-unit {
            margin: 20px 0;
        }
    }

    /*   .description {
        padding: 0 20px 6px 20px;

        .description_button {
            margin-bottom: 10px;

            a {
                display: inline-block;
                margin-right: 10px;
                color: #999;

                &:hover, &:hover svg {
                    color: var(--primary-color);
                }
            }

            svg {
                font-size: 12px;
                vertical-align: middle;
            }
        }
    }
 */
    .package-list-item_tip {
        position: absolute;
        top: 65px;
        right: 20px;
        color: #ff5722;
        font-size: 12px;
    }
}

.package-list-item_opened {
    .description .description_button {
        a {
            color: #999;

            &:hover,
            &:hover svg {
                color: #999;
            }
        }
    }

    .description_content {
        color: #666;
    }
}

.package-sold-out {
    header,
    .subname {
        color: #d1d1d1 !important;
    }

    .unavailable {
        background: #d1d1d1 !important;
        color: #fff !important;
        border: none !important;

        &:hover {
            background: #d1d1d1 !important;
        }
    }
}
</style>
