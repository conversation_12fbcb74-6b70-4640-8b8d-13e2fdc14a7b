<template>
    <div class="msp-tip" v-show="visible" :style="style" ref="refTip">
        <p>{{ $t('suggest_low_price') }}</p>
        <div>
            <span v-for="(item, index) in mspInfo" :key="index">
                <span>{{ item.currency }}</span
                >{{ handleThousands(item.value) }}
            </span>
        </div>
    </div>
</template>

<script>
export default {
    props: {
        mspInfo: {
            type: Array,
            default: () => [],
        },
        visible: {
            type: Boolean,
        },
        propStyle: {
            type: Object,
            default: () => {},
        },
    },
    data() {
        return {
            curHeight: 0,
        };
    },
    computed: {
        style() {
            return {
                top:
                    (this.propStyle ? this.propStyle.top : 0) -
                    this.curHeight +
                    'px',
                left: (this.propStyle ? this.propStyle.left : 0) + 'px',
            };
        },
    },
    watch: {
        visible(val) {
            if (val) {
                this.$nextTick(() => {
                    this.curHeight = this.$refs.refTip.offsetHeight;
                });
            }
        },
    },
    methods: {
        handleThousands(value) {
            return klook.formatPriceThousands(value);
        },
    },
};
</script>

<style lang="scss">
.msp-tip {
    width: 360px;
    position: absolute;
    border: 1px solid #ccc;
    padding: 15px;
    z-index: 9999 !important;
    background-color: #fff;
    div {
        display: flex;
        flex-direction: column;
        padding-top: 10px;
        > span {
            margin-top: 5px;
            display: flex;
            padding: 0 10px;
            span {
                width: 42px;
            }
        }
    }
}
</style>
