<template>
    <div id="activity" class="activity-container">
        <div id="banner" class="banner" @contextmenu="disabledContextMenu">
            <div class="img-wrapper">
                <carousel
                    v-if="is_china_agent"
                    class="img-wrapper"
                    :key="carouselDataKey"
                >
                    <div
                        v-for="(item, index) in carouselData"
                        :key="index"
                        :class="{
                            'carousel-item': true,
                            'carousel-item-empty': !item.activity_id,
                        }"
                    >
                        <img :src="item.image_url" />
                    </div>
                </carousel>
                <img
                    v-else
                    :src="getImageUrl(activityInfo.banner_url)"
                    :title="activityInfo.banner_alt"
                />
            </div>
        </div>
        <div class="topmenu-container">
            <div class="topmenu" id="topmenu">
                <a
                    class="nav-item"
                    data-nav="content-activity-meal"
                    href="javascript:void(0)"
                    >{{ $t('activity.v2.label.package_options') }}</a
                >
                <a
                    data-nav="content-activity-overview"
                    class="nav-item"
                    href="javascript:void(0)"
                    >{{ $t('activity.v2.label.activity_desc') }}</a
                >
                <a
                    v-if="showBookingKnow"
                    data-nav="content-activity-booking-know"
                    class="nav-item"
                    href="javascript:void(0)"
                    >{{ $t('activity.v2.label.activity_info') }}</a
                >
                <a
                    data-nav="content-activity-how-use"
                    class="nav-item"
                    href="javascript:void(0)"
                >
                    {{ $t('activity.v2.label.how_to_use') }}</a
                >
            </div>
        </div>
        <section class="white-bg content">
            <div class="g-main">
                <intro-component
                    :activity-info="activityInfo"
                    class="g-left"
                ></intro-component>
            </div>
        </section>

        <section class="content-activity-meal" id="meal">
            <div class="g-main">
                <meal-component
                    class="g-left"
                    :activity-info="activityInfo"
                    :schedules="schedules"
                    :exit-sub-pkg="exitSubPkg"
                ></meal-component>
            </div>
        </section>

        <section class="white-bg">
            <div class="g-main">
                <div class="g-left">
                    <!--活动概览-->
                    <div class="content-activity-overview" id="overview">
                        <header>
                            {{ $t('activity.v2.label.activity_desc') }}
                        </header>
                        <div>{{ activityInfo.what_we_love }}</div>
                        <div
                            v-if="is_china_agent"
                            class="content-activity-image"
                        >
                            <div
                                class="content-activity-image-item"
                                v-for="(item, index) in imageData"
                                :key="index"
                            >
                                <div
                                    v-if="item.ifLongImage"
                                    class="content-activity-long-image-item"
                                >
                                    <img
                                        v-for="(img, index) in item.longIamges"
                                        class="activity-img"
                                        :src="img.image_url"
                                        :key="index"
                                    />
                                </div>
                                <img v-else :src="item.image_url" />
                                <div v-if="item.image_desc" class="desc">
                                    <span
                                        ><klk-icon
                                            type="icon_navigation_chevron_up"
                                            size="14"
                                        ></klk-icon>
                                    </span>
                                    {{ item.image_desc }}
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="content-activity-how-use" id="use">
                        <header>
                            {{ $t('activity.v2.label.how_to_use') }}
                        </header>
                        <div>
                            <div
                                class="how-use-item markdown-content"
                                v-html="markdownArrToHtml(howToUse)"
                            ></div>
                        </div>
                    </div>
                    <div class="content-activity-map">
                        <h5 class="location">{{ $t('location') }}</h5>
                        <div
                            class="content-activity-map-image"
                            id="activityMap"
                        >
                            <p class="activity-address">{{ address }}</p>
                            <img
                                class="image"
                                @click="goMap"
                                :src="activityInfo.map_box_image_url"
                                alt="map"
                            />
                        </div>
                    </div>
                    <template v-if="!activityInfo.use_section">
                        <div
                            class="content-activity-booking-know"
                            id="know"
                            v-if="showBookingKnow"
                        >
                            <header>
                                {{ $t('activity.v2.label.activity_info') }}
                            </header>
                            <div
                                class="booking-know_item markdown-content"
                                v-html="
                                    markdownToHtml(
                                        activityInfo.confirmation_details,
                                    )
                                "
                                v-if="activityInfo.confirmation_details"
                            ></div>
                            <div
                                class="booking-know_item markdown-content"
                                v-html="
                                    markdownToHtml(activityInfo.inclusive_of)
                                "
                                v-if="activityInfo.inclusive_of"
                            ></div>
                            <div
                                class="booking-know_item markdown-content"
                                v-html="
                                    markdownToHtml(
                                        activityInfo.not_inclusive_of,
                                    )
                                "
                                v-if="activityInfo.not_inclusive_of"
                            ></div>
                            <div
                                class="booking-know_item markdown-content"
                                v-html="
                                    markdownToHtml(activityInfo.package_options)
                                "
                                v-if="activityInfo.package_options"
                            ></div>
                            <div
                                class="booking-know_item markdown-content"
                                v-html="markdownToHtml(activityInfo.itinerary)"
                                v-if="activityInfo.itinerary"
                            ></div>
                            <div
                                class="booking-know_item markdown-content"
                                v-html="
                                    markdownToHtml(activityInfo.insider_tips)
                                "
                                v-if="activityInfo.insider_tips"
                            ></div>
                            <div
                                class="booking-konw_item markdown-content reminder"
                                v-html="
                                    markdownToHtml(
                                        activityInfo.additional_information,
                                    )
                                "
                                v-if="activityInfo.additional_information"
                            ></div>
                        </div>
                    </template>
                    <template v-else>
                        <section-render
                            :sections="activityInfo.section_content.sections"
                        ></section-render>
                    </template>
                </div>
            </div>
        </section>
        <image-preview
            ref="imagePreview"
            :imageListProp="usageImages"
        ></image-preview>
    </div>
</template>

<script>
import urlObj from '@/common/url';
import MealComponent from './meal.vue';
import IntroComponent from './intro.vue';
import ImagePreview from './image-preview.vue';
import { UtilMixin, scrollspy, formatLongImagesObj } from './util';
import Carousel from '../home/<USER>';
import { KlkSwiper, KlkSwiperSlide } from './components/slider/index';
import SectionRender from './components/section-render.vue';

export default {
    name: 'ActivityDetail',
    props: {
        activityInfo: {
            type: Object,
            required: true,
            default: () => {},
        },
    },
    mixins: [UtilMixin],
    data() {
        return {
            schedules: [],
            use_section_image: [],
            exitSubPkg: false,
            /**
             * https://orion.myklook.com/1000003/17/orion/features/1051897/detail
             * 主要解决活动详情预订须知不准确的问题，目前已经发现的问题id是['7840', '35854']
             */
            showBookingKnow: false,
        };
    },
    components: {
        MealComponent,
        SectionRender,
        IntroComponent,
        KlkSwiper,
        KlkSwiperSlide,
        ImagePreview,
        Carousel,
    },
    mounted() {
        this.scrollspy = scrollspy.init();
        this.fetchSchedules();
    },
    beforeDestroy() {
        this.scrollspy && this.scrollspy.dispose();
        this.scrollspy = null;
    },
    computed: {
        usageImages() {
            if (this.activityInfo && this.activityInfo.use_section) {
                return this.use_section_image;
            } else {
                const usageImages =
                    (this.activityInfo.image_item &&
                        this.activityInfo.image_item.usage_images) ||
                    [];

                return this.computedImageUrl(usageImages);
            }
        },
        howToUse() {
            return this.activityInfo.how_to_use_render_obj;
        },
        address() {
            return this.activityInfo.address_desc;
        },
        carouselData() {
            const images =
                (this.activityInfo.image_item &&
                    this.activityInfo.image_item.banner) ||
                [];

            return this.computedImageUrl(images, 1920, 720);
        },
        carouselDataKey() {
            return this.activityInfo.id;
        },
        imageData() {
            const images = this.activityInfo.images || [];

            return this.computedImageUrl(images).map((img, index) => {
                const currentLongImages = images[index].images;
                const ifLongImageItem =
                    currentLongImages && currentLongImages.length > 0;
                return {
                    ...img,
                    ifLongImage: ifLongImageItem,
                    longIamges:
                        ifLongImageItem &&
                        currentLongImages.map(img => formatLongImagesObj(img)),
                };
            });
        },
        is_china_agent() {
            return window.KLK_USER_INFO.agent_category === 'China';
        },
    },
    methods: {
        goMap() {
            const address = this.activityInfo.address.split(',');
            window.open(
                `https://maps.google.com/maps?daddr=${address[0]},${address[1]}&amp;ll=?&klookNewPage=true`,
            );
        },
        fetchDetail() {
            this.$emit('load-activity-info');
        },
        fetchSchedules() {
            klook.ajaxGet(
                urlObj.get_activity_schedules_url_by_activity_id(
                    this.activityInfo.id,
                ),
                { preview: false },
                res => {
                    if (res && res.success && res.result) {
                        this.schedules = res.result.schedules || [];
                        this.exitSubPkg = res.result.exist_sub_pkg;
                    }
                },
            );
        },
        disabledContextMenu(event) {
            event.preventDefault();
            return false;
        },
        sectionViewImage({ index, images }) {
            this.previewUsageImage(index, images);
        },
        previewUsageImage(index, images) {
            if (this.activityInfo.use_section) {
                this.use_section_image = images;
            }
            this.$refs.imagePreview.curIndex = index;
            this.$refs.imagePreview.visible = true;
        },
        computedImageUrl(images, width, height) {
            images.forEach(item => {
                item.width = width ? width : item.width;
                item.height = height ? height : item.height;
                item.image_url = this.getImageUrl(
                    item.image_url,
                    item.width,
                    item.height,
                    this.is_china_agent,
                );
            });
            return images;
        },
    },
};
</script>

<style lang="scss">
$minWidth: 1160px;
$bodyBgColor: #f5f5f5;
$mainColor: var(--primary-color);

.activity-container {
    background: #fff;
    min-width: 1160px;
    padding-bottom: 100px;

    * {
        box-sizing: border-box;
    }

    .white-bg {
        background: #fff;
    }

    .gray-bg {
        background: #f5f5f5;
    }

    .topmenu,
    .g-main {
        width: $minWidth;
        margin: 0 auto;
        position: relative;
    }

    .g-left {
        width: 760px;
        position: relative;
    }

    .left-arrow {
        display: inline-block;
        transform: rotate(-90deg) scale(0.6);

        i {
            color: #888;
            font-size: 10px;
        }
    }

    .banner {
        font-size: 0;
        height: 500px;
        background: #eee;

        .img-wrapper {
            height: 100%;
            width: 100%;

            img {
                display: inline-block;
                width: 100%;
                height: 100%;
                border: none;
            }
        }
    }

    .topmenu-container {
        position: fixed;
        top: 60px;
        background: #fff;
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.12);
        width: 100%;
        z-index: 9998;

        .topmenu {
            display: none;

            a {
                font-size: 16px;
                height: 50px;
                line-height: 50px;
                color: #666;
                display: inline-block;
                margin-right: 14px;
                padding: 0 20px;
            }

            .active {
                border-bottom: 2px solid var(--primary-color);
            }
        }
    }

    .content {
        padding: 30px 0 0;
    }

    .content-activity-meal {
        padding: 30px 0;
    }

    .content-activity-overview,
    .content-activity-booking-know,
    .content-activity-how-use,
    .content-activity-cancel {
        border-top: 1px solid #eee;
        line-height: 1.5;
        font-size: 14px;
        margin-bottom: 30px;

        header {
            font-size: 24px;
            line-height: 1.5;
            color: #424242;
            padding: 25px 0;
            font-weight: 600;
        }

        .reminder {
            padding: 20px;
            background: #fffae5;
            margin-top: 30px;

            ul {
                margin-bottom: 0;
            }
        }

        .booking-know_item {
            margin-bottom: 20px;
        }
    }

    .content-activity-how-use {
        border-top: 1px solid #e0e0e0;
    }

    .content-activity-map {
        border-top: 1px solid #e0e0e0;
        .location {
            margin: 16px 0 8px;
            font-weight: 600;
            color: #212121;
        }

        .content-activity-map-image {
            height: 316px;
            width: 100%;
            .activity-address {
                color: #757575;
                line-height: 19px;
            }
            img {
                cursor: pointer;
                margin-top: 10px;
                width: 100%;
                height: 100%;
            }
        }
    }

    .content-activity-cancel {
        border-top: none;
        margin-bottom: 0;
        padding-bottom: 30px;
    }

    .content-activity-overview {
        border-top: 1px solid #e0e0e0;

        header {
            font-weight: 600;
            padding-top: 40px;
            padding-bottom: 20px;
        }

        .content-activity-image {
            .content-activity-image-item {
                margin: 30px 0;

                img {
                    width: 790px;
                    // height: 439px;
                }

                .content-activity-long-image-item {
                    font-size: 0;
                    img {
                        margin: 0;
                        height: auto;
                    }
                }

                .desc {
                    display: flex;

                    span {
                        margin-right: 7px;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                    }
                }
            }
        }
    }

    .content-activity-use-image {
        padding-bottom: 30px;
        font-size: 0;

        ul {
            height: auto;
            overflow: hidden;
        }

        li {
            list-style: none;
            display: inline-block;
            margin-right: 20px;
            background: #f5f5f5;
            height: 100%;
            margin-bottom: 16px;
        }

        li:nth-child(2n) {
            margin-right: 0;
        }

        img,
        span {
            display: inline-block;
            cursor: pointer;
            height: 100%;
            vertical-align: top;
        }

        img {
            width: 195px;
            height: 73px;
        }

        span {
            padding: 10px 10px 0 10px;
            font-size: 14px;
            color: #333;
            width: 175px;
            word-break: break-word;
            overflow: hidden;
            text-overflow: ellipsis;
        }
    }
}
</style>
