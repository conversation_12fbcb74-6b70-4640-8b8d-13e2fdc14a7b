<template>
    <div class="time-picker" v-clickoutside="toggle.bind(this, false)">
        <klk-input
            prepend-icon="icon_time_time"
            readonly
            class="time-input"
            @focus="toggle(true)"
            :value="formatValue(value)"
        >
            <klk-icon
                style="margin-right: 12px"
                slot="append"
                type="icon_navigation_chevron_down"
                @click="toggle()"
            ></klk-icon>
        </klk-input>

        <ul class="time-dropdown" v-show="visible">
            <li
                v-for="(item, index) in times"
                :key="index"
                @click="setValue(index)"
                :class="[
                    'time-dropdown-item',
                    value === item ? 'active' : '',
                    item.stock === 0 ? 'disabled' : '',
                ]"
            >
                <span
                    >{{ formatValue(item) }}&nbsp;{{
                        item.stock === 0 ? $t('datepicker.date.soldout') : ''
                    }}</span
                >
            </li>
        </ul>
    </div>
</template>

<script>
import Clickoutside from '@/directives/clickoutside';
import { standardFormatDate } from './util';

export default {
    name: 'TimePicker',
    props: ['times', 'value'],
    data() {
        return {
            visible: false,
        };
    },
    directives: {
        clickoutside: Clickoutside,
    },
    methods: {
        toggle(isShow) {
            this.visible = isShow !== undefined ? isShow : !this.visible;
        },
        setValue(index) {
            const _time = this.times[index];
            if (_time.stock === 0) {
                return;
            }
            this.$emit('input', _time);
            this.toggle(false);
        },
        formatValue(arrangement) {
            return arrangement && arrangement.date
                ? standardFormatDate(arrangement.date, 1)
                : '';
        },
    },
};
</script>

<style lang="scss">
.time-picker {
    width: 300px;
    position: relative;
    input {
        cursor: pointer;
    }

    .time-input {
        border: none;
        height: 40px;
        width: 100%;
        font-size: 14px;

        &:hover,
        &:focus {
            border: none;
        }
    }

    .time-dropdown {
        padding: 0;
        margin: 0;
        position: absolute;
        z-index: 111;
        background: #fff;
        border: 1px solid #eee;
        box-shadow: 0 0 3px 1px #ccc;
        width: 100%;
        max-height: 203px;
        overflow-y: scroll;

        li {
            list-style: none;
            line-height: 40px;
            height: 40px;
            border-bottom: 1px solid #eee;
            padding-left: 48px;
            cursor: pointer;

            &:hover {
                background: #f5f5f5;
            }
        }

        li:last-child {
            border-bottom: none;
        }

        .active {
            color: var(--primary-color);
        }

        .disabled {
            color: #d1d1d1;
            cursor: not-allowed;

            &:hover {
                background: #fff;
            }
        }
    }
}
</style>
