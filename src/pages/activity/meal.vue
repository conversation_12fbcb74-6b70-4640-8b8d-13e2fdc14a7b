<template lang="html">
    <div class="activity-meal">
        <header class="header">
            {{ $t('activity.v2.label.package_options') }}
        </header>
        <div class="view-datetime">
            <div class="datetime-button" v-show="activityPackages.length > 1">
                <klk-poptip
                    v-model="showDatePicker"
                    trigger="click"
                    :arrow="false"
                    placement="bottom-start"
                    :max-height="360"
                >
                    <div>
                        <klk-button icon="icon_time_calendar">
                            {{ $t('activity.v2.button.check_availability') }}
                        </klk-button>
                        <span class="datetime-info">
                            {{ activityInfo.date_description }}
                        </span>
                    </div>
                    <klk-date-picker
                        slot="content"
                        class="package-date-picker"
                        :date.sync="selectedDate"
                        shadow
                        @select="showDatePicker = false"
                        :should-disable-date="disabledDate"
                    ></klk-date-picker>
                </klk-poptip>
            </div>
        </div>
        <ul class="package-list">
            <package-item
                v-if="!item.is_blocked"
                v-for="item in activityPackages"
                :key="item.package_id"
                :name="item.package_id"
                :package-info="item"
                :initial-date="selectedDate"
                :only-one-package="activityPackages.length === 1"
                :currency-symbol="currencySymbol"
                @get-time="getTime"
            >
                <package-detail
                    :cut-off-time="cutOffTime"
                    :package-info="item"
                ></package-detail>
            </package-item>
        </ul>
    </div>
</template>

<script>
import urlObj from '@/common/url';
import PackageDetail from './components/package-detail.vue';
import PackageItem from './package-item.vue';
import {
    isDisabledDate,
    formatDate,
    getEarliestAvailableDate,
    standardFormatDate,
    countOriginalSellPrice,
} from './util';

/**
 * 逻辑：如果activityInfo.packages的数量大于1且至少有一个package的库存 has_stocks为true，则显示：选择预定日期
 * 否则显示已售馨， 如果activity.packages的数量===1,则默认展开该package
 *
 * 具体交互逻辑：
 * 1、根据activityid获取schedules数据
 * 2、点击最上方的“查看可预订日期按钮” 根据schedules数据，显示当前可选、禁用、已售馨初始化日期选择框
 * 3、如果选择了最上方的预定日期，则需要初始化各个package的数据，初始化package数据的流程如下：
 *    1、设置package的日期选择框的时间
 *    2、如果当前环境中没有缓存该package的schedules,则获取schedules，并缓存， 接口：'/v1/usrcsrv/packages/' + packageId + '/schedules'
 *    3、根据schedules数据初始化time选择框
 *    4、根据选择的arrangementId，获取unit信息：'/v1/usrcsrv/units/'+arrangement.arrangement_id
 *    5、对package的显示进行排序
 *    6、如果activityInfo.exist_sub_pkg = true, 则需要按照日期来重新拉取价格, 需要拉取价格然后展示新的价格：/v1/usrcsrv/activities/{activity_id}/schedule/{date}/package_price'
 */
export default {
    name: 'MealComponent',
    props: ['activityInfo', 'schedules', 'exitSubPkg'],
    data: function() {
        return {
            selectedDate: null,
            subPackages: [],
            subPackagesDate: '',
            isFetching: false,
            cutOffTime: [],
            showDatePicker: false,
        };
    },
    components: {
        PackageItem,
        PackageDetail,
    },
    watch: {
        activityInfo(newVal) {
            this.$store.commit('activity/UPDATE_ACTIVITY_INFO', newVal);
        },
    },
    computed: {
        activityPackages() {
            const originalPackages = (this.activityInfo || {}).packages || [];

            let stocks = null;

            if (this.selectedDate && this.schedules) {
                stocks = this.schedules[
                    formatDate(this.selectedDate, 'YYYY-MM-DD')
                ];
            }
            /*
            FIX online change date not able to refresh packages, code not working is above
            */
            const subPackages = Array.isArray(this.subPackages)
                ? this.subPackages.reduce((pre, item) => {
                      pre[item.sub_id] = item;
                      return pre;
                  }, {})
                : null;

            return originalPackages
                .map(item => {
                    const subInfo = subPackages && subPackages[item.package_id];
                    let priceInfo = {};

                    if (subInfo) {
                        priceInfo = {
                            sell_price: subInfo.selling_price,
                            market_price: subInfo.market_price,
                            original_sell_price: subInfo.original_sell_price,
                        };
                    }

                    let returnItem = {
                        ...item,
                        ...priceInfo,
                        stock: stocks
                            ? stocks[item.package_id] || 0
                            : Number(item.has_stocks),
                        firstAvailableDate: getEarliestAvailableDate(
                            item.package_id,
                            this.schedules,
                        ),
                    };
                    returnItem.market_price = countOriginalSellPrice(
                        returnItem,
                    );
                    return returnItem;
                })
                .sort((a, b) => (b.stock > 0) - (a.stock > 0));
        },
        currencySymbol() {
            return this.getCurrencySymbolByCurrencyCode(
                this.activityInfo.currency,
            );
        },
    },
    methods: {
        getTime($event) {
            this.cutOffTime = $event;
        },
        standardFormatDate,
        disabledDate(date) {
            return isDisabledDate(date, this.schedules);
        },
        fetchSubPackageInfo(date) {
            if (!this.exitSubPkg || this.isFetching) {
                return;
            }
            const dateStr = formatDate(date, 'YYYY-MM-DD');

            if (dateStr === this.subPackagesDate) {
                return;
            }

            this.isFetching = true;

            klook.ajaxGet(
                urlObj.get_package_price_by_activity_id_and_date(
                    this.activityInfo.id,
                    dateStr,
                ),
                {},
                res => {
                    if (res.success) {
                        this.subPackages = res.result;
                        this.subPackagesDate = dateStr;
                    }
                    this.isFetching = false;
                },
            );
        },
    },
};
</script>

<style lang="scss">
.activity-meal {
    position: relative;

    .o-hidden {
        overflow: hidden;
    }

    .header {
        font-size: 24px;
        color: #424242;
        line-height: 33px;
        margin-bottom: 20px;
    }

    .view-datetime {
        margin-bottom: 20px;
        width: 760px;
        position: relative;

        .klk-poptip-popper-inner {
            padding: 0;
        }
        span {
            display: inline-block;
        }

        .datetime-info {
            vertical-align: middle;
            max-width: 464px;
            font-size: 14px;
            line-height: 1.5;
            word-wrap: break-word;
        }
        .datetime-button {
            .klk-button {
                margin-right: 20px;
            }
        }
    }

    .package-list {
        position: relative;
    }
}
</style>
