<template>
    <div
        class="package-item-unit"
        ref="packageItem"
        v-clickoutside="toggle.bind(this, false)"
    >
        <klk-input
            class="unit-input"
            @focus="handleFocus(true)"
            :value="formatUnits(value)"
            :placeholder="$t('activity.tips.please_select_unit')"
            readonly
            prepend-icon="participant-desc"
        >
            <klk-icon
                style="margin-right: 12px"
                slot="append"
                type="icon_navigation_chevron_down"
                @click="handleFocus()"
            ></klk-icon>
        </klk-input>

        <div
            class="unit-content"
            v-show="visible"
            v-loading="units.length === 0"
        >
            <div
                class="top-tip"
                v-show="minPax > 1"
                v-html="$t('activity.v2.error.package_min_pax', [minPax])"
            ></div>
            <ul class="unit-list">
                <li
                    v-for="(item, index) in units"
                    :key="index"
                    :class="{
                        'unit-list-item': true,
                        'sold-out': item.stock === 0,
                    }"
                >
                    <div class="title">
                        <span class="title_price">{{ item.name }}</span>
                        <div
                            class="msp_tip"
                            :ref="'msp' + index"
                            v-if="item.sku_msp_info"
                        >
                            <span
                                class="review-icon"
                                @mouseenter="
                                    handleTipShow(
                                        'msp' + index,
                                        item.sku_msp_info,
                                    )
                                "
                                @mouseleave="closeMsp"
                            >
                                <klk-icon type="icon_tips_tips"></klk-icon>
                            </span>
                            <span class="msp">{{ $t('msp') }}</span>
                        </div>
                        <br />
                        <span class="title_tip" v-show="item.required">{{
                            $t('must_buy')
                        }}</span>
                    </div>
                    <div class="selling-soldout" v-if="item.is_price_overflow">
                        {{ $t('activity.v2.btn.sold_out') }}
                    </div>
                    <div class="flash-sale-icon-wrapper">
                        <span
                            v-if="item.is_promotion"
                            class="flash-sale-icon"
                            >{{ $t('flash_sale') }}</span
                        >
                    </div>
                    <div class="operation">
                        <span
                            class="market-price"
                            v-if="+item.market_price > +item.price"
                        >
                            <span>{{ currencySymbol }} </span>
                            <span>{{ item.market_price }}</span>
                        </span>
                        <span class="selling-price">
                            <span>{{ currencySymbol }} </span>
                            <span>{{ item.price }}</span>
                        </span>
                        <span
                            class="quality"
                            :class="{ unclick: item.is_price_overflow }"
                        >
                            <span
                                class="quality-delete"
                                @click.stop="deleteQuality(index, item)"
                            >
                            </span>
                            <input
                                class="quality-size"
                                type="text"
                                :value="quality[index]"
                                @input="handleInput($event, index, item)"
                                @paste="handleInput($event, index, item)"
                                @keypress="handleInputKeypress"
                                :disabled="item.is_price_overflow"
                            />
                            <span
                                :class="{
                                    'quality-add': true,
                                    'quality-gray':
                                        quality[index] >= item.max_pax ||
                                        sum >= maxPax,
                                }"
                                @click.stop="addQuality(index, item)"
                            >
                            </span>
                        </span>
                        <div class="quality-tip" v-show="item.min_pax > 0">
                            {{
                                $t('activity.error.price_min', [
                                    item.min_pax,
                                    '',
                                ])
                            }}
                        </div>
                    </div>
                </li>
            </ul>
            <div class="unit-confirm">
                <span
                    :class="['ok', !isValid ? 'disabled' : '']"
                    @click="handleConfirm"
                    >{{ $t('confirm') }}</span
                >
                <span class="cancel" @click="visible = false">{{
                    $t('cancel')
                }}</span>
            </div>
            <div class="unit-toast-container" v-show="showToastMsg">
                <div class="unit-toast-content">{{ toastMsg }}</div>
            </div>
        </div>
        <msptip :mspInfo="mspInfo" :visible="tipVisible" :propStyle="style">
        </msptip>
    </div>
</template>

<script>
import Clickoutside from '@/directives/clickoutside';
import Msptip from './msp-tip.vue';

export default {
    name: 'PackageUnit',
    props: {
        units: { type: Array, default: () => [] },
        value: { type: Array, default: () => [] },
        minPax: { type: Number, default: 0 },
        maxPax: { type: Number, default: 9999 },
        currencySymbol: { type: String, default: '' },
        inventories: {
            type: Object,
            default: () => {},
        },
    },
    data: function() {
        return {
            visible: false,
            quality: [],
            showToastMsg: false,
            toastMsg: '',
            unitsCount: {},
            stockMap: {},
            tipVisible: false,
            mspInfo: [],
            style: null,
        };
    },
    components: { Msptip },
    computed: {
        isValid() {
            let result = true;
            result = this.units.every((item, index) => {
                if (item.required || this.quality[index] >= 1) {
                    return (
                        (this.quality[index] || 0) >=
                            Math.max(item.min_pax, 1) &&
                        (this.quality[index] || 0) <= (item.max_pax || 100000)
                    );
                }
                return true;
            });

            const unitsAllQuality = this.quality.reduce((pre, cur) => {
                return pre + cur;
            }, 0);

            result =
                result &&
                unitsAllQuality >= Math.max(this.minPax, 1) &&
                unitsAllQuality <= (this.maxPax || 100000);
            return result;
        },
        sum() {
            return this.quality.reduce((pre, cur) => {
                return pre + cur;
            }, 0);
        },
    },
    directives: {
        clickoutside: Clickoutside,
    },
    watch: {
        units: {
            handler: function(newVal) {
                this.quality = (newVal || []).map(item =>
                    item.required ? +item.min_pax : 0,
                );
            },
            immediate: true,
        },
    },
    methods: {
        toggle(isShow) {
            this.visible = typeof isShow === 'boolean' ? isShow : !this.visible;
        },
        handleConfirm() {
            if (!this.isValid) {
                return;
            }
            const _units = this.units
                .map((item, index) => {
                    item.quality = this.quality[index];
                    return item;
                })
                .filter((item, index) => this.quality[index] > 0);
            this.$emit('input', _units);
            this.toggle(false);
        },
        addQuality(index, item) {
            if (item.is_price_overflow) {
                return false;
            }
            const _value = parseInt(this.quality[index], 10);
            const inventory_id = item.inventory_id;

            if (!this.checkQualitySum(index, _value + 1)) {
                return;
            }
            // unit 超过最大值时弹框提示
            if (
                this.quality[index] >= item.max_pax ||
                this.sum >= this.maxPax
            ) {
                this.showToast(
                    this.$t('activity.error.price_max_v2', [item.max_pax]),
                );
                return;
            }

            this.stockMap[inventory_id] = this.inventories[inventory_id];
            const _max = Math.min(
                this.stockMap[inventory_id],
                Math.min(item.max_pax, 99),
            );

            if (this.unitsCount[inventory_id] >= this.stockMap[inventory_id]) {
                this.showToast(this.$t('activity.error.price_max_v2', [_max]));
                return;
            }

            if (this.unitsCount[inventory_id]) {
                this.unitsCount[inventory_id] += 1;
            } else {
                this.unitsCount[inventory_id] = _value + 1;
            }

            this.quality.splice(
                index,
                1,
                item.min_pax && _value < item.min_pax
                    ? item.min_pax
                    : _value + 1,
            );
        },
        deleteQuality(index, item) {
            if (item.is_price_overflow) {
                return false;
            }
            const _value = parseInt(this.quality[index], 10);
            const inventory_id = item.inventory_id;

            if (_value <= Math.max(item.required ? item.min_pax : 0, 0)) {
                return;
            }

            if (this.unitsCount[inventory_id]) {
                this.unitsCount[inventory_id] -= 1;
            }

            this.quality.splice(
                index,
                1,
                item.min_pax && _value < item.min_pax ? 0 : _value - 1,
            );
        },
        handleFocus(isShow) {
            if (this.$parent.selectedTime.arrangement_id === undefined) {
                return;
            }
            this.toggle(isShow);
        },
        formatUnits(selectedUnits) {
            return selectedUnits
                .reduce((accumulator, curValue) => {
                    accumulator +=
                        curValue.name + ' x ' + curValue.quality + ' , ';
                    return accumulator;
                }, '')
                .slice(0, -3);
        },
        handleInputKeypress(event) {
            if (event.keyCode < 48 || event.keyCode > 57) {
                event.preventDefault();
            }
        },
        handleInput(event, index, item) {
            if (item.is_price_overflow) {
                return false;
            }
            const _value = event.target.value;
            const inventory_id = item.inventory_id;

            if (!this.checkQualitySum(index, +_value)) {
                return;
            }

            this.stockMap[inventory_id] = this.inventories[inventory_id];
            const _max = Math.min(
                this.stockMap[inventory_id],
                Math.min(item.max_pax, 99),
            );

            if (+_value <= _max && this.hasStock(item, +_value, index)) {
                this.quality.splice(
                    index,
                    1,
                    Math.max(+_value, item.required ? item.min_pax : 0),
                );
            } else {
                // 当输入超过库存时自动设置为能输入的最大值
                this.quality.splice(
                    index,
                    1,
                    _max - this.restStock(item, index),
                );
            }
            this.setSelectdAmount(item);
        },
        checkQualitySum(index, value) {
            const _quality = this.quality.concat();
            _quality[index] = value;
            const all = _quality.reduce((pre, cur) => {
                return pre + cur;
            }, 0);
            if (this.maxPax && all > this.maxPax) {
                this.showToast(
                    this.$t('activity.error.package_max', [this.maxPax]),
                );
                return false;
            }
            return true;
        },
        hasStock(item, value, index) {
            const selectedQuality = this.unitsCount[item.inventory_id];
            const _max = Math.min(
                this.stockMap[item.inventory_id],
                Math.min(item.max_pax, 99),
            );

            if (selectedQuality + value > _max && value > this.quality[index]) {
                this.showToast(this.$t('activity.error.price_max_v2', [_max]));
                return false;
            }

            return true;
        },
        restStock(item, index) {
            const quality = this.quality || [];
            let restStockRes = quality.filter((qlt, idx) => {
                return (
                    qlt &&
                    idx !== index &&
                    this.units[idx].inventory_id === item.inventory_id
                );
            });

            return restStockRes.reduce((prev, cur) => {
                return prev + cur;
            }, 0);
        },
        setSelectdAmount() {
            this.unitsCount = {};

            this.quality.forEach((qlt, index) => {
                if (qlt) {
                    if (this.unitsCount[this.units[index].inventory_id]) {
                        this.unitsCount[this.units[index].inventory_id] += qlt;
                    } else {
                        this.unitsCount[this.units[index].inventory_id] = qlt;
                    }
                }
            });
        },
        showToast(msg) {
            if (msg) {
                this.toastMsg = msg;
                this.showToastMsg = true;
                setTimeout(() => {
                    this.showToastMsg = false;
                }, 2000);
            }
        },
        handleTipShow(el, mspInfo) {
            if (mspInfo) {
                this.mspInfo = mspInfo;
                this.tipVisible = true;
                this.style = this.getPostion(this.$refs[el][0]);
            }
        },
        getPostion(node) {
            const getParentPos = (top, left, node) => {
                if (node !== this.$refs.packageItem) {
                    top += node.offsetTop;
                    left += node.offsetLeft;
                    return getParentPos(top, left, node.offsetParent);
                } else {
                    return {
                        top: top,
                        left: left,
                    };
                }
            };

            let { top, left } = getParentPos(0, 0, node);
            return {
                top: top,
                left: left + node.offsetWidth,
            };
        },
        closeMsp() {
            this.tipVisible = false;
        },
    },
};
</script>

<style lang="scss">
.package-item-unit {
    width: 300px;
    position: relative;

    .unit-input {
        cursor: pointer;
        .unit-input-prepend {
            padding: 0 12px;
        }
        input {
            cursor: pointer;
        }
    }

    .down-arrow {
        top: 13px;
        font-size: 14px;
        right: 0;
    }

    .unit-content {
        font-size: 14px;
        line-height: 20px;
        background: #fff;
        z-index: 90;
        position: absolute;
        left: 0;
        box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.12);
        border: solid 1px #e0e0e0;
        border-radius: 2px;
        min-width: 500px;

        .top-tip {
            background: #f5f5f5;
            padding: 13px 20px;

            span {
                color: var(--primary-color);
            }
        }

        .unit-list {
            user-select: none;
            padding: 0;
            margin: 0;

            .unit-list-item {
                padding: 15px 20px;
                color: #333;
                box-shadow: 0 1px 0 0 #e0e0e0;
                overflow: hidden;
                display: table;
                width: 100%;
                position: relative;

                span {
                    display: inline-block;
                }

                .title,
                .operation,
                .selling-soldout {
                    display: table-cell;
                    vertical-align: middle;
                }

                .title {
                    padding-right: 20px;
                    width: 200px;

                    &:before {
                        content: '';
                        width: 0;
                        height: 100%;
                        display: inline-block;
                        vertical-align: middle;
                    }

                    span {
                        vertical-align: middle;
                        word-break: break-word;
                    }

                    .title_price {
                        line-height: 20px;
                        max-width: 90px;
                    }

                    .title_tip {
                        font-size: 12px;
                        line-height: 1.2;
                        color: #ffa628;
                        margin-top: 6px;
                    }

                    .msp_tip {
                        display: inline;
                        margin-left: 10px;
                        z-index: 9999999;

                        .review-icon {
                            display: inline-flex;
                            justify-items: center;
                            align-items: center;

                            svg {
                                position: relative;
                                margin: 0 4px 0 0;
                                top: 0;
                            }
                        }

                        .msp {
                            font-weight: 700;
                        }
                    }
                }

                .operation {
                    position: relative;
                    text-align: right;
                    white-space: nowrap;

                    .quality-tip {
                        font-size: 12px;
                        line-height: 1.2;
                        text-align: right;
                        position: absolute;
                        width: 100%;
                        text-align: right;
                    }
                    .unclick {
                        span,
                        input {
                            cursor: not-allowed;
                        }
                    }
                }

                .market-price,
                .selling-price,
                .quality {
                    line-height: 32px;
                    vertical-align: middle;
                }

                .market-price {
                    color: #b2b2b2;

                    span {
                        text-decoration: line-through;
                    }
                }

                .selling-price {
                    margin: 0 20px 0 10px;
                }

                .quality {
                    border: 1px solid #ccc;
                    height: 32px;

                    .quality-add,
                    .quality-delete {
                        background: #fafafa;
                        width: 32px;
                        height: 100%;
                        position: relative;
                        cursor: pointer;

                        &:before {
                            content: '';
                            width: 10px;
                            height: 2px;
                            background: #7d7d7d;
                            position: absolute;
                            top: 15px;
                            left: 11px;
                        }

                        &:hover {
                            background: #f5f5f5;
                        }
                    }

                    .quality-delete {
                        border-right: 1px solid #ccc;
                    }

                    .quality-size {
                        vertical-align: top;
                        width: 30px;
                        border: none;
                        padding: 0;
                        height: 100%;
                        text-align: center;
                    }

                    .quality-add {
                        border-left: 1px solid #ccc;

                        &:after {
                            content: '';
                            width: 2px;
                            height: 10px;
                            position: absolute;
                            top: 11px;
                            left: 15px;
                            background: #7d7d7d;
                        }
                    }

                    .quality-gray {
                        &:after,
                        &:before {
                            background: #ccc;
                        }

                        &:hover {
                            background: #fafafa;
                        }
                    }
                }

                .flash-sale-icon-wrapper {
                    position: absolute;
                    top: 50%;
                    left: 100px;
                    transform: translate3d(0, -50%, 0);
                    -webkit-transform: translate3d(0, -50%, 0);

                    .flash-sale-icon {
                        transform: scale(0.8);
                        -webkit-transform: scale(0.8);
                    }
                }
            }
        }

        .unit-confirm {
            padding: 16px 20px;
            font-size: 14px;
            overflow: hidden;

            span {
                float: right;
                padding: 7px 21px;
                cursor: pointer;
            }

            .cancel {
                margin-right: 20px;
                color: #888;
                background: #fff;

                &:hover {
                    background: #f5f5f5;
                }
            }

            .ok {
                background: var(--primary-color);
                color: #fff;
            }

            .disabled {
                cursor: not-allowed;
                background: #d5d5d5;
            }
        }
    }

    .unit-toast-container {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        text-align: center;

        &:before {
            content: '';
            display: inline-block;
            vertical-align: middle;
            height: 100%;
        }

        .unit-toast-content {
            user-select: none;
            display: inline-block;
            line-height: 20px;
            max-width: 90%;
            padding: 5px;
            background: #4d4d4d;
            color: #fff;
            vertical-align: middle;
        }
    }
}
</style>
