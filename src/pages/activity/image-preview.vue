<template>
    <transition name="fade">
        <div
            class="image-preview-container"
            v-transfer-dom="true"
            v-show="visible && curIndex !== -1"
        >
            <span class="close" @click="visible = false">+</span>
            <div class="wrapper">
                <div class="image-wrapper" :style="imageWrapperStyle">
                    <div class="image-desc">
                        {{ curIndex + 1 }} / {{ imageList.length }}:
                        {{ imageDesc }}
                    </div>
                    <img
                        class="img-instance"
                        :width="properMaxWidth * scale + 'px'"
                        :src="imageSrc"
                    />
                </div>
            </div>
            <div class="image-operation">
                <span class="smaller" @click="minify()"></span>
                <span class="larger" @click="enlarge()"></span>
            </div>
            <span class="left-arrow" @click="pre"
                ><klk-icon
                    type="icon_navigation_chevron_left"
                    size="30"
                ></klk-icon
            ></span>
            <span class="right-arrow" @click="next"
                ><klk-icon
                    type="icon_navigation_chevron_right"
                    size="30"
                ></klk-icon
            ></span>
        </div>
    </transition>
</template>

<script>
import transfer_dom from '@/directives/transfer-dom';
import throttle from 'lodash/throttle';

export default {
    name: 'ImagePreview',
    props: {
        imageListProp: {
            type: Array,
            default: () => [],
        },
    },
    directives: {
        'transfer-dom': transfer_dom,
    },
    data: function() {
        return {
            curIndex: -1,
            imageList: [],
            visible: false,
            scale: 1,
            maxHeight: 0,
            maxWidth: 0,
            defaultMaxHeight: 476,
            defaultMaxWidth: 846,
            paddingTop: 60,
            paddingBottom: 164,
        };
    },
    mounted() {
        this.caculateImageWrapperSize();
        window.addEventListener(
            'resize',
            throttle(() => {
                if (window.innerHeight < this.defaultMaxHeight) {
                    return;
                }
                this.caculateImageWrapperSize();
            }, 600),
        );
    },
    watch: {
        visible(newValue) {
            document.body.style.overflow = newValue ? 'hidden' : '';
        },
        imageListProp: {
            handler: function(newValue) {
                this.imageList = newValue.map(img => {
                    // return Object.assign({},img,this.getImageSize(img.image_url_host))
                    return Object.assign({}, img);
                });
            },
            immediate: true,
        },
    },
    computed: {
        imageSrc() {
            let image = this.imageList[this.curIndex || 0] || {};
            return image.image_url_host || image.image_url || '';
        },
        imageDesc() {
            return (this.imageList[this.curIndex || 0] || {}).image_desc || '';
        },
        imageWrapperStyle() {
            return {
                height: this.maxHeight + 'px',
                width: this.maxWidth + 'px',
            };
        },
        properMaxWidth() {
            if (this.curIndex < 0 || !this.visible) return 0;
            let properHeight = 0;
            let img = this.imageList[this.curIndex];
            if (img.width / img.height >= this.maxWidth / this.maxHeight) {
                //
                return img.width > this.maxWidth ? this.maxWidth : img.width;
            } else {
                properHeight =
                    img.height > this.maxHeight ? this.maxHeight : img.height;
                return properHeight * (img.width / img.height);
            }
        },
    },
    methods: {
        caculateImageWrapperSize() {
            this.maxHeight =
                window.innerHeight - this.paddingTop - this.paddingBottom;
            this.maxWidth = this.maxHeight * (16 / 9);
        },
        // getImageSize(src) { //compute
        //     let image = new Image()
        //     image.src = src
        //     return {
        //         width : image.width,
        //         height: image.height
        //     }
        // },
        getProperMaxWidth(img) {
            if (this.curIndex < 0 || !this.visible) return;
            let properHeight = 0;
            if (img.width / img.height >= this.maxWidth / this.maxHeight) {
                //
                return img.width > this.maxWidth ? this.maxWidth : img.width;
            } else {
                properHeight =
                    img.height > this.maxHeight ? this.maxHeight : img.height;
                return properHeight * (img.width / img.height);
            }
        },
        pre() {
            this.curIndex = Math.max(this.curIndex - 1, 0);
        },
        next() {
            this.curIndex = Math.min(
                this.curIndex + 1,
                this.imageList.length - 1,
            );
        },
        enlarge() {
            this.scale = 1;
        },
        minify() {
            this.scale = 0.5;
        },
    },
    created() {
        document.body.style.overflow = '';
    },
};
</script>

<style lang="scss">
.image-preview-container {
    position: fixed;
    top: 100px;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1000;
    background: rgba(0, 0, 0, 0.8);

    .close {
        position: absolute;
        right: 20px;
        top: 20px;
        cursor: pointer;
        transform: rotate(45deg);
        font-size: 30px;
        color: #fff;
        z-index: 100;
    }

    .wrapper {
        position: absolute;
        height: 100%;
        width: 100%;
        padding: 60px 210px 164px 210px;
        justify-content: center;
        align-items: center;
        display: flex;
        .image-wrapper {
            display: flex;
            justify-content: center;
            align-items: center;
            position: relative;
            .img-instance {
                transition: all 0.6s ease;
            }
            .image-desc {
                width: 100%;
                text-align: center;
                color: #fff;
                position: absolute;
                bottom: -30px;
            }
        }
    }

    .image-operation {
        position: absolute;
        width: 100%;
        text-align: center;
        bottom: 60px;
    }

    .smaller,
    .larger {
        display: inline-block;
        background: #000;
        width: 42px;
        height: 42px;
        cursor: pointer;
        position: relative;

        &:before {
            content: '';
            width: 12px;
            height: 2px;
            background: #fff;
            position: absolute;
            top: 20px;
            left: 15px;
        }
    }

    .larger {
        border-radius: 0 2px 2px 0;

        &:after {
            content: '';
            width: 2px;
            height: 12px;
            position: absolute;
            top: 15px;
            left: 20px;
            background: #fff;
        }
    }

    .smaller {
        border-radius: 2px 0 0 2px;

        &:after {
            content: '';
            height: 100%;
            width: 1px;
            background: #fff;
            opacity: 0.15;
            position: absolute;
            right: 0;
        }
    }

    .left-arrow,
    .right-arrow {
        display: inline-block;
        width: 50px;
        height: 50px;
        border-radius: 100%;
        background: #000;
        opacity: 0.2;
        top: 50%;
        position: absolute;
        margin-top: -25px;
        cursor: pointer;
        color: #fff;

        .klk-icon {
            font-size: 30px;
            position: absolute;
            top: 10px;
            left: 10px;
            color: #fff;
        }

        &:hover {
            opacity: 1;
        }
    }

    .left-arrow {
        left: 12px;
    }

    .right-arrow {
        right: 12px;
    }
}
</style>
