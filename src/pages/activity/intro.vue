<template>
    <div class="intro">
        <div class="intro-header">
            <div class="intro-header-nav">
                <span
                    ><router-link :to="{ name: 'agent_index' }">{{
                        $t('global.text.index')
                    }}</router-link></span
                >
                <span class="left-arrow"
                    ><klk-icon
                        type="icon_navigation_chevron_down"
                        size="10"
                    ></klk-icon
                ></span>
                <span
                    ><router-link
                        :to="{
                            name: 'city_activity',
                            params: { city_id: activityInfo.city_id },
                        }"
                        >{{ activityInfo.city_name }}</router-link
                    ></span
                >
                <span class="left-arrow"
                    ><klk-icon
                        type="icon_navigation_chevron_down"
                        size="10"
                    ></klk-icon
                ></span>
                <span
                    ><a href="javascript:void(0);" class="active">{{
                        activityInfo.title
                    }}</a></span
                >
            </div>
            <h1 class="title">{{ activityInfo.title }}</h1>
            <div class="subtitle">
                <div class="subtitle-content">
                    {{ activityInfo.subtitle }}
                </div>
                <div>
                    <div
                        @mouseenter="isHover = true"
                        @mouseleave="isHover = false"
                        :class="collectClass"
                        @click="collect"
                    >
                        <klk-icon
                            size="24"
                            :color="
                                activityInfo.is_wish || isHover
                                    ? '#ec6337'
                                    : '#333'
                            "
                            :type="
                                activityInfo.is_wish && !isHover
                                    ? 'collect-fill'
                                    : 'icon_social_favorite'
                            "
                        >
                        </klk-icon>

                        <span>{{ $t('favorites') }}</span>
                    </div>
                </div>
            </div>
        </div>
        <div class="intro-detail">
            <announcement-component :klook_insider="activityInfo.klook_insider">
            </announcement-component>
            <feature-icons
                :multi_language_icons="multiLanguageIcons"
            ></feature-icons>
        </div>
        <div class="intro-reason" v-html="activityHighLight"></div>
        <div
            :class="{ 'intro-booking': true, 'sold-out': isSoldOut }"
            :style="introBookingStyle"
            id="intro-booking"
        >
            <div
                class="count-wrapper"
                v-if="activityInfo.is_promotion || isEnded"
            >
                <count-down
                    :start-time="activityInfo.start_promotion_time"
                    :end-time="activityInfo.end_promotion_time"
                    :finished-data="$t('promotion_finished_data')"
                    :show-text="$t('end_in')"
                    @finishCallback="finishCallback"
                >
                </count-down>
            </div>
            <div class="intro-booking_price">
                <span
                    class="selling"
                    v-html="
                        $t('price_from', [
                            `${getCurrencySymbolByCurrencyCode(
                                activityInfo.currency,
                            )} ${activityInfo.selling_price}`,
                        ])
                    "
                ></span>
                <span
                    class="market"
                    v-if="
                        +activityInfo.market_price > +activityInfo.selling_price
                    "
                >
                    {{ activityInfo.market_price }}
                </span>
            </div>
            <div class="intro-booking_btn">
                <a
                    class="btn"
                    href="javascript:void(0);"
                    @click="jumpToPackageSection"
                >
                    <klk-icon
                        type="fast"
                        size="14"
                        v-show="!isSoldOut && activityInfo.instant"
                    ></klk-icon>
                    <span>
                        {{
                            isSoldOut
                                ? $t('activity.v2.btn.sold_out')
                                : $t('activity.v2.btn.book_now')
                        }}
                    </span>
                </a>
            </div>
            <div class="intro-booking_otherinfo">
                <div class="item">
                    <klk-icon size="18" color="#d1d1d1" type="date"></klk-icon>
                    <span>{{
                        getAvailableDateText(activityInfo.packages)
                    }}</span>
                </div>
                <div class="item">
                    <klk-icon
                        size="16"
                        :type="
                            activityInfo.instant
                                ? 'fast'
                                : 'confirmation-reverse'
                        "
                    ></klk-icon>
                    <span v-if="activityInfo.instant">{{
                        $t('activity.v2.label.instant_confirmation')
                    }}</span>
                    <span v-else>{{ confirmationType }}</span>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import { isAfter, isSameDay, addDays, isToday } from 'date-fns';
import throttle from 'lodash/throttle';
import { mapState } from 'vuex';
import collectMixin from '@/mixins/collect';
import CountDown from '@/pages/tpl/countdown.vue';
import FeatureIcons from './components/feature-icons.vue';
import announcementComponent from './components/activity-announcement.vue';
import { UtilMixin, offset, standardFormatDate } from './util';

export default {
    name: 'IntroComponent',
    props: {
        activityInfo: {
            type: Object,
            default: () => ({}),
        },
    },
    components: {
        FeatureIcons,
        CountDown,
        announcementComponent,
    },
    mixins: [UtilMixin, collectMixin],
    data() {
        return {
            isHover: false,
            introBookingFixed: false,
            isEnded: false,
        };
    },
    computed: {
        ...mapState('activity', ['active_package_id']),
        multiLanguageIcons() {
            if (!this.activityInfo) return [];
            if (this.activityInfo.use_section) {
                console.log(
                    'this.activityInfo.section_content.icons',
                    this.activityInfo.section_content.icons,
                );
                return this.activityInfo.section_content.icons;
            }
            return this.activityInfo.multi_language_icons || [];
        },
        activityHighLight() {
            if (this.activityInfo.use_section) {
                return this.markdownArrToHtml(
                    this.activityInfo.section_content.highlight_render_obj,
                );
            } else {
                return this.markdownToHtml(this.activityInfo.summary);
            }
        },
        isSoldOut() {
            return !(this.activityInfo.packages || []).some(
                packageItem => packageItem.has_stocks,
            );
        },
        introBookingStyle() {
            if (!(this.active_package_id > 0) && this.introBookingFixed) {
                return {
                    left: (window.innerWidth - 1160) / 2 + 800 + 'px',
                    top: '125px !important',
                    position: 'fixed !important',
                };
            }
            return {};
        },
        collectClass() {
            return {
                'activity-collection': true,
                'collection-active': this.isHover || this.activityInfo.is_wish,
            };
        },
        confirmationType() {
            return (
                this.activityInfo.all_icon_final &&
                this.activityInfo.all_icon_final.confirmation_type
            );
        },
    },
    mounted() {
        window.addEventListener(
            'scroll',
            this.handleCaculateIntroBookingPosition,
        );
    },
    methods: {
        handleCaculateIntroBookingPosition() {
            throttle(() => {
                this.caculateIntroBookingPosition();
            }, 30)();
        },
        caculateIntroBookingPosition() {
            let scrollTop = window.pageYOffset;

            if (scrollTop <= 500) {
                this.introBookingFixed = false;
            } else {
                this.introBookingFixed = true;
            }
        },
        jumpToPackageSection() {
            const _offset = offset(document.getElementById('meal'));
            if (_offset && _offset.top > 1) {
                document.documentElement.scrollTop = _offset.top - 50;
            }
        },
        getAvailableDateText(packages) {
            if (!Array.isArray(packages)) {
                return this.$t('activity.v2.label.activity_unavaiable');
            }
            let availableDates = packages
                .map(item => item.available_date)
                .sort((a, b) => (isAfter(a, b) ? 1 : -1));

            if (!availableDates[0]) {
                return this.$t('activity.v2.label.activity_unavaiable');
            }

            if (isToday(availableDates[0])) {
                return this.$t('activity.v2.label.today_available');
            }

            if (isSameDay(availableDates[0], addDays(new Date(), 1))) {
                return this.$t('book.tomorrow');
            }

            return (
                this.$t('activity.v2.label.earliest_available') +
                standardFormatDate.call(this, availableDates[0])
            );
        },

        finishCallback() {
            // when count down finished
            this.isEnded = true;
            this.$parent.fetchDetail();
        },
    },
    beforeDestroy() {
        window.removeEventListener(
            'scroll',
            this.handleCaculateIntroBookingPosition,
        );
    },
};
</script>

<style lang="scss">
.intro {
    background: #fff;

    .count-wrapper {
        font-size: 14px;
        margin-bottom: 20px;

        .pre-text {
            color: #ff5722;
            display: inline-block;
            vertical-align: middle;
            margin-right: 10px;
        }

        .count-down {
            display: inline-block;
            vertical-align: middle;
        }
    }

    .is-collect-icon {
        cursor: pointer;
    }

    .intro-header {
        padding-bottom: 24px;
        overflow: hidden;
        border-bottom: 1px solid #e0e0e0;

        .left-arrow {
            margin: 0 10px;
        }

        .intro-header-nav {
            font-size: 14px;
            line-height: 1.2;

            a.active {
                color: #666;
            }
        }

        .title {
            margin: 20px 0 16px 0;
            font-size: 32px;
            line-height: 40px;
            color: #424242;
            font-weight: 600;
        }

        .subtitle {
            display: flex;
            font-size: 14px;
            color: #333333;
            line-height: 1.5;

            .subtitle-content {
                flex: 1;
                padding-right: 120px;
                color: #333;
            }

            .activity-collection {
                display: flex;
                cursor: pointer;

                span {
                    margin-left: 8px;
                }

                &.collection-active {
                    color: #ec6337;
                }
            }

            svg {
                color: #000;
            }
        }
    }

    .intro-detail {
        padding: 30px 0 7px;
        border-bottom: 1px solid #e0e0e0;

        ul {
            display: flex;
            flex-wrap: wrap;

            li {
                padding: 0 38px 20px 0;

                svg {
                    display: inline-block;
                    font-size: 24px;
                    color: #666;
                    vertical-align: middle;
                    margin-right: 14px;
                }
            }
        }
    }

    .intro-reason {
        padding: 30px 0;
        border-bottom: 1px solid #e0e0e0;

        img {
            max-width: 100%;
        }

        ul {
            margin-left: 20px;

            li {
                line-height: 20px;
                margin-bottom: 6px;
                list-style: initial;
            }
        }
    }

    .intro-booking {
        background: #fff;
        position: absolute;
        left: 800px;
        top: 0;
        box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.2);
        padding: 20px 20px 9px;
        width: 360px;
        font-size: 0;
        z-index: 11;

        span {
            display: inline-block;
        }

        .selling {
            font-size: 24px;
            line-height: 28px;
            font-weight: bold;
            color: #333;
            margin-right: 11px;
        }

        .market {
            font-size: 14px;
            line-height: 18px;
            color: #888;
            vertical-align: bottom;
            text-decoration: line-through;
        }

        a.btn {
            height: 42px;
            line-height: 42px;
            display: inline-block;
            margin: 20px 0;
            background: var(--primary-color);
            color: #fff;
            font-size: 0;
            font-weight: bold;
            padding: 0;
            border-radius: 2px;
            border: none;
            outline: none;
            width: 100%;
            cursor: pointer;
            text-align: center;

            svg {
                margin-right: 12px;
                color: #fff;
                font-size: 14px;
            }

            span {
                font-size: 16px;
            }
        }

        .intro-booking_otherinfo {
            .item {
                margin-bottom: 8px;
            }

            svg {
                font-size: 16px;
                display: inline-block;
                margin-right: 12px;
                color: #d1d1d1;
                vertical-align: middle;
            }

            span {
                vertical-align: middle;
                font-size: 14px;
                line-height: 17px;
            }

            .klk-icon-fast {
                color: var(--primary-color);
            }
        }
    }

    .sold-out {
        a.btn {
            background: #d1d1d1;
        }
    }
}
</style>
