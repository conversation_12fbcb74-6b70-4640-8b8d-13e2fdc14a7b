<template>
    <div class="cut-off-time">
        <div class="cut-off-time-title">
            {{ $t('cut_off_time') }}
        </div>
        <div>
            <span class="day-tips">
                {{ getDay[0] }}
            </span>
            <span class="time-tips">
                {{ getDay[1] }}
            </span>
        </div>
    </div>
</template>
<script>
export default {
    name: 'CutOffTime',
    props: ['getDay'],
};
</script>
<style lang="scss">
.cut-off-time {
    margin-right: 39px;

    .cut-off-time-title {
        color: #999;
        font-size: 12px;
        line-height: 14px;
        margin-bottom: 2px;
    }

    .time-tips,
    .day-tips {
        display: inline-block;
        padding: 4px 8px;
        border: 1px solid #ff8f00;
    }

    .day-tips {
        background: #ff8f00;
        color: #fff;
    }

    .time-tips {
        background: #fff;
        color: #ff5722;
    }
}
</style>
