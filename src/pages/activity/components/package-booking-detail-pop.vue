<template>
    <div class="package-booking-detail-pop">
        <header>{{ $t('activity.v2.label.summary.bookding_details') }}</header>
        <div class="content-main">
            <div class="title">
                <span class="label">{{ $t('package_name') }}</span>
                <span class="package-name">{{
                    selectedPackage.package_name
                }}</span>
            </div>
            <div class="datetime">
                <div class="date">
                    <span class="label">{{
                        $t('activity.v2.label.summary.date')
                    }}</span>
                    <span class="date">{{
                        formatDateTime(selectedPackage.selectedTime)
                    }}</span>
                </div>
                <div class="time" v-show="isTimeShow">
                    <span class="label">{{
                        $t('activity.v2.label.summary.time')
                    }}</span>
                    <span class="time">{{
                        formatDateTime(selectedPackage.selectedTime, 1)
                    }}</span>
                </div>
            </div>
            <ul class="unit-list">
                <li
                    v-for="(item, index) in selectedPackage.selectedUnits"
                    :key="index"
                >
                    <span class="unit-name">{{ item.name }}</span>
                    <span class="unit-quality">x {{ item.quality }}</span>
                    <span class="unit-price"
                        >{{ currencySymbol }}
                        {{ item.price | formatPrice }}</span
                    >
                </li>
            </ul>
        </div>
    </div>
</template>

<script>
import { mapState } from 'vuex';
import { formatDate, UtilMixin, standardFormatDate } from '../util';

export default {
    name: 'PackageBookingDetailPop',
    mixins: [UtilMixin],
    props: {
        package_id: {
            type: Number,
        },
    },

    computed: {
        ...mapState('activity', ['activityInfo', 'packageBookingInfoMap']),

        currencySymbol() {
            return this.getCurrencySymbolByCurrencyCode(
                this.activityInfo.currency,
            );
        },
        selectedPackage() {
            return this.packageBookingInfoMap[this.package_id];
        },
        isTimeShow() {
            return (
                this.selectedPackage.selectedTime &&
                formatDate(this.selectedPackage.selectedTime.date, 'HH:mm') !==
                    '00:00'
            );
        },
    },
    methods: {
        formatDateTime(selectedTime, type) {
            return (selectedTime || {}).date
                ? standardFormatDate(selectedTime.date, type)
                : '';
        },
    },
};
</script>
<style lang="scss">
.package-booking-detail-pop {
    background: #fff;
    width: 360px;
    padding: 20px;
    font-size: 14px;
    color: #424242;

    header {
        font-size: 16px;
        line-height: 24px;
        font-weight: 600;
    }

    .title {
        word-break: break-word;
        margin: 16px 0;
        display: flex;
        justify-content: space-between;
        .label {
            color: #999;
        }
        .package-name {
            text-align: right;
        }
    }

    .datetime {
        display: flex;
        flex-direction: column;
        margin-bottom: 16px;
        .date,
        .time {
            display: flex;
            justify-content: space-between;
            .label {
                color: #999;
            }
        }
    }

    .unit-list {
        padding-top: 8px;
        border-top: 1px dashed #d5d5d5;

        li {
            display: table;
            width: 100%;
            padding-top: 8px;

            span {
                display: table-cell;
                vertical-align: middle;
            }

            .unit-name {
                color: #888;
                width: 50%;
                word-break: break-word;
            }

            .unit-quality {
                color: #888;
            }

            .unit-price {
                text-align: right;
            }
        }
    }
}
</style>
