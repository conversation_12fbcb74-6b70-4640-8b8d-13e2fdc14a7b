<template>
    <div class="section-render">
        <div
            v-for="(section, index) in sections"
            class="content-activity-booking-know"
            :key="index"
        >
            <div v-if="isPackageInfo" class="h2">{{ section.title }}</div>
            <header v-else>
                {{ section.title }}
            </header>

            <div v-for="(renderItem, index) in section.components" :key="index">
                <div
                    class="booking-know_item markdown-content"
                    :class="{
                        'booking-know_item': !isPackageInfo,
                    }"
                    v-if="renderItem.type === 'markdown'"
                    v-html="markdownArrToHtml(renderItem.data.render_obj)"
                ></div>
                <activity-images
                    v-if="renderItem.type === 'image'"
                    :images="renderItem.data.images"
                >
                </activity-images>
            </div>
        </div>
    </div>
</template>
<script>
import { UtilMixin } from '@/pages/activity/util';
import ActivityImages from '@/pages/tpl/activity-images.vue';

export default {
    name: 'SectionRender',
    mixins: [UtilMixin],
    components: {
        ActivityImages,
    },
    props: {
        sections: {
            type: Array,
            default: () => [],
        },
        isPackageInfo: {
            type: Boolean,
            default: () => false,
        },
    },
};
</script>
<style lang="scss">
.section-render {
    .use-section {
        width: 100%;
        margin-bottom: 30px;
        .h2 {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 10px;
        }
    }

    .content-activity-booking-know {
        border-top: 1px solid #eee;
        line-height: 1.5;
        font-size: 14px;
        margin-bottom: 30px;

        header {
            font-size: 24px;
            line-height: 1.5;
            color: #424242;
            padding: 25px 0;
            font-weight: 600;
        }

        .reminder {
            padding: 20px;
            background: #fffae5;
            margin-top: 30px;

            ul {
                margin-bottom: 0;
            }
        }

        .booking-know_item {
            margin-bottom: 20px;
        }
    }

    .h2 {
        font-size: 18px;
        font-weight: 600;
        margin-bottom: 10px;
    }

    .images {
        margin-top: 10px;
    }
}
</style>
