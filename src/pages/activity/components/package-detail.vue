<template>
    <div class="package-detail" :style="packageDetailStyle" v-show="visible">
        <div class="h1">
            {{ $t('activity.v2.btn.select_package1') }}
            {{ $t('activity.v2.label_more_info') }}
        </div>
        <div class="feature-detail">
            <feature-icons
                :multi_language_icons="multiLanguageIcons"
            ></feature-icons>
        </div>
        <template v-if="!packageInfo.use_section">
            <div class="description">
                <div class="h2">{{ $t('activity.v2.label_more_info') }}</div>
                <div
                    class="description_content markdown-content"
                    v-show="packageOption"
                    v-html="markdownToHtml(packageOption)"
                ></div>
            </div>
            <div class="confirmation">
                <div class="h2">
                    {{ $t('activity.v2.label.reserve_policy') }}
                </div>
                <div
                    class="confirmation-content markdown-content"
                    v-show="guideLines"
                    v-html="markdownToHtml(guideLines)"
                ></div>
                <!--退订须知-->
                <div
                    class="confirmation-content markdown-content"
                    v-show="confirmationDetail"
                    v-html="markdownArrToHtml(confirmationDetail)"
                ></div>
            </div>
            <div class="how-to-use">
                <div class="h2">{{ $t('activity.v2.label.how_to_use') }}</div>
                <div
                    class="how-use-item markdown-content"
                    v-html="markdownArrToHtml(howToUse)"
                ></div>
            </div>
            <activity-images :images="packageInfo.images"> </activity-images>
        </template>

        <!--use_section-->
        <template v-else>
            <section-render
                :isPackageInfo="true"
                :sections="packageInfo.section_content.sections"
            ></section-render>
        </template>

        <div v-if="cutOffTime.length" class="cut-off-time">
            <div class="title">Cut Off Time</div>
            <div class="time">{{ cutOffTime[0] }}{{ cutOffTime[1] }}</div>
        </div>
        <div class="bottom"></div>
    </div>
</template>

<script>
import { mapState } from 'vuex';
import throttle from 'lodash/throttle';
import ActivityImages from '@/pages/tpl/activity-images.vue';
import { UtilMixin } from '../util';
import FeatureIcons from './feature-icons.vue';
import SectionRender from './section-render.vue';

export default {
    name: 'PackageDetail',
    mixins: [UtilMixin],

    props: {
        packageInfo: {
            type: Object,
            required: true,
        },
        onlyOnePackage: {
            type: Boolean,
            default: () => false,
        },
        cutOffTime: {
            type: Array,
            default: () => [],
        },
    },
    components: {
        FeatureIcons,
        SectionRender,
        ActivityImages,
    },
    data() {
        return {
            packageDetailStyle: {},
            use_section_image: [],
        };
    },

    computed: {
        ...mapState('activity', ['active_package_id']),
        icons() {
            if (
                !this.packageInfo ||
                this.packageInfo.package_id === undefined
            ) {
                return [];
            }

            return this.packageInfo.multi_language_icons;
        },
        packageOption() {
            return this.packageInfo.sub_name
                ? `${this.packageInfo.sub_name}\n${this.packageInfo.package_option}`
                : this.packageInfo.package_option;
        },
        visible() {
            return this.packageInfo.package_id === this.active_package_id;
        },
        howToUse() {
            return this.packageInfo.how_to_use_render_obj;
        },
        confirmationDetail() {
            return this.packageInfo.policy_render_obj;
        },
        guideLines() {
            return this.packageInfo.guide_lines.confirmation_details;
        },
        multiLanguageIcons() {
            if (this.packageInfo.use_section) {
                return this.packageInfo.section_content.icons;
            }
            return this.packageInfo.multi_language_icons;
        },
    },

    mounted() {
        window.addEventListener('resize', this.handleCaculateStyle);
        this.caculateStyle();
    },

    methods: {
        handleCaculateStyle() {
            throttle(() => {
                this.visible && this.caculateStyle();
            }, 300)();
        },
        caculateStyle() {
            this.packageDetailStyle = {
                height: window.innerHeight - 110 - 80 + 'px',
            };
        },
    },
    beforeDestroy() {
        window.removeEventListener('resize', this.handleCaculateStyle);
    },
};
</script>

<style lang="scss">
.package-detail {
    background: #fff;
    width: 360px;
    min-height: 300px;
    padding: 0 20px;
    font-size: 14px;
    position: absolute;
    top: 0;
    left: 800px;
    z-index: 66;
    border: solid 1px #e0e0e0;
    border-radius: 2px;
    overflow: scroll;

    .h1 {
        font-weight: 600;
        color: #333;
        font-size: 20px;
        position: sticky;
        top: 0;
        background: #fff;
        padding: 16px 0;
    }
    .feature-detail {
        width: 100%;
        margin-bottom: 20px;
        border-radius: 2px;
        background-color: #eff9f8;
        padding: 10px;
        ul {
            li {
                font-size: 14px;
                color: #333;
                padding: 6px;
            }
        }
    }
    .description,
    .confirmation,
    .use-section,
    .how-to-use {
        width: 100%;
        margin-bottom: 30px;
        .h2 {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 10px;
        }
    }

    .images {
        margin-top: 10px;
    }
    .bottom {
        height: 20px;
        width: 100%;
        position: -webkit-sticky;
        position: sticky;
        bottom: 0;
        background: #fff;
    }

    .cut-off-time {
        .title {
            font-size: 18px;
            margin-bottom: 20px;
            font-weight: bold;
        }

        .time {
            padding-left: 10px;
        }
    }
}
</style>
