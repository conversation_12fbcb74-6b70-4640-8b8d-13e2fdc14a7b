<template>
    <div
        class="package-booking-detail-wrapper"
        ref="package-wrapper-ref"
        v-if="visible"
    >
        <div
            :class="classes"
            ref="package-ref"
            :style="packageFixedStyleComputed"
        >
            <div class="amount">
                <span class="total"
                    >{{ currencySymbol }} {{ totalAmount | formatPrice }}</span
                >

                <klk-poptip
                    :offset="6"
                    trigger="click"
                    :max-height="300"
                    :placement="packagePlacement"
                >
                    <span style="display:flex;align-items:center;">
                        <a href="javascript:void(0)" style="color:#666">
                            {{
                                $t('activity.v2.label.summary.bookding_details')
                            }}
                        </a>
                        <klk-icon
                            color="#666"
                            style="margin-left:12px"
                            type="icon_navigation_chevron_down"
                            size="14"
                        ></klk-icon>
                    </span>
                    <template slot="content">
                        <package-booking-detail-pop
                            :package_id="package_id"
                        ></package-booking-detail-pop>
                    </template>
                </klk-poptip>
            </div>
            <div class="operation" v-if="isCanAddCart">
                <span
                    class="add-card"
                    @click="addCart"
                    v-loading="cartAdding"
                    >{{ $t('activity.v2.btn.summary.add_to_cart') }}</span
                >
                <div class="goto-edit-selection" @click="scrollTopEdit">
                    <klk-icon
                        type="icon_edit_edit"
                        size="24"
                        style="margin-right:8px"
                        color="var(--primary-color)"
                    ></klk-icon>
                    {{ $t('edited_selections') }}
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import { mapState } from 'vuex';
import urlObj from '@/common/url';
import throttle from 'lodash/throttle';
import { UtilMixin, offset } from '../util';
import PackageBookingDetailPop from './package-booking-detail-pop.vue';

const prefixCls = 'package-booking-detail';

export default {
    name: 'PackageBookingDetail',
    mixins: [UtilMixin],
    components: {
        PackageBookingDetailPop,
    },
    props: {
        package_id: {
            type: Number,
        },
    },

    data() {
        return {
            cartAdding: false,
            positionFixed: false,
            packageFixedStyle: {},
        };
    },

    computed: {
        ...mapState('activity', [
            'activityInfo',
            'packageBookingInfoMap',
            'active_package_id',
        ]),

        classes() {
            return [
                prefixCls,
                {
                    [`${prefixCls}-fixed`]: this.positionFixed,
                },
            ];
        },

        packageFixedStyleComputed() {
            return this.visible && this.positionFixed
                ? this.packageFixedStyle
                : {};
        },

        currencySymbol() {
            return this.getCurrencySymbolByCurrencyCode(
                this.activityInfo.currency,
            );
        },

        permissions() {
            return this.activityInfo.permissions;
        },
        totalAmount() {
            let selectedPackage = this.packageBookingInfoMap[this.package_id];

            return (selectedPackage.selectedUnits || []).reduce(
                (accumulator, curValue) => {
                    accumulator = klook.accAdd(
                        accumulator,
                        klook.accMul(curValue.price, curValue.quality),
                    );
                    return accumulator;
                },
                0,
            );
        },
        isCanAddCart() {
            // SS-833 增加限制
            return (this.permissions || {}).add_to_shoppingcart === false
                ? false
                : true;
        },
        visible() {
            let selectedPackage = this.packageBookingInfoMap[this.package_id];
            return selectedPackage
                ? selectedPackage.selectedUnits.length > 0
                : false;
        },
        packagePlacement() {
            if (this.positionFixed && this.visible) {
                return 'top';
            }
            return 'bottom';
        },
    },

    mounted() {
        window.addEventListener('scroll', this.handleTransferBookingPosition);
        window.addEventListener('resize', this.handleCaculateFixedPosition);
        this.caculateFixedPosition();
    },

    methods: {
        handleTransferBookingPosition() {
            throttle(() => {
                this.visible && this.transferBookingPosition();
            }, 600)();
        },
        handleCaculateFixedPosition() {
            throttle(() => {
                this.visible && this.caculateFixedPosition();
            }, 300)();
        },
        caculateFixedPosition() {
            let paddingLeft = (parseInt(window.innerWidth) - 1160) / 2;
            let paddingRight = paddingLeft + 1160 - 760;

            this.packageFixedStyle = Object.assign(
                {},
                {
                    'padding-left': paddingLeft + 'px',
                    'padding-right': paddingRight + 'px',
                },
            );
        },

        scrollTopEdit() {
            window.scrollTo({
                top: offset(this.$parent.$el).top - 60 - 50 - 10, // 110 为详情页中搜索header+详情pin住header的高度
                behavior: 'smooth',
            });
        },
        transferBookingPosition() {
            if (!this.$refs['package-wrapper-ref']) return;
            let rect = this.$refs[
                'package-wrapper-ref'
            ].getBoundingClientRect();
            if (rect.top <= 50 && this.package_id === this.active_package_id) {
                this.positionFixed = true;
            } else {
                this.positionFixed = false;
            }
        },
        addCart() {
            if (this.cartAdding) {
                return;
            }
            this.cartAdding = true;

            let selectedPackage = this.packageBookingInfoMap[this.package_id];
            const postdata = {
                arrangement_id: (selectedPackage.selectedUnits[0] || {})
                    .arrangement_id,
                price_items: selectedPackage.selectedUnits.map(item => {
                    return { count: item.quality, sku_id: item.sku_id };
                }),
            };
            klook.ajaxPostJSON(urlObj.add_shopping_cart, postdata, res => {
                if (res.success) {
                    document.getElementById('shopping_cart_trigger').click();
                } else {
                    window.alert(res.desc || (res.error && res.error.message));
                }
                this.cartAdding = false;
            });
        },
    },
    beforeDestroy() {
        window.removeEventListener(
            'scroll',
            this.handleTransferBookingPosition,
        );
        window.removeEventListener('resize', this.handleCaculateFixedPosition);
    },
};
</script>
<style lang="scss">
.package-booking-detail-wrapper {
    min-height: 64px;
    .package-booking-detail {
        width: 100%;
        display: flex;
        justify-content: space-between;
        border-top: 1px solid #fff;
        padding-top: 20px;
        .goto-edit-selection {
            display: none;
        }
        &.package-booking-detail-fixed {
            position: fixed;
            background: #fff;
            z-index: 9999;
            bottom: 0;
            left: 0;
            padding: 20px 0;
            box-shadow: 0 -4px 12px 0 rgba(0, 0, 0, 0.12);
            align-items: center;
            .operation {
                position: relative;
                display: flex;
                align-items: center;

                .goto-edit-selection {
                    display: flex;
                    justify-content: center;
                    position: absolute;
                    right: -178px;
                    color: var(--primary-color);
                    font-size: 16px;
                    align-items: center;
                    cursor: pointer;
                }
            }
        }

        .add-card {
            display: inline-block;
            width: 100%;
            height: 48px;
            line-height: 48px;
            padding: 0 40px;
            border-radius: 2px;
            font-size: 20px;
            font-weight: 600;
            color: #fff;
            text-align: center;
            cursor: pointer;
            background: #ffa628;

            &:hover {
                background: #ff9b20;
            }
        }

        .klk-poptip-popper-inner {
            box-shadow: 0 5px 30px rgba(0, 0, 0, 0.1);
            border-radius: 2px;
        }

        .amount {
            display: flex;
            flex-direction: column;

            color: #888;

            .total {
                font-size: 20px;
                line-height: 28px;
                color: #ff5722;
                font-weight: 500;
            }
        }
    }
}
</style>
