<template>
    <ul class="feature-icons">
        <li v-for="(item, index) in icons" :key="index">
            <img v-if="item.icon_url" class="icon" :src="item.icon_url" />
            <klk-icon v-else :type="item.icon" :size="20"></klk-icon>
            <span>{{ item.label }}</span>
        </li>
    </ul>
</template>
<script>
export default {
    name: 'FeatureIcons',
    props: {
        multi_language_icons: {
            type: Array,
            default: () => [],
        },
    },
    data() {
        return {
            icons: [],
        };
    },
    watch: {
        multi_language_icons: {
            handler: function(newVal) {
                this.icons = (newVal || []).map(iconInfo => ({
                    label: iconInfo.icon_desc,
                    icon: iconInfo.icon_key,
                    icon_url: iconInfo.icon_url,
                }));
            },
            immediate: true,
        },
    },
};
</script>
<style lang="scss">
ul.feature-icons {
    li {
        display: flex;
        align-items: center;
        svg {
            margin-right: 12px;
        }
    }
    .icon {
        display: inline-block;
        width: 24px;
        height: 24px;
        margin-right: 16px;
    }
}
</style>
