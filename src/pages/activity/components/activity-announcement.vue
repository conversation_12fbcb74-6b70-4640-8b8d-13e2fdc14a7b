<template>
    <div class="announcement">
        <div
            class="intro-announcement"
            v-if="klook_insider"
            @click="showDetail = true"
        >
            <klk-icon type="highlight-color" :size="25"></klk-icon>
            <p class="announcement-content">{{ klook_insider }}</p>
            <klk-icon
                class="announcement-icon"
                type="icon_navigation_chevron_right_xxs"
                :size="20"
            ></klk-icon>
        </div>
        <klk-modal
            :open.sync="showDetail"
            :show-cancel-button="false"
            @on-confirm="showDetail = false"
        >
            {{ klook_insider }}
        </klk-modal>
    </div>
</template>

<script>
export default {
    name: 'announcementComponent',
    props: {
        klook_insider: {
            type: String,
        },
    },
    data() {
        return {
            showDetail: false,
        };
    },
};
</script>

<style lang="scss">
.intro-announcement {
    max-width: 100%;
    background-color: #fff4ed;
    border-radius: 2px;
    margin: 10px 0;
    padding: 10px;
    display: flex;
    align-items: center;
    cursor: pointer;
    color: #ff9d26;
    position: relative;

    .announcement-content {
        padding-right: 15px;
        overflow: hidden;
        white-space: nowrap;
        padding-left: 5px;
        max-width: 883px;
        text-overflow: ellipsis;
    }
    .announcement-icon {
        position: absolute;
        right: 5px;
    }
}
</style>
