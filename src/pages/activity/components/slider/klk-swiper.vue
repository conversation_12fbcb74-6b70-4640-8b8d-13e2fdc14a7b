<template>
    <div :class="classes">
        <div @click="goLeft()" v-show="!disableLeft" :class="classLeft">
            <slot name="left">
                <div class="controller-default-slot">
                    <klk-icon
                        type="icon_navigation_chevron_left"
                        :size="14"
                    ></klk-icon>
                </div>
            </slot>
        </div>
        <div class="swiper-inner">
            <div
                class="slide-wrapper"
                ref="klk-swiper-ref"
                :style="{
                    transform: `translate(${currentOffsetX}px,0)`,
                }"
            >
                <slot></slot>
            </div>
        </div>
        <div @click="goRight()" v-show="!disableRight" :class="classRight">
            <slot name="right">
                <div class="controller-default-slot">
                    <klk-icon
                        type="icon_navigation_chevron_right"
                        :size="14"
                    ></klk-icon>
                </div>
            </slot>
        </div>
    </div>
</template>

<script>
const prefixCls = 'klk-swiper';
export default {
    name: 'KlkSwiper',
    props: {
        stepLength: {
            type: Number,
            default: () => 224,
        },
        touch: {
            type: Boolean,
            default: () => false,
        },
        slideLength: {
            type: Number,
            default: () => 100,
        },
        slideGap: {
            type: Number,
            default: () => 12,
        },
    },
    data() {
        return {
            currentOffsetX: 0,
        };
    },
    computed: {
        classLeft() {
            return [
                'left-controller',
                {
                    'left-controller-disabled': this.disableLeft,
                },
            ];
        },
        classRight() {
            return [
                'right-controller',
                {
                    'right-controller-disabled': this.disableRight,
                },
            ];
        },
        classes() {
            return [
                prefixCls,
                {
                    [`${prefixCls}-touch`]: this.touch,
                },
            ];
        },
        disableRight() {
            if (this.touch) {
                return true;
            }
            /*
             * last step present in view will freeze goRight ,
             * more than 0 means all slides length less than each stepLength
             */
            if (this.boundry > 0 || this.currentOffsetX === this.boundry) {
                return true;
            }

            return false;
        },
        disableLeft() {
            if (this.touch) {
                return true;
            }

            if (this.boundry > 0 || this.currentOffsetX === 0) {
                return true;
            }

            return false;
        },
        boundry() {
            let boundry = this.stepLength - this.maxOffSetX;
            return boundry;
        },

        maxOffSetX() {
            return (
                this.getSwiperSlideNumber() *
                    (this.slideLength + this.slideGap) -
                this.slideGap
            );
            // could not get proper width because slot mounted is not before this.mounted as expected
            // let rect = this.$refs['klk-swiper-ref'].getBoundingClientRect();
            // return parseInt(rect.right) - parseInt(rect.left);
        },
    },
    methods: {
        getSwiperSlideNumber() {
            return this.$slots.default.filter(
                slot =>
                    slot.tag.match(/^vue-component-\d+-KlkSwiperSlide/) !==
                    null,
            ).length;
        },
        goLeft() {
            if (this.disableLeft) {
                return;
            }

            let afterMoveOffSet = this.currentOffsetX + this.stepLength;

            this.currentOffsetX = afterMoveOffSet >= 0 ? 0 : afterMoveOffSet;
        },
        goRight() {
            if (this.disableRight) {
                return;
            }

            let afterMoveOffSet = this.currentOffsetX - this.stepLength;

            this.currentOffsetX =
                afterMoveOffSet <= this.boundry
                    ? this.boundry
                    : afterMoveOffSet;
        },
    },
};
</script>
<style lang="scss">
.klk-swiper {
    position: relative;
    width: 100%;
    &.klk-swiper-touch {
        .swiper-inner {
            overflow: scroll;
        }
        .left-controller,
        .right-controller {
            display: none;
        }
    }

    .left-controller-disabled,
    .right-controller-disabled {
        &.left-controller,
        &.right-controller {
            .controller-default-slot {
                background: #cccccc;
                color: #666666;
                cursor: not-allowed;
            }
        }
    }
    .left-controller,
    .right-controller {
        position: absolute;
        z-index: 1;
        cursor: pointer;
    }
    .left-controller {
        top: 50%;
        left: 0;
        transform: translate(-50%, -50%);
    }
    .right-controller {
        top: 50%;
        right: 0;
        transform: translate(50%, -50%);
    }
    .controller-default-slot {
        width: 37px;
        height: 37px;
        background: #fff;
        border-radius: 100px;
        box-shadow: 0 1px 6px 0 rgba(0, 0, 0, 0.2);
        display: flex;
        justify-content: center;
        align-items: center;
    }
    .swiper-inner {
        overflow: hidden;
        width: 100%;
        height: 100%;
        .slide-wrapper {
            display: inline-flex;
            width: auto;
            transition: all 0.6s ease-in-out;
            transition-property: transform;
        }
        .klk-swiper-slide {
            flex-shrink: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            overflow: hidden;
            &:last-child {
                margin-right: 0;
            }
        }
    }
}
</style>
