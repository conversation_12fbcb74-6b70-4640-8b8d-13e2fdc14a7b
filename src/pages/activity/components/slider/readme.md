### How to use

```html
<klk-swiper :stepLength="224">
    <klk-swiper-slide
        class="swiper-slide"
        v-for="(image, index) in images"
        :key="index"
    >
        <img
            width="100%"
            :src="image.image_url_host"
            :alt="image.image_alt"
        />
    </klk-swiper-slide>
</klk-swiper>
```

```javascript
import {KlkSwiper,KlkSwiperSlide} from 'klk-swiper'
export default {
    components:{
        KlkSwiper,
        KlkSwiperSlide
    },
    data() {
        return {
            images: [
                {
                    image_url: "w9t4nuv5r9ka8rfdmeul.jpg",
                    image_alt: "",
                    image_desc: "自助取票機首頁，選擇取票開始操作",
                    width: 1632,
                    height: 1224,
                    image_url_host:
                        "https://res.klook.com/image/upload/activities/w9t4nuv5r9ka8rfdmeul.jpg"
                },
                {
                    image_url: "mrxw4kssulkp6dqvp1ih.jpg",
                    image_alt: "",
                    image_desc: "請詳閱閱讀取票須知",
                    width: 1632,
                    height: 1224,
                    image_url_host:
                        "https://res.klook.com/image/upload/activities/mrxw4kssulkp6dqvp1ih.jpg"
                }
            ]
        };
    }
};
```

## API

| property   | type   | default | description |
| ---------  | ---------  | ------------ | --------|
| stepLength | Number |     224 |   the length of each click move when touch is false |
| slideLength | Number |     100 |   the size of each slider |
| slideGap | Number |     12 |   the length of slider gap |
| touch      | Boolean |   false |  whether enable touch |