<template>
  <div class="klk-swiper-slide" :style="style">
    <slot></slot>
  </div>
</template>

<script>
export default {
  name: 'KlkSwiperSlide',
  data(){
    return {
      slideLength : 100,
      slideGap : 12
    }
  },
  computed:{
    style(){
      return {
        width : this.slideLength+'px',
        height : this.slideLength+ 'px',
        'margin-right' : this.slideGap+'px'
      }
    }
  },
  mounted(){
      this.slideLength = this.$parent.slideLength;
      this.slideGap = this.$parent.slideGap;
  }
}
</script>
<style lang="scss">
</style>

