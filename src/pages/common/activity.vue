<template>
    <div class="activity" v-loading="loading">
        <component
            v-if="componentName"
            :is="componentName"
            @load-activity-info="loadActivityInfo"
            :activity-info="activityInfo"
        ></component>
    </div>
</template>

<script>
import urlObj from '@/common/url';
import { mapActions } from 'vuex';
import ActivityIndex from '../activity/index.vue';
import TicketActivityIndex from '../ticket/activity/index.vue';
import { countOriginalSellPrice } from '../activity/util';
import AidActivityIndex from '../ticket/aidActivity/index.vue';

export default {
    data() {
        return {
            loading: true,
            activityInfo: {},
            componentName: '',
        };
    },
    methods: {
        ...mapActions({
            checkActivityVersion: 'ticket/activity/checkActivityVersion',
        }),

        // 主方法：协调新旧活动逻辑
        loadActivityInfo() {
            this.componentName = '';
            this.loading = true;

            const activityId = this.$route.params.id;

            // 先检查活动版本
            this.checkActivityVersion(activityId)
                .then(isNewActivity => {
                    if (isNewActivity === 1) {
                        // 加载新活动
                        return this.loadNewActivity(activityId);
                    } else {
                        // 加载旧活动
                        return this.loadOldActivity(activityId);
                    }
                })
                .catch(error => {
                    this.handleError(error);
                });
        },

        // 加载新活动
        async loadNewActivity(activityId) {
            try {
                const activityData = await this.getNewActivityDetail(
                    activityId,
                );
                this.activityInfo = activityData;
                this.loading = false;
                this.componentName = AidActivityIndex;
                return this.activityInfo;
            } catch (error) {
                // 不在这里调用 handleError，让上层统一处理
                throw error;
            }
        },

        // 获取新活动详情数据
        getNewActivityDetail(activity_id) {
            return new Promise((resolve, reject) => {
                klook.ajaxGet(
                    urlObj.get_activity_detail_url_by_id2(),
                    {
                        activity_id,
                    },
                    res => {
                        if (res.success && res.result) {
                            resolve(res.result);
                        } else {
                            const errorMsg =
                                res.error &&
                                (res.error.code === '050001'
                                    ? this.$t('activity_detail_not_exist')
                                    : res.error.message);
                            reject(new Error(errorMsg || '获取活动详情失败'));
                        }
                    },
                );
            });
        },
        // 加载旧活动
        loadOldActivity(activityId) {
            return new Promise((resolve, reject) => {
                klook.ajaxGet(
                    urlObj.get_activity_detail_url_by_id(activityId),
                    {},
                    res => {
                        if (res.success && res.result) {
                            if (res.result.is_blocked) {
                                this.$router.push({ name: '404' });
                                return;
                            }

                            this.activityInfo = res.result;

                            if (
                                this.activityInfo &&
                                this.activityInfo.is_promotion
                            ) {
                                this.activityInfo.market_price = countOriginalSellPrice(
                                    this.activityInfo,
                                );
                            }

                            this.loading = false;
                            this.componentName =
                                this.activityInfo.amp_order_process === 1
                                    ? TicketActivityIndex
                                    : ActivityIndex;
                            resolve(this.activityInfo);
                        } else {
                            const errorMsg =
                                res.error &&
                                (res.error.code === '050001'
                                    ? this.$t('activity_detail_not_exist')
                                    : res.error.message);
                            // this.handleError(
                            //     new Error(errorMsg || '获取活动详情失败'),
                            // );
                            reject(new Error(errorMsg || '获取活动详情失败'));
                        }
                    },
                );
            });
        },

        // 错误处理方法
        handleError(error) {
            console.error('Activity loading error:', error);
            this.loading = false;

            const errorMessage = error.message || '加载活动信息失败';
            this.$alert(errorMessage).then(() => {
                this.$router.push({ name: 'agent_index' });
            });
        },
    },
    created() {
        this.loadActivityInfo();
        this.$watch(
            () => this.$route.params,
            () => {
                this.loadActivityInfo();
            },
        );
    },
};
</script>
