<template lang="html">
    <div class="agent_user_center main_center">
        <div
            class="left_view"
            v-sucktop="{
                suck_top: 90,
                normal_top: 40,
                position: 'left',
                reference: '#right_view',
            }"
        >
            <ul class="menu">
                <li v-for="(value, key) in navItems" :key="key">
                    <router-link
                        :to="{ name: key }"
                        active-class="active_router_link"
                    >
                        <klk-icon
                            size="20"
                            :type="value.icon"
                            class="agent_icon"
                        ></klk-icon>
                        <span class="icon_related_text">{{
                            $t(value.translateKey)
                        }}</span>
                    </router-link>
                </li>
                <li class="js_agent_log_out">
                    <a href="javascript:void(0)" @click="logOut">
                        <klk-icon
                            size="20"
                            type="login-out"
                            class="agent_icon"
                        ></klk-icon>
                        <span class="icon_related_text">{{
                            $t('log_out')
                        }}</span>
                    </a>
                </li>
                <li>
                    <a :href="'mailto:' + jv_email">
                        <klk-icon
                            size="20"
                            type="icon_communication_email"
                            class="agent_icon"
                        ></klk-icon>
                        <span class="icon_related_text">{{
                            $t('for_enquiry')
                        }}</span>
                    </a>
                </li>
                <li>
                    <a :href="feedbackUrl">
                        <klk-icon
                            size="20"
                            type="icon_feedback_warning"
                            class="agent_icon"
                        ></klk-icon>
                        <span class="icon_related_text">{{
                            $t('feedback')
                        }}</span>
                    </a>
                </li>
            </ul>
        </div>
        <transition name="fade" mode="out-in">
            <router-view class="right_view" id="right_view"></router-view>
        </transition>
    </div>
</template>

<script>
import sucktop from '@/directives/sucktop';

export default {
    name: 'AgentUserCenter',
    data() {
        return {};
    },
    directives: { sucktop },
    computed: {
        feedbackUrl() {
            if (window.KLK_LANG === 'zh-CN') {
                return 'https://www.klook.cn/zh-CN/tetris/survey/agent_marketplace_feedback/';
            }
            return 'https://www.klook.com/tetris/survey/agent_marketplace_feedback/';
        },
        navItems() {
            const items = {
                home: { icon: 'icon_user_avatar', translateKey: 'my_account' },
                voucher_info: {
                    icon: 'voucher',
                    translateKey: 'voucher_details',
                },
                credits: { icon: 'balance', translateKey: 'balance' },
                bookings: { icon: 'bookings', translateKey: 'bookings' },

                collection: {
                    icon: 'icon_social_favorite',
                    translateKey: 'header.WishList',
                },
            };

            // history booking list
            if (klook.showHistoryList) {
                items['history_bookings'] = {
                    icon: 'bookings',
                    translateKey: 'history_bookings',
                };
            }
            //0 母账号 ，1 子账号
            if (window.KLK_USER_INFO.agent_type === 0) {
                items['account_setting'] = {
                    icon: 'setting',
                    translateKey: 'settings',
                };
                items['sub_account'] = {
                    icon: 'sub-account',
                    translateKey: 'sub_accounts',
                };
            }
            if ((window.KLK_USER_INFO.permissions || {}).batch_order) {
                items['batch_order'] = {
                    icon: 'batch-order',
                    translateKey: 'bulk_buy',
                };
            }
            if ((window.KLK_USER_INFO.permissions || {}).mashang) {
                items['tmall_bookings'] = {
                    icon: 'taobao',
                    translateKey: 'tmall_bookings',
                };
            }
            return items;
        },
        jv_email() {
            // return window.KLK_JOINT_VENTRUES.contact_email || '';
            return '<EMAIL>';
        },
    },
    methods: {
        logOut() {
            const logoutEls = document.querySelectorAll('.klk-icon-login-out');
            if (logoutEls && logoutEls[0]) {
                logoutEls[0].parentNode.click();
            }
        },
    },
};
</script>

<style lang="scss">
.fade-enter-active,
.fade-leave-active {
    transition: all 0.2s ease;
}

.fade-enter,
.fade-leave-active {
    opacity: 0;
}
.main_center {
    border-radius: 2px;
    min-height: 800px;
    width: 1160px;
    margin: 0 auto;
    // display: flex;
    margin-top: 30px;
    margin-bottom: 60px;
}
.agent_user_center {
    overflow: hidden;

    .left_view {
        float: left;
        width: 218px;
        .menu {
            height: auto;
            border-radius: 2px;
            font-size: 16px;
            padding: 16px 24px;
            background-color: #fff;
            li {
                width: 100%;
                margin-bottom: 8px;
                display: table;
                line-height: 17px;
                white-space: normal;
                .agent_icon,
                .icon_related_text {
                    vertical-align: middle;
                    color: #333;
                    fill: #333;
                    display: inline;
                }
                .agent_icon {
                    font-size: 24px;
                    margin-right: 16px;
                }
                a {
                    display: inline-block;
                    width: 100%;
                    height: 40px;
                    line-height: 40px;
                }
                &:hover {
                    a,
                    i,
                    .icon_related_text {
                        color: var(--primary-color);
                    }

                    svg {
                        color: var(--primary-color);
                        fill: var(--primary-color);
                    }
                }
            }
            .active_router_link {
                a,
                i,
                span {
                    color: var(--primary-color);
                }

                svg {
                    fill: var(--primary-color);
                }
            }
        }
        .contact_us {
            display: flex;
            justify-content: center;
            align-items: center;

            width: 162.8px;
            height: 42px;
            line-height: 42px;
            text-align: center;
            border-radius: 4px;
            background-color: #ffffff;
            border: solid 1px #d1d1d1;
            margin: 32px 27px 0 27px;
            cursor: pointer;

            .icon_related_text {
                font-size: 16px;
            }
        }
        li[linkto='collection'] {
            padding-left: 5px;
        }
        li[linkto='batch_order'] {
            padding-left: 0;
            margin-left: -2px;
        }
    }
    .right_view {
        display: table-cell;
        overflow-y: scroll;
        padding: 30px;
        float: right;
        width: 922px;
        min-height: 800px;
        margin-left: 20px;
        background-color: #fff;
        padding: 24px 30px 24px 30px;
    }
}
</style>
