<template lang="html">
    <div class="my_account" v-loading="loading">
        <div class="title">
            {{ $t('my_account') }}
            <klk-button
                size="small"
                type="secondary"
                class="toggle_eye_icon"
                @click="toggleLoadAccountInfo"
                >{{ toggleText }}</klk-button
            >
        </div>
        <klk-form inline class="account-form">
            <klk-form-item :label="$t('title')">
                <klk-select v-model="title" :disabled="true">
                    <klk-option
                        v-for="item in ['MR', 'MRS', 'MISS']"
                        :key="item"
                        :value="item"
                        :label="item"
                    ></klk-option>
                </klk-select>
            </klk-form-item>
            <klk-form-item :label="$t('first_name')" class="width-200">
                <klk-input :disabled="true" v-model="firstName"> </klk-input>
            </klk-form-item>
            <klk-form-item :label="$t('last_name')" class="width-200">
                <klk-input :disabled="true" v-model="lastName"> </klk-input>
            </klk-form-item>
            <klk-form-item :label="$t('email_address')" class="width-200">
                <klk-input :disabled="true" v-model="email"> </klk-input>
            </klk-form-item>
            <klk-form-item :label="$t('company_name')" class="width-250">
                <klk-input :disabled="true" v-model="companyName"> </klk-input>
            </klk-form-item>
            <klk-form-item
                :label="$t('country_or_regional_code')"
                class="width-250"
            >
                <klk-input :disabled="true" v-model="countryCode"> </klk-input>
            </klk-form-item>
            <klk-form-item :label="$t('phone_number')" class="width-250">
                <klk-input :disabled="true" v-model="phoneNumber"> </klk-input>
            </klk-form-item>
            <klk-form-item :label="$t('language')" class="width-250">
                <klk-select v-model="language" filterable>
                    <klk-option
                        v-for="(item, key) in langOptions"
                        :key="key"
                        :value="item.iso.replace('-', '_')"
                        :label="$t(`global.${key}`)"
                    ></klk-option>
                </klk-select>
            </klk-form-item>
            <klk-form-item :label="$t('currency')" class="width-250">
                <klk-select
                    v-model="currencyCode"
                    class="is_disabled"
                    :disabled="true"
                >
                    <klk-option
                        v-for="item in currencyList"
                        :key="item.value"
                        :value="item.value"
                        :label="item.title"
                    ></klk-option>
                </klk-select>
            </klk-form-item>
            <br />
            <klk-form-item :label="$t('address')" class="width-520">
                <klk-input
                    :maxlength="256"
                    :disabled="!is_master_agent"
                    v-model="address"
                >
                </klk-input>
            </klk-form-item>
            <klk-form-item :label="$t('website_url')" class="width-520">
                <klk-input
                    :maxlength="256"
                    :disabled="!is_master_agent"
                    v-model="websiteUrl"
                >
                </klk-input>
            </klk-form-item>
            <klk-form-item :label="$t('website_name')" class="width-520">
                <klk-input
                    :maxlength="256"
                    :disabled="!is_master_agent"
                    v-model="websiteName"
                />
            </klk-form-item>
            <klk-form-item
                class="width-520"
                :label="$t('top_up_account')"
                v-if="currencyCode === 'CNY' && cmb_virtual_account_id"
            >
                <span>{{ cmb_virtual_account_id }}</span>
            </klk-form-item>
            <br />
            <klk-form-item>
                <klk-button @click="submitForm">
                    {{ $t('save') }}
                </klk-button>
            </klk-form-item>
        </klk-form>
    </div>
</template>

<script>
import urlObj from '@/common/url';

export default {
    name: 'Home',
    data() {
        return {
            loading: false,
            is_master_agent: !window.KLK_USER_INFO.agent_type, //0 master 1 SubAccount
            title: '',
            firstName: '',
            lastName: '',
            email: '',
            companyName: '',
            countryCode: '',
            phoneNumber: '',
            language: '',
            currencyCode: '',
            address: '',
            websiteName: '',
            websiteUrl: '',
            dialog_error_tip: '',
            dialog_success_tip: '',
            cmb_virtual_account_id: '',
            is_encryption: true,
        };
    },
    computed: {
        toggleText() {
            return this.is_encryption
                ? this.$t('show_all_texts')
                : this.$t('hide_partial_texts');
        },
    },
    mounted() {
        this.loadAccountInfo();
    },
    methods: {
        toggleLoadAccountInfo() {
            this.is_encryption = !this.is_encryption;
            this.loadAccountInfo();
        },
        showDialogSuccess(tip) {
            this.$message({
                type: 'success',
                message: tip || 'success',
            });
        },
        showDialogFail(tip) {
            this.$message({
                type: 'error',
                message: tip || 'error',
            });
        },
        loadAccountInfo() {
            // this.loading = true;
            const url = this.is_encryption
                ? urlObj.agent_account_info
                : `${urlObj.agent_account_info}?show_original=1`;
            klook
                .ajaxGet(url, resp => {
                    if (resp.success) {
                        const result = resp.result || {};
                        [
                            'title',
                            'firstName',
                            'lastName',
                            'email',
                            'companyName',
                            'countryCode',
                            'phoneNumber',
                            'language',
                            'currencyCode',
                            'address',
                            'companyName',
                            'websiteName',
                            'websiteUrl',
                            'cmb_virtual_account_id',
                        ].forEach(key => {
                            this[key] = result[key];
                        });
                    }
                })
                .catch(error => {
                    console.log(error.message);
                })
                .finally(() => {
                    // this.loading = false;
                });
        },
        submitForm() {
            this.loading = true;
            this.updateAccountInfo();
        },
        updateAccountInfo() {
            klook.ajaxPostJSON(
                urlObj.agent_account_info,
                {
                    language: this.language,
                    address: this.address,
                    websiteUrl: this.websiteUrl,
                    websiteName: this.websiteName,
                },
                resp => {
                    this.loading = false;
                    if (resp.success) {
                        this.showDialogSuccess(this.$t('success'));
                    } else {
                        this.showDialogFail(resp.error && resp.error.message);
                    }
                },
            );
        },
    },
};
</script>

<style lang="scss">
.klk-form-inline .klk-form-item:not(:last-child) {
    margin-right: 24px;
}
.my_account {
    .title {
        width: 100%;
        height: 52px;
        font-size: 24px;
        color: #333333;
        border-bottom: solid 1px #e0e0e0;
        display: flex;
        align-items: flex-start;
    }

    .account-form {
        padding: 30px 0;
    }
    .toggle_eye_icon {
        margin-left: 24px;
        border-color: #999;
        color: #666;
    }
}
</style>
