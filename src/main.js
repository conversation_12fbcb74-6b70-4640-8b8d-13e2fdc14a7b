import Vue from 'vue';
import i18n from '@/i18n';
import loading from '@/directives/loading';
import App from './App.vue';
import router from './router';
import store from './store';
import { AllCurrencies, locales, supportLocales } from '../config';
import '@/components/index';
import '@/assets/scss/agent.scss';
// 引入自定义主题样式
import './theme.scss';
import '@/klookUI';

Vue.config.errorHandler = (err, vm, info) => {
    klook.logger.handlerError(
        { hook: info, stack: err.stack.toString() },
        { type: 'vue' },
    );
};
const svgFiles = require.context('@/assets/imgs/font-icon-svg', true, /.svg$/);
svgFiles.keys().map(svgFiles);

Vue.config.productionTip = false;

Vue.mixin({
    methods: {
        getCurrencySymbolByCurrencyCode(code) {
            return code
                ? (this.currencyList.find(item => item.currency === code) || {})
                      .currencySymbol
                : '';
        },
        formatPriceThousands(price) {
            return klook.formatPriceThousands(price);
        },
    },
    computed: {
        currencyList() {
            return Object.values(AllCurrencies).map(item => ({
                value: item.currency,
                title: `${this.$t(item.currencyName)} | ${item.currencySymbol}`,
                currencySymbol: item.currencySymbol,
                currency: item.currency,
            }));
        },
        langOptions() {
            let langOptions = {};
            let localesCode = (
                window.KLK_JOINT_VENTRUES.lang_list || []
            ).map(item => item.code.replace('_', '-'));
            Object.keys(locales).forEach(key => {
                if (localesCode.includes(locales[key].iso)) {
                    langOptions[key] = locales[key];
                }
            });
            console.log('***langOptions***', langOptions);
            return langOptions;
        },
    },
});

Vue.directive('loading', loading);
const loadLanguageAsync = lang => {
    // 如果当前语言没有对应的json多语言文件，则使用en.json
    lang = supportLocales.hasOwnProperty(lang) ? lang : 'en';
    return import(
        /* webpackChunkName: "lang-[request]" */ `~/locales/${lang}.json`
    ).then(msg => {
        i18n.setLocaleMessage(lang, msg.default);
        i18n.locale = lang;
        document.querySelector('html').setAttribute('lang', lang);
        return Promise.resolve();
    });
};

loadLanguageAsync(window.KLK_LANG || 'en').then(() => {
    new Vue({
        router,
        i18n,
        store,
        render: h => h(App),
    }).$mount('#app');
});

/**
 * 处理同一个浏览器使用多个账号登录的信息与token同步问题
 */
if (window.localStorage && (window.KLK_USER_INFO || {}).id) {
    window.localStorage.setItem('klk_u', window.KLK_USER_INFO.id);
    window.addEventListener('storage', event => {
        if (
            event.key === 'klk_u' &&
            +event.newValue !== window.KLK_USER_INFO.id
        ) {
            window.location.reload(true);
        }
    });
}
