import Vue from 'vue';
import {
    Icon,
    Button,
    Input,
    Badge,
    Message,
    Modal,
    Pagination,
    Switch,
    Checkbox,
    Radio,
    Select,
    DatePicker,
    Poptip,
    Form,
    Table,
    Carousel,
    SectionTitle,
    Markdown,
    Divider,
    Toast,
    Drawer,
    Loading,
} from '@klook/klook-ui';
import locale from '@klook/klook-ui/lib/locale';

// 设置语言
import(
    `@klook/klook-ui/lib/locale/lang/${window.KLK_LANG}` /* webpackChunkName: "ui-lang-[request]" */
).then(lang => {
    locale.use(lang);
});

// //  klook-ui 主题
// const loadTheme = theme => {
//     import(`~/src/assets/scss/theme/${theme}.scss`);
// };
// loadTheme(window.KLK_JV_NAME || 'klook');

Message.setOffsetTop(100);
Vue.use(Icon);
Vue.use(Button);
Vue.use(Input);
Vue.use(Badge);
Vue.use(Message);
Vue.use(Modal);
Vue.use(Pagination);
Vue.use(Switch);
Vue.use(Checkbox);
Vue.use(Radio);
Vue.use(Select);
Vue.use(DatePicker);
Vue.use(Poptip);
Vue.use(Form);
Vue.use(Poptip);
Vue.use(Table);
Vue.use(Carousel);
Vue.use(SectionTitle);
Vue.use(Markdown);
Vue.use(Divider);
Vue.use(Toast);
Vue.use(Drawer);
Vue.use(Loading);
