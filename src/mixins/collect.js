import urlObj from '@/common/url';

export default {
    data() {
        return {
            isCollecting: false,
        };
    },
    methods: {
        collect() {
            // 兼容不同对象命名
            let activity = this.activity || this.activityInfo;
            let url = urlObj.add_activity_to_collection;

            if (this.isCollecting) return;
            if (activity.is_wish) {
                url = urlObj.remove_activity_from_collection;
            }
            this.isCollecting = true;
            klook.ajaxPost(url, { id: activity.id }, resp => {
                this.isCollecting = false;
                if (resp.success) {
                    this.$set(activity, 'is_wish', !activity.is_wish);
                    this.$emit('clickWish', !activity.is_wish);
                    this.$emit('change', !activity.is_wish);
                }
            });
        },
    },
};
