import Vue from 'vue';
import VueRouter from 'vue-router';
import { trackPageRenderSuccessOnce } from '@/common/optimus';

Vue.use(VueRouter);

const routes = [
    {
        path: '/signin',
        name: 'signin',
        component: () =>
            import(/* webpackChunkName:"login" */ './pages/login.vue'),
        beforeEnter: (to, from, next) => {
            if (klook.isLoggedIn()) {
                next({ name: 'agent_index' });
            } else {
                next();
            }
        },
    },
    {
        path: '/sam/voucher',
        name: 'samVoucher',
        component: () =>
            import(
                /* webpackChunkName:"samVoucher" */ './pages/sam_voucher.vue'
            ),
    },
    {
        path: '/index',
        name: 'agent_index',
        component: () =>
            import(
                /* webpackChunkName:"agent_index" */ './pages/home/<USER>'
            ),
    },
    {
        path: '/city/:city_id',
        name: 'city_activity',
        component: () =>
            import(
                /* webpackChunkName:"city_activity" */ './pages/city_activity.vue'
            ),
    },
    {
        path: '/activity/:id',
        name: 'activity_detail',
        component: () =>
            import(
                /* webpackChunkName:"activity_detail" */ './pages/common/activity.vue'
            ),
        props: route => {
            const matches = /^(\d+)-?.*$/.exec(route.params.id);
            return {
                id: matches ? parseInt(matches[1]) : 0,
            };
        },
    },
    {
        path: '/promotion/list',
        name: 'promotion_list',
        component: () =>
            import(
                /* webpackChunkName:"promotion_list" */ './pages/promotion/index.vue'
            ),
    },
    {
        path: '/pay',
        name: 'pay',
        component: () =>
            import(/* webpackChunkName:"pay" */ './pages/pay/index.vue'),
    },
    {
        path: '/pay_ticket/:package_id/:sku_id',
        name: 'payTicket',
        component: () =>
            import(
                /* webpackChunkName:"pay_ticket" */ './pages/ticket/pay/index.vue'
            ),
    },
    {
        path: '/pay/result',
        name: 'pay_result',
        component: () =>
            import(
                /* webpackChunkName:"pay_result" */ './pages/pay/PayResult.vue'
            ),
    },
    {
        path: '/pay/middleware',
        name: 'pay_middleware',
        component: () =>
            import(
                /* webpackChunkName:"pay_middleware" */ './pages/pay/PayMiddleware'
            ),
    },
    {
        path: '/pay/middleware/vertical',
        name: 'pay_middleware_vertical',
        component: () =>
            import(
                /* webpackChunkName:"pay_middleware_vertical" */ './pages/pay/PayVerticalMiddleware'
            ),
    },
    {
        path: '/forgetpwd',
        name: 'forgetpwd',
        component: () =>
            import(
                /* webpackChunkName:"forget_pwd" */ './pages/forget_pwd.vue'
            ),
    },
    {
        path: '/signup',
        component: () => import('./pages/signup.vue'),
        children: [
            // 注册分步骤
            {
                path: '', //步骤1
                name: 'signup',
                component: () =>
                    import(
                        /* webpackChunkName:"signup" */ './pages/signup/step1.vue'
                    ),
                meta: { step: 1 },
            },
            {
                path: 'step2', //步骤2
                name: 'signupStep2',
                component: () =>
                    import(
                        /* webpackChunkName:"signup" */ './pages/signup/step2.vue'
                    ),
                meta: { step: 2 },
            },
            {
                path: 'step3', //步骤3
                name: 'signupStep3',
                component: () =>
                    import(
                        /* webpackChunkName:"signup" */ './pages/signup/step3.vue'
                    ),
                meta: { step: 3 },
                beforeEnter: (to, from, next) => {
                    // 当前刷线当前页，关闭标签再打开需要跳到登录。
                    if (
                        sessionStorage &&
                        sessionStorage.getItem('_session_time')
                    ) {
                        next();
                    } else {
                        next({ name: 'signin' });
                    }
                },
            },
        ],
    },
    {
        path: '/reset/password',
        name: 'resetPassword',
        component: () =>
            import(
                /* webpackChunkName:"reset_password" */ './pages/ResetPassword.vue'
            ),
    },
    {
        path: '/search',
        name: 'search',
        component: () =>
            import(/* webpackChunkName:"search" */ './pages/search/index.vue'),
    },
    {
        path: '/auth/middleware',
        name: 'auth_middleware',
        component: () =>
            import(
                /* webpackChunkName:"auth_middleware" */ './pages/auth/index.vue'
            ),
    },
    {
        path: '/payment/batch/success',
        redirect: { name: 'pay_result' },
    },
    {
        path: '/',
        component: () =>
            import(
                /* webpackChunkName:"agent_user_center" */ './pages/agent_user_center.vue'
            ),
        children: [
            {
                path: 'home',
                name: 'home',
                component: () =>
                    import(/* webpackChunkName:"home" */ './pages/home.vue'),
            },
            {
                path: 'voucher_info',
                name: 'voucher_info',
                component: () =>
                    import(
                        /* webpackChunkName:"voucher_info" */ './pages/voucher_info.vue'
                    ),
            },
            {
                path: 'credits',
                name: 'credits',
                component: () =>
                    import(
                        /* webpackChunkName:"credits" */ './pages/credits.vue'
                    ),
            },
            {
                path: 'bookings',
                name: 'bookings',
                component: () =>
                    import(
                        /* webpackChunkName:"bookings" */ './pages/bookings/bookings.vue'
                    ),
            },
            {
                path: 'history_bookings',
                name: 'history_bookings',
                beforeEnter: (to, from, next) => {
                    if (klook.showHistoryList) {
                        next();
                    } else {
                        next({ name: 'agent_index' });
                    }
                },
                component: () =>
                    import(
                        /* webpackChunkName:"history-bookings" */ './pages/historyBookings/bookings.vue'
                    ),
            },
            {
                path: 'bookings_detail',
                name: 'bookings_detail',
                component: () =>
                    import(
                        /* webpackChunkName:"bookings-detail" */ './pages/bookings/detail-page.vue'
                    ),
            },
            {
                path: 'history_bookings_detail',
                name: 'history_bookings_detail',
                beforeEnter: (to, from, next) => {
                    if (klook.showHistoryList) {
                        next();
                    } else {
                        next({ name: 'agent_index' });
                    }
                },
                component: () =>
                    import(
                        /* webpackChunkName:"history-bookings-detail" */ './pages/historyBookings/detail-page.vue'
                    ),
            },
            {
                path: 'account_setting',
                name: 'account_setting',
                component: () =>
                    import(
                        /* webpackChunkName:"account_setting" */ './pages/account_setting.vue'
                    ),
                beforeEnter: (to, from, next) => {
                    if ((window.KLK_USER_INFO || {}).agent_type !== 0) {
                        next({ name: 'agent_index' });
                    } else {
                        next();
                    }
                },
            },
            {
                path: 'collection',
                name: 'collection',
                component: () =>
                    import(
                        /* webpackChunkName:"collection" */ './pages/collection.vue'
                    ),
            },
            {
                path: 'batch_order',
                name: 'batch_order',
                component: () =>
                    import(
                        /* webpackChunkName:"batch_order" */ './pages/batch_order.vue'
                    ),
                beforeEnter: (to, from, next) => {
                    if (!(window.KLK_USER_INFO.permissions || {}).batch_order) {
                        next({ name: 'agent_index' });
                    } else {
                        next();
                    }
                },
            },
            {
                path: 'sub_account',
                name: 'sub_account',
                component: () =>
                    import(
                        /* webpackChunkName:"sub_account" */ './pages/sub_account.vue'
                    ),
                beforeEnter: (to, from, next) => {
                    if ((window.KLK_USER_INFO || {}).agent_type !== 0) {
                        next({ name: 'agent_index' });
                    } else {
                        next();
                    }
                },
            },
            {
                path: 'tmall_bookings',
                name: 'tmall_bookings',
                component: () =>
                    import(
                        /* webpackChunkName:"tmall" */ './pages/tmall/index.vue'
                    ),
                beforeEnter: (to, from, next) => {
                    if (!(window.KLK_USER_INFO.permissions || {}).mashang) {
                        next({ name: 'agent_index' });
                    } else {
                        next();
                    }
                },
            },
        ],
    },
    {
        path: '/hotels',
        name: 'hotels',
        component: () =>
            import(/* webpackChunkName:"hotel" */ './pages/hotel/index.vue'),
        children: [
            {
                path: '/*',
                name: 'hotels',
                component: () =>
                    import(
                        /* webpackChunkName:"hotel" */ './pages/hotel/index.vue'
                    ),
            },
        ],
        beforeEnter: (to, from, next) => {
            // hotel仅中文
            if (klook.isCNSite && window.KLK_LANG !== 'zh-CN') {
                window.location.href = `/zh-CN${to.fullPath}`;
            } else {
                next();
            }
        },
    },
    {
        path: '*',
        name: '404',
        component: () => import(/* webpackChunkName:"404" */ './pages/404.vue'),
    },
];

const scrollBehavior = (to, from, savedPosition) => {
    return savedPosition || { x: 0, y: 0 };
};

const router = new VueRouter({
    base: window.KLK_LANG_PATH,
    scrollBehavior,
    routes,
    mode: 'history',
});

const whiteList = [
    'signin',
    'signup',
    'signupStep2',
    'signupStep3',
    'forgetpwd',
    'samVoucher',
    'resetPassword',
    '404',
    'agent_404',
    'pay_middleware',
];

const DRPBlackList = ['hotels', 'agent_index', 'search', 'city_activity'];

router.beforeEach((to, from, next) => {
    if (!klook.isLoggedIn() && whiteList.indexOf(to.name) === -1) {
        next({
            name: 'signin',
            query: {
                continue: encodeURIComponent(to.fullPath),
            },
        });
    } else {
        //专为DRP Agents做的处理
        if (DRPBlackList.indexOf(to.name) >= 0 && klook.isDRPAgent) {
            next(klook.DRPLandingPage);
        } else {
            next();
        }
    }
});

router.afterEach(to => {
    /**
     * 以下提条件可接入网易七鱼:
     * Agent Category = China GDS & Tmall GDS
     * AMP首页 AMP活动商品详情页 订单列表页 订单详情页 站内信
     */

    // 加上页面性能追踪
    trackPageRenderSuccessOnce(to.name);
});

// [cn拆站] - 移除云客服
// const addChatByCategory = name => {
//     const qiyuList = ['China', 'Tmall'];
//     if (qiyuList.includes(window.KLK_USER_INFO.agent_category)) {
//         let yfsDom = document.querySelector('#YSF-BTN-HOLDER');
//         if (yfsDom) {
//             document.body.removeChild(yfsDom);
//         }

//         if (addchatList.includes(name)) {
//             addChat();
//         }
//     }
// };

export default router;
