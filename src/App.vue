<template>
    <div id="app">
        <common-header></common-header>
        <router-view class="container"></router-view>
    </div>
</template>
<script>
import CommonHeader from '@/pages/tpl/header.vue';
import { PRIMARY_COLOR } from '~/jv_config';

export default {
    name: 'App',
    components: {
        CommonHeader,
    },
    data() {
        return {
            loadedDDjs: false,
        };
    },
    beforeCreate() {
        // 注册、登录、忘记密码和重设密码 引入反爬SDK
        const pathName = window.location.pathname;
        if (
            pathName.includes('signin') ||
            pathName.includes('signup') ||
            pathName.includes('forgetpwd') ||
            pathName.includes('reset/password')
        ) {
            const loadDDjs = () => {
                !(function(a, b, c, d, e, f) {
                    a.ddjskey = e;
                    a.ddoptions = f || null;
                    let m = b.createElement(c),
                        n = b.getElementsByTagName(c)[0];
                    (m.async = 1), (m.src = d), n.parentNode.insertBefore(m, n);
                })(
                    window,
                    document,
                    'script',
                    'https://js.datadome.co/tags.js',
                    '1A2CDFCDF412CD9D3C93BB2E078906',
                    { ajaxListenerPath: true },
                );
            };
            loadDDjs();
        }
        klook.loadScript('https://at.alicdn.com/t/font_2140432_u60nr165yl.js'); // AMP SVG
        klook.loadScript('https://at.alicdn.com/t/font_2140432_9g5scbr9rm.js'); // Activity SVG
        document
            .querySelector(':root')
            .style.setProperty(
                '--primary-color',
                PRIMARY_COLOR[window.KLK_JV_NAME],
            );
    },
};
</script>

<style lang="scss">
:root {
    --primary-color: #4c87e6;
}

#app {
    padding-top: 60px;
}

/**
  *animation begin
  **/

$transition-time: 0.3s;
// fade animation
.list-move {
    transition: all 0.5s;
}

.list-enter-active,
.list-leave-active {
    transition: all 0.3s;
}

.list-enter,
.list-leave-to {
    opacity: 0;
    transform: translateX(-100px);
}

.fade-enter-active,
.fade-leave-active {
    transition: all $transition-time ease;
}

.fade-enter,
.fade-leave-active {
    opacity: 0;
}

// ease animation used in dialog
.ease-enter-active {
    animation: slide_down $transition-time;
}

.ease-leave-active {
    animation: slide_down $transition-time reverse;
}

@keyframes slide_down {
    0% {
        opacity: 0; // transform: scaleY(.8);
        transform: translate3d(0, -10%, 0);
    }
    100% {
        opacity: 1;
        transform: translate3d(0, 0, 0); // transform: scaleY(1);
    }
}

@keyframes circle-scale-loading {
    0%,
    80%,
    100% {
        transform: scale(0);
    }
    40% {
        transform: scale(1);
    }
}

.vue_loading {
    position: absolute;
    text-align: center;
    top: 0px;
    left: 0px;
    z-index: 99;
    margin: 0px;
    padding: 0px;
    width: 100%;
    height: 100%;
    border: none;
    background-color: rgba(255, 255, 255, 0.9);
    cursor: wait;
    opacity: 0;
    transition: opacity 0.4s;

    &:before {
        content: '';
        display: inline-block;
        width: 0;
        height: 100%;
        vertical-align: middle;
    }
}

.fixed {
    position: fixed;
}

.vue_loading_msg {
    display: inline-block;
    vertical-align: middle;
    font-size: 14px;

    .circle_1,
    .circle_2,
    .circle_3 {
        width: 10px;
        height: 10px;
        border-radius: 100%;
        display: inline-block;
        background: var(--primary-color);
        margin-right: 10px;
        -webkit-animation: circle-scale-loading 1.4s infinite ease-in-out;
        animation: circle-scale-loading 1.4s infinite ease-in-out;
        -webkit-animation-fill-mode: both;
        animation-fill-mode: both;
    }

    .circle_1 {
        animation-delay: -0.32s;
    }

    .circle_2 {
        animation-delay: -0.16s;
    }
}

// core.js 里面的globalLoading兼容代码用到下面的样式
.uil-ring-css > div {
    margin-right: auto;
    margin-left: auto;
    font-size: 10px;
    position: relative;
    text-indent: -9999em;
    border-top: 3px solid #e0e0e0;
    border-right: 3px solid #e0e0e0;
    border-bottom: 3px solid #e0e0e0;
    border-left: 3px solid var(--primary-color);
    -webkit-animation: animatename 1.1s infinite linear;
    animation: animatename 1.1s infinite linear;
}

.uil-ring-css > div,
.uil-ring-css > div:after {
    border-radius: 50%;
    width: 30px;
    height: 30px;
}

@-webkit-keyframes animatename {
    0% {
        -webkit-transform: rotate(0deg);
        transform: rotate(0deg);
    }
    100% {
        -webkit-transform: rotate(360deg);
        transform: rotate(360deg);
    }
}

@keyframes animatename {
    0% {
        -webkit-transform: rotate(0deg);
        transform: rotate(0deg);
    }
    100% {
        -webkit-transform: rotate(360deg);
        transform: rotate(360deg);
    }
}
</style>
