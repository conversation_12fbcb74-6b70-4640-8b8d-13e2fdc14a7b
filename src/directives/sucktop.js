/**
 * 侧边栏固定的指令
 * @param {number} suck_top             -  fixed定位后的top属性值
 * @param {number} normal_top           -  使元素定位恢复static时scrollTop的值
 * @param {string} reference            -  相对元素
 * @param {string} [postion = 'right']  -  固定的位置
 * @param {number} [spacing = 20]       -  与对比元素的间距
 */
import throttle from 'lodash/throttle';

export default {
    bind: function(el, binding) {
        function adjustSideMenu() {
            const el_top = el.getBoundingClientRect().top;
            const scroll_top =
                document.body.scrollTop || document.documentElement.scrollTop;
            let fixedPosition = 0,
                position = binding.value.position || 'right',
                spacing = binding.value.spacing || 20;

            if (binding.value.reference) {
                fixedPosition = document
                    .querySelector(binding.value.reference)
                    .getBoundingClientRect()[position];
            }

            if (el_top <= parseInt(binding.value.suck_top)) {
                el.style.position = 'fixed';
                el.style.zIndex = '100';
                el.style.top = binding.value.suck_top + 'px';
                if (binding.value.reference && position === 'left') {
                    el.style.left =
                        fixedPosition - el.offsetWidth - spacing + 'px';
                } else if (binding.value.reference && position === 'right') {
                    el.style.left = fixedPosition + spacing + 'px';
                }
            }
            if (scroll_top <= binding.value.normal_top) {
                el.style.position = 'static';
            }
        }
        el.__vueSuckTop__ = throttle(adjustSideMenu, 100);
        window.addEventListener('scroll', el.__vueSuckTop__);
        window.addEventListener('resize', el.__vueSuckTop__);
    },
    update() {},
    unbind(el) {
        el.style.position = 'static';
        window.removeEventListener('scroll', el.__vueSuckTop__);
        window.removeEventListener('resize', el.__vueSuckTop__);
        delete el.__vueSuckTop__;
    },
};
