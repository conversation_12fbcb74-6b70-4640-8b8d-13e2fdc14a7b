import Vue from 'vue';
import VueLazyload from 'vue-lazyload';

const lazyErrorImage =
    'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABAQMAAAAl21bKAAAABGdBTUEAALGPC/xhBQAAAAFzUkdCAK7OHOkAAAADUExURenp6VcjxIwAAAAKSURBVAjXY2AAAAACAAHiIbwzAAAAAElFTkSuQmCC';

// const lazyLoadingImage = 'https://res.klook.com/image/upload/image_logo_mx7wgd.png'

// 图片格式配置映射
const imageOptionsMap = {
    default: {
        w: 0,
        h: 0,
        blur: 0,
        quality: 0,
        waterMark: false,
        customize: '',
    },
    thumbnail: {
        w: 120,
        h: 120,
        blur: 0,
        quality: 80,
        waterMark: false,
        customize: '',
    },
    // 可以根据需要添加更多配置
};

// 图片格式化函数
const imageFormat = (imgSrc, type = 'default', retina) => {
    let newImgUrl = imgSrc;

    const option = imageOptionsMap[type];

    if (option) {
        // 引用类型会导致w, h增加
        const imageOptions = { ...option };

        if (retina) {
            const { w, h } = imageOptions;
            w && (imageOptions.w = w * 2);
            h && (imageOptions.h = h * 2);
        }

        const options = imageOptions;
        const w = options.w && options.w > 0 ? `,w_${options.w}` : '';
        const h = options.h && options.h > 0 ? `,h_${options.h}` : '';
        const size = w && h ? `c_fill${w}${h}` : '';
        const blur =
            options.blur && options.blur > 0 ? `e_blur:${options.blur}` : '';
        const quality =
            options.quality && options.quality > 0
                ? `fl_lossy.progressive,q_${options.quality},f_auto`
                : 'fl_lossy.progressive,q_auto';
        const customize =
            options.customize && options.customize !== ''
                ? options.customize
                : '';
        let mark = options.waterMark
            ? 'w_80,x_15,y_15,g_south_west,l_Klook_water_br_trans_yhcmh3'
            : '';

        // 判断原链接是否有 size、blur、quality、water mark 参数
        // 若原链接有以上参数，则利用配置参数替换原链接参数

        // 匹配图片大小
        if (size) {
            newImgUrl = newImgUrl.replace(/\/[^/]*c_fill*[^/]*\//, '/');
        }
        // 匹配高斯模糊
        if (blur) {
            newImgUrl = newImgUrl.replace(/\/[^/]*e_blur:(\d*)*[^/]*\//, '/');
        }
        // 匹配图片质量
        if (quality) {
            newImgUrl = newImgUrl.replace(
                /\/[^/]*fl_lossy\.progressive*[^/]*\//,
                '/',
            );
        }
        // 匹配水印
        const matchWaterMark = /\/[^/]*_water*[^/]*\//.test(newImgUrl);
        if (matchWaterMark) {
            mark = '/';
        }
        const prefix = [size, quality, blur, mark, customize]
            .filter(item => item !== '')
            .join('/');
        const temp = `image/upload/${prefix}/`;
        newImgUrl = newImgUrl.replace('image/upload/', temp);
    }

    return newImgUrl;
};

const plugin = (options = {}) => {
    const { store = {} } = options;
    const { klook = {} } = store.state || {};
    const platform = klook.platform || 'desktop';
    const retina = klook.retina || false;

    Vue.use(VueLazyload, {
        observer: true,
        adapter: {
            loaded(detail) {
                const { el, bindType } = detail;
                const ratio = el.getAttribute('ratio');
                const gradient = el.dataset?.gradient || '';

                if (ratio) {
                    const logo =
                        el?.querySelector?.('.v-lazy-logo') ||
                        el.parentElement?.querySelector?.('.v-lazy-logo');
                    logo && (logo.style.display = 'none');
                }

                if (gradient && bindType === 'background-image') {
                    el.style[bindType] = `${gradient},${el.style[bindType]}`;
                }
            },
        },
        error: lazyErrorImage,
        filter: {
            webp(listener, options) {
                const type = listener.el.dataset?.imgType || '';
                const isRetina = platform === 'mobile' && retina;
                const src = imageFormat(listener.src, type, isRetina);
                if (!options.supportWebp) {
                    return;
                }
                const isCDN = /res\.klook\.com/;
                if (isCDN.test(src)) {
                    listener.src = src.replace(/\.\w+$/, '.webp');
                }
            },
        },
    });

    return Promise.resolve();
};

export default plugin;
