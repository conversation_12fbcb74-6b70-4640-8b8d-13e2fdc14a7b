<template lang="html">
    <table class="klk_grid">
        <thead>
            <tr>
                <th
                    :key="index"
                    v-for="(item, index) in cols"
                    :width="item.width"
                    v-html="item.label"
                ></th>
            </tr>
        </thead>
        <transition-group
            v-if="transition_enable"
            :name="transition_name"
            tag="tbody"
        >
            <slot
                v-for="(item, index) in table_data"
                :$index="index"
                :row="item"
            ></slot>
        </transition-group>
        <tbody v-else>
            <slot
                v-for="(item, index) in table_data"
                :$index="index"
                :row="item"
            ></slot>
        </tbody>
        <slot name="tfoot"></slot>
    </table>
</template>

<script>
export default {
    name: 'KlkTable',
    props: {
        table_data: {
            type: Array,
            required: true,
        },
        cols: {
            type: Array,
            required: true,
        },
        transition_enable: {
            //open tr add remove animation
            type: Boolean,
            default: () => false,
        },
        transition_name: {
            type: String,
            default: () => 'list',
        },
    },
};
</script>

<style lang="scss">
.klk_grid {
    table-layout: fixed;
    border-spacing: 0;
    border-collapse: collapse;
    width: 100%;
    td,
    th {
        padding: 12px 8px;
        border: solid 1px #d5d5d5;
        word-wrap: break-word;
    }
    thead {
        tr {
            height: 40px;
            th {
                background-color: #f9f9f9;
                font-size: 12px;
                text-align: center;
                color: #333;
            }
        }
    }
    tbody {
        tr {
            td {
                font-size: 12px;
                line-height: 1.67;
                text-align: center;
                color: #424242;
                height: 49px;
            }
        }
    }
}
</style>
