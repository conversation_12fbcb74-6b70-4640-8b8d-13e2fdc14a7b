/**
 * 挂载当前文件夹中的所有组件，参考https://cn.vuejs.org/v2/guide/components-registration.html
 */

import Vue from 'vue';
import upperFirst from 'lodash/upperFirst';
import camelCase from 'lodash/camelCase';

const requireComponents = require.context('.', true, /\.(vue|js)$/);

requireComponents.keys().forEach(fileName => {
    const componentConfig = requireComponents(fileName);

    if (/(utils|src|index\.js)/.test(fileName)) {
        return;
    }

    const componentName = upperFirst(
        camelCase(
            fileName
                .slice(fileName.lastIndexOf('/') + 1)
                .replace(/^(.*)\.\w+$/, '$1'),
        ),
    );

    if (componentName === 'Modal' || componentName === 'Message') {
        Vue.prototype[`$${componentName}`] =
            componentConfig.default || componentConfig;
    }
    Vue.component(
        `Klk${componentName}`,
        componentConfig.default || componentConfig,
    );
});
