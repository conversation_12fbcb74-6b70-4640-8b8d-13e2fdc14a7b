<template>
    <div
        :class="{
            'single-date-picker': true,
            'single-date-picker__disabled': disabled,
        }"
    >
        <div class="input-container">
            <klk-input
                prepend-icon="icon_time_calendar"
                append-icon="icon_navigation_chevron_down"
                :placeholder="placeholder"
                @focus="onFocused"
                :value="displayValue"
                :disabled="disabled"
                :readonly="readOnly"
            >
            </klk-input>
        </div>
        <div class="dropdown-container" v-if="focused">
            <outside-click-handler @outside="onBlur">
                <klk-date-picker
                    :view-switchable="viewSwitchable"
                    :min-date="minDate"
                    class="package-date-picker"
                    :date.sync="selectedDate"
                    shadow
                    @select="onDayClick"
                    :should-disable-date="isDayBlocked"
                    :tip="tip"
                >
                    <template
                        v-if="getPrice"
                        slot="date-append"
                        slot-scope="{ date, disabled, selected }"
                    >
                        {{ getPrice(date, disabled) }}
                    </template>
                </klk-date-picker>
            </outside-click-handler>
        </div>
    </div>
</template>

<script>
import { parse, isValid, format } from 'date-fns';
import { formatDate } from './src/utils';
import OutsideClickHandler from './src/OutsideClickHandler.vue';

export default {
    name: 'SingleDatePicker',
    props: {
        viewSwitchable: { type: Boolean, default: true },
        minDate: { type: Date, default: () => new Date('1900-01-01') },
        value: {},
        isDayBlocked: {},
        format: {},
        getPrice: {
            type: Function,
            default: () => '',
        },
        tip: { type: String, default: '' },
        disabled: { type: Boolean, default: false },
        readOnly: { type: Boolean, default: true },
        displayFormat: { type: String, default: '' },
        placeholder: { type: String, default: 'Please Select' },
    },
    data() {
        return {
            focused: false,
        };
    },
    components: {
        OutsideClickHandler,
    },
    computed: {
        selectedDate() {
            if (!this.value) return null;
            const parsedDate = parse(this.value);
            return isValid(parsedDate) ? parsedDate : null;
        },
        displayValue() {
            return this.selectedDate
                ? formatDate(this.selectedDate, this.displayFormat, this.lang)
                : '';
        },
    },
    methods: {
        onDayClick(day) {
            this.$emit('input', this.format ? format(day, this.format) : day);
            this.onBlur();
        },
        onFocused() {
            this.focused = !this.disabled;
        },
        onBlur() {
            const inputEl = this.$el
                ? this.$el.querySelector('.input-container input')
                : null;
            if (inputEl) {
                inputEl.blur();
            }
            this.focused = false;
        },
    },
};
</script>

<style lang="scss">
.single-date-picker {
    position: relative;
    input {
        cursor: pointer;
    }
    &:focus {
        border: none;
        outline: none;
    }

    .dropdown-container {
        position: absolute;
        background: #fff;
        left: 0;
        margin-top: 5px;
        margin-bottom: 20px;
        z-index: 999;

        .klk-date-picker {
            .klk-date-picker-tip {
                padding-left: 14px;
            }

            .klk-icon {
                width: 1em;
                height: 1em;
                vertical-align: middle;
                position: static;
            }
        }
    }
}
</style>
