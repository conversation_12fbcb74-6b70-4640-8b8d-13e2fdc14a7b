
import { isDate, getDay, startOfMonth, addDays, subDays, getDaysInMonth, format, setMonth } from 'date-fns'
import localeCN from 'date-fns/locale/zh_cn';
import localeTW from 'date-fns/locale/zh_tw';

const locales = {
    'zh-CN': localeCN,
    'zh-TW': localeTW
};

const getCalendarMonthWeeks = (month, firstDayOfWeek) => {
    if (!isDate(month)) {
        throw new TypeError('`month` must be a valid date object');
    }

    if ([0,1,2,3,4,5,6].indexOf(firstDayOfWeek) === -1) {
        firstDayOfWeek = 0;
    }

    // getDay 返回 0 - 6, 0表示周日
    const firstDateOfMonth = startOfMonth(month);
    const firstDayOfMonth = getDay(firstDateOfMonth);

    const firstDate = subDays(firstDateOfMonth, Math.abs(firstDayOfMonth - firstDayOfWeek));

    const monthWeeks = Math.ceil((getDaysInMonth(month) + Math.abs(firstDayOfMonth, firstDayOfWeek)) / 7);

    return Array.from(new Array(monthWeeks), (i, index) => {
        return Array.from([0, 1, 2, 3, 4, 5, 6], j => addDays(firstDate, index * 7 + j));
    });
};

const monthFormat = {
    'zh-CN': 'YYYY年 MMMM',
    'zh-TW': 'YYYY年 MMMM',
    'en': 'MMMM YYYY'
};

const dateFormat = {
    'en': 'DD MMM YYYY',
    'zh-CN': 'YYYY年MM月DD日',
    "zh-TW": 'YYYY年MM月DD日'
};

const formatMonth = (date, lang) => {
    lang = lang || window.KLK_LANG || 'en';
    return format(date, monthFormat[lang] || monthFormat['en'], { locale: locales[lang] || '' });
};

const formatDate = (date, formatStr, lang) => {
    lang = lang || window.KLK_LANG || 'en';
    return format(date, formatStr || dateFormat[lang] || 'YYYY-MM-DD', { locale: locales[lang] || '' });
};

const getCalendarMonths = () => {
    return Array.from(new Array(12), (i, index) => setMonth(new Date(), index));
};

export {
    getCalendarMonthWeeks,
    formatDate,
    formatMonth,
    getCalendarMonths
};