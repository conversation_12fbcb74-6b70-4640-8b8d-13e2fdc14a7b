<template>
    <div>
        <slot></slot>
    </div>
</template>

<script>
export default {
    name: 'OutsideClickHandler',
    mounted() {
        document.addEventListener('mousedown', this.onMouseDown, true);
    },
    beforeDestroy() {
        document.removeEventListener('mousedown', this.onMouseDown, true);
        document.removeEventListener('mouseup', this.onMouseUp, true);
    },
    methods: {
        onMouseDown(e) {
            const isDescendantOfRoot = this.$el && this.$el.contains(e.target);
            if (!isDescendantOfRoot) {
                document.addEventListener('mouseup', this.onMouseUp, true);
            }
        },
        onMouseUp(e) {
            const isDescendantOfRoot = this.$el && this.$el.contains(e.target);
            document.removeEventListener('mouseup', this.onMouseUp, true);
            if (!isDescendantOfRoot) {
                this.$emit('outside', e);
            }
        }
    }
}
</script>

