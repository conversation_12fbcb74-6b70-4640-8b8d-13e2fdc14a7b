<template lang="html">
    <div class="nodata" v-show="show">
        <!-- <img src="https://res.klook.com/image/upload/q_80/v1504768199/no_data_p6h2i1.png" alt=""> -->
        <img src="./empty.png" alt="" />
        <div class="nodata_text">{{ no_results_find }}</div>
    </div>
</template>

<script>

export default {
    name:"KlkNoData",
    props:{
        show: {
            type: <PERSON><PERSON>an,
            default:()=>true
        },
        no_data_text_prop:{
            default:()=>''
        }
    },
    data:function(){
        return {
            no_results_find:this.$t('no_results_find')
        }
    },
    mounted(){
        this.no_results_find = this.no_data_text_prop || this.no_results_find;
    }

}
</script>

<style lang="scss">
.nodata{
    width: 100%;
    text-align: center;
    padding: 100px;
    background: #fff;
    img{
        width: 140px;
    }
    .nodata_text{
        font-size: 16px;
        line-height: 16px;
        color: #666;
        margin-top: 24px;
    }
}
</style>
