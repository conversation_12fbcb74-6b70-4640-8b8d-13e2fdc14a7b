<template>
    <div ref="tableWrapper">
        <div
            class="klk-table-wrapper"
            :class="className"
            v-if="table_data && table_data.length"
        >
            <div class="klk-table-default">
                <div
                    class="klk-table-fixed-left"
                    v-if="
                        cols.filter(col => col.fixed === 'left').length && flag
                    "
                >
                    <table class="klk_grid">
                        <thead>
                            <tr>
                                <th
                                    :class="{
                                        'klk-table-hidden':
                                            item.fixed !== 'left',
                                    }"
                                    :key="index"
                                    v-for="(item, index) in colsFixedLeft"
                                    :width="item.width"
                                    v-html="item.label"
                                ></th>
                            </tr>
                        </thead>
                        <transition-group
                            v-if="transition_enable"
                            :name="transition_name"
                            tag="tbody"
                        >
                            <slot
                                v-for="(item, index) in table_data"
                                :$index="index"
                                :row="item"
                            ></slot>
                        </transition-group>
                        <tbody v-else>
                            <slot
                                v-for="(item, index) in table_data"
                                :$index="index"
                                :row="item"
                            ></slot>
                        </tbody>
                        <slot name="tfoot"></slot>
                    </table>
                </div>

                <div class="klk-table-main">
                    <table class="klk_grid">
                        <thead>
                            <tr>
                                <th
                                    :key="index"
                                    v-for="(item, index) in colsMiddle"
                                    :width="item.width"
                                    v-html="item.label"
                                ></th>
                            </tr>
                        </thead>
                        <transition-group
                            v-if="transition_enable"
                            :name="transition_name"
                            tag="tbody"
                        >
                            <slot
                                v-for="(item, index) in table_data"
                                :$index="index"
                                :row="item"
                            ></slot>
                        </transition-group>
                        <tbody v-else>
                            <slot
                                v-for="(item, index) in table_data"
                                :$index="index"
                                :row="item"
                            ></slot>
                        </tbody>
                        <slot name="tfoot"></slot>
                    </table>
                </div>

                <div
                    class="klk-table-fixed-right"
                    v-if="
                        cols.filter(col => col.fixed === 'right').length && flag
                    "
                >
                    <table class="klk_grid">
                        <thead>
                            <tr>
                                <th
                                    :class="{
                                        'klk-table-hidden':
                                            item.fixed !== 'right',
                                    }"
                                    :key="index"
                                    v-for="(item, index) in colsFixedRight"
                                    :width="item.width"
                                    v-html="item.label"
                                ></th>
                            </tr>
                        </thead>
                        <transition-group
                            v-if="transition_enable"
                            :name="transition_name"
                            tag="tbody"
                        >
                            <slot
                                v-for="(item, index) in table_data"
                                :$index="index"
                                :row="item"
                            ></slot>
                        </transition-group>
                        <tbody v-else>
                            <slot
                                v-for="(item, index) in table_data"
                                :$index="index"
                                :row="item"
                            ></slot>
                        </tbody>
                        <slot name="tfoot"></slot>
                    </table>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    name: 'KlkTableExtend',
    props: {
        table_data: {
            type: Array,
            required: true,
        },
        cols: {
            type: Array,
            required: true,
        },
        transition_enable: {
            //open tr add remove animation
            type: Boolean,
            default: () => false,
        },
        transition_name: {
            type: String,
            default: () => 'list',
        },
    },
    data() {
        return {
            className: '',
            leftIndex: [],
            rightIndex: [],
            allIndex: [],
            colsFixedLeft: [],
            colsMiddle: [],
            colsFixedRight: [],
            flag: true,
        };
    },
    created() {
        this.colsMiddle = this.sortArrHead(this.cols);
    },
    mounted() {
        this.className = `klk-table-${Math.random().toFixed(3) * 1000}`;
        this.init();
    },
    methods: {
        init() {
            this.$nextTick(() => {
                this.setTdPosition();
                let leftBodyTrs = this.$refs.tableWrapper.querySelectorAll(
                    '.' + this.className + ' .klk-table-fixed-left tbody tr',
                );
                let rightBodyTrs = this.$refs.tableWrapper.querySelectorAll(
                    '.' + this.className + ' .klk-table-fixed-right tbody tr',
                );
                if (leftBodyTrs && leftBodyTrs.length) {
                    this.setTdCls(leftBodyTrs, 'left');
                }
                if (rightBodyTrs && rightBodyTrs.length) {
                    this.setTdCls(rightBodyTrs, 'right');
                }
            });
        },
        sortArrHead(cols, field = 'fixed', direction) {
            let len = cols.length;
            let cloneArr = JSON.parse(JSON.stringify(cols));
            let temp = [];
            if (!direction) {
                let left = cloneArr.filter(arr => arr[field] === 'left');
                let mid = cloneArr.filter(
                    arr =>
                        !arr[field] ||
                        (arr[field] !== 'left' && arr[field] !== 'right'),
                );
                let right = cloneArr.filter(arr => arr[field] === 'right');
                return left.concat(mid).concat(right);
            }

            while (len) {
                if (
                    cloneArr[len - 1][field] &&
                    cloneArr[len - 1][field] === direction
                ) {
                    temp.unshift(cloneArr[len - 1]);
                    cloneArr.splice(len, 1);
                    !this[direction + 'Index'].includes(len - 1)
                        ? this[direction + 'Index'].push(len - 1)
                        : '';
                }
                len--;
            }
            return temp.concat(
                cols.filter(col => !col[field] || col[field] !== direction),
            );
        },
        setTdCls(elements, direction = 'left') {
            if (
                (!this[direction + 'Index'] &&
                    !this[direction + 'Index'].length) ||
                (!elements && elements.length)
            )
                return;
            try {
                elements.forEach(element => {
                    this[direction + 'Index'].forEach((i, index) => {
                        // 当有一个插入到最前面后，索引需要+1
                        if (index > 0) i += 1;
                        element.childNodes[i]
                            ? element.insertBefore(
                                  element.childNodes[i],
                                  element.childNodes[0],
                              )
                            : '';
                    });
                });

                // 设置固定的table的宽度
                let tableWidth = 0;
                this.$refs.tableWrapper
                    .querySelectorAll(
                        '.' +
                            this.className +
                            ' .klk-table-fixed-' +
                            direction +
                            ' thead tr th',
                    )
                    .forEach(th => {
                        if (!th.classList.contains('klk-table-hidden')) {
                            tableWidth += th.offsetWidth;
                        }
                    });
                this.$refs.tableWrapper.querySelector(
                    '.klk-table-fixed-' + direction,
                ).style.width = ++tableWidth + 'px';
            } catch (e) {
                console.log(e);
            }
        },
        setTdPosition() {
            try {
                let mainBodyTrs = this.$refs.tableWrapper.querySelectorAll(
                    '.' + this.className + ' .klk-table-main tbody tr',
                );

                let leftFragment = [];
                if (this.leftIndex && this.leftIndex.length) {
                    let leftIndex = JSON.parse(
                        JSON.stringify(this.leftIndex),
                    ).sort((a, b) => {
                        return b - a;
                    });

                    mainBodyTrs.forEach((element, eleIndex) => {
                        let fragments = [];
                        leftIndex.forEach(i => {
                            fragments.push(
                                document
                                    .createDocumentFragment()
                                    .appendChild(
                                        element.childNodes[i].cloneNode(true),
                                    ),
                            );
                        });
                        leftFragment[eleIndex] = fragments;
                    });
                }

                let rightFragment = [];
                if (this.rightIndex && this.rightIndex.length) {
                    let rightIndex = JSON.parse(
                        JSON.stringify(this.rightIndex),
                    ).sort((a, b) => {
                        return a - b;
                    });

                    mainBodyTrs.forEach((element, eleIndex) => {
                        let fragments = [];
                        rightIndex.forEach(i => {
                            fragments.push(
                                document
                                    .createDocumentFragment()
                                    .appendChild(
                                        element.childNodes[i].cloneNode(true),
                                    ),
                            );
                        });
                        rightFragment[eleIndex] = fragments;
                    });
                }

                Array.from(mainBodyTrs).forEach((tr, trIndex) => {
                    this.allIndex.forEach(posIndex => {
                        tr.removeChild(tr.childNodes[posIndex]);
                    });

                    if (leftFragment && leftFragment.length) {
                        leftFragment[trIndex].forEach(td => {
                            tr.insertBefore(td, tr.childNodes[0]);
                        });
                    }

                    if (rightFragment && rightFragment.length) {
                        rightFragment[trIndex].forEach(td => {
                            tr.appendChild(td);
                        });
                    }
                });
            } catch (e) {
                console.log(e);
            }
        },
    },
    watch: {
        table_data: {
            handler: function() {
                this.flag = false;
                this.$forceUpdate();
                this.colsFixedLeft = this.sortArrHead(
                    this.cols,
                    'fixed',
                    'left',
                );
                this.colsFixedRight = this.sortArrHead(
                    this.cols,
                    'fixed',
                    'right',
                );
                this.allIndex = this.leftIndex
                    .concat(this.rightIndex)
                    .sort((a, b) => {
                        return b - a;
                    });

                this.$nextTick(() => {
                    this.flag = true;
                    this.init();
                });
            },
            deep: true,
            // immediate: true
        },
    },
};
</script>

<style lang="scss">
.klk-table-wrapper {
    position: relative;

    .klk-table-default {
        width: 100%;
        overflow-x: scroll;
    }

    .klk-table-hidden {
        visibility: hidden;
    }

    .klk-table-fixed-left,
    .klk-table-fixed-right {
        position: absolute;
        background: #fff;
        overflow: hidden;
        top: 0;
    }

    .klk-table-fixed-left {
        left: 0;
        box-shadow: 4px 0 8px 0 rgba(0, 0, 0, 0.08);
    }

    .klk-table-fixed-right {
        right: 0;
        box-shadow: -4px 0 8px 0 rgba(0, 0, 0, 0.08);
    }

    .klk_grid {
        table-layout: fixed;
        border-spacing: 0;
        border-collapse: collapse;
        width: 100%;
        td,
        th {
            padding: 12px 8px;
            border: solid 1px #d5d5d5;
            word-wrap: break-word;
        }
        thead {
            tr {
                height: 40px;
                th {
                    background-color: #f9f9f9;
                    font-size: 12px;
                    text-align: center;
                    color: #333;
                }
            }
        }
        tbody {
            tr {
                td {
                    font-size: 12px;
                    line-height: 1.67;
                    text-align: center;
                    color: #424242;
                    height: 49px;
                }
            }
        }
    }
}
</style>
