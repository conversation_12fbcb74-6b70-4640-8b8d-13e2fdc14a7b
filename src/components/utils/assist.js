// Find components upward
export const findComponentUpward = (context, componentName, componentNames) => {
    if (typeof componentName === 'string') {
        componentNames = [componentName];
    } else {
        componentNames = componentName;
    }

    let parent = context.$parent;
    let name = parent.$options.name;
    while (parent && (!name || componentNames.indexOf(name) < 0)) {
        parent = parent.$parent;
        if (parent) name = parent.$options.name;
    }
    return parent;
};

// Find components downward
export const findComponentsDownward = (context, componentName) => {
    return context.$children.reduce(function(components, child) {
        if (child.$options.name === componentName) components.push(child);
        let foundChilds = findComponentsDownward(child, componentName);
        return components.concat(foundChilds);
    }, []);
};

export const oneOf = (value, validList) => {
    for (let i = 0; i < validList.length; i++) {
        if (value === validList[i]) {
            return true;
        }
    }
    return false;
};
