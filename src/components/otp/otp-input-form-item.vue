<template>
    <OtpFormItem v-bind="formItemAttrs">
        <OtpInput
            ref="codeInput"
            v-bind="inputAttrs"
            @on-complete="codeComplete"
        />
    </OtpFormItem>
</template>

<script>
import OtpFormItem from './otp-form-item.vue';
import OtpInput from './otp-input.vue';

export default {
    name: 'KlkUserFormItemCodeInput',
    components: {
        OtpFormItem,
        OtpInput,
    },
    props: {
        formItemAttrs: {
            type: Object,
            default: () => {},
        },
        inputAttrs: {
            type: Object,
            default: () => {},
        },
    },
    data() {
        return {};
    },
    methods: {
        codeComplete(value) {
            this.$emit('on-complete', value);
        },
        focusInput() {
            this.$refs &&
                this.$refs.codeInput &&
                this.$refs.codeInput.handleInputFocus();
        },
    },
};
</script>
