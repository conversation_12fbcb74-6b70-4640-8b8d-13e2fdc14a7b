<template>
    <div class="klk-otp-validate_input-wrapper">
        <input
            id="otp-validate-code"
            ref="inputCode"
            v-model="codeValue"
            type="number"
            class="klk-otp-validate_inputCode"
            autocomplete="off"
            inputmode="numeric"
            :disabled="inputDisabled"
            @focus="onInputFocus"
            @blur="onInputBlur"
            @input="handleAutoInput"
            @keydown="onKeydown"
        />
        <div class="klk-otp-validate_labels">
            <label
                for="otp-validate-code"
                v-for="(item, index) in codeLength"
                :key="index"
                class="klk-otp-validate_label"
                :class="{
                    'klk-otp-validate_label--active':
                        inputCodeFocus === true && index === currentIndex,
                    'klk-otp-validate_label--small': inputSize === 'small',
                }"
            >
                {{ codeArray[index] }}
            </label>
        </div>
    </div>
</template>

<script>
export default {
    name: 'OtpInput',
    components: {},
    props: {
        value: {
            type: [String, Number],
            default: '',
        },
        inputSize: {
            type: String,
            default: 'big',
            validator(val) {
                return ['small', 'big'].includes(val);
            },
        },
        inputDisabled: {
            type: Boolean,
            default: false,
        },
        codeLength: {
            type: Number,
            default: 6,
        },
        autofocus: {
            type: Boolean,
            default: true,
        },
    },
    data() {
        return {
            inputCodeFocus: true,
            codeValue: '',
        };
    },
    computed: {
        codeArray() {
            return this.codeValue ? this.codeValue.split('') : [];
        },
        currentIndex() {
            return this.codeValue.length || 0;
        },
    },
    watch: {
        value(val) {
            this.codeValue = val;
        },
        codeValue(val) {
            if (val && val.length === this.codeLength) {
                this.$emit('on-complete', val);
            }
        },
        inputDisabled(val) {
            if (val) {
                this.inputCodeFocus = false;
            }
        },
    },
    methods: {
        onKeydown(e) {
            const keyCode = e.keyCode;
            // 不支持输入arrow keys, "e", "-", "."
            if ([37, 38, 39, 40, 69, 189, 190].includes(keyCode)) {
                e.preventDefault();
            }
        },
        handleAutoInput(e) {
            let value = e.target.value;
            if (value.length > this.codeLength) {
                value = value.slice(0, this.codeLength);
                this.codeValue = value;
            }
        },
        handleInputFocus() {
            setTimeout(() => {
                this.$refs &&
                    this.$refs.inputCode &&
                    this.$refs.inputCode.focus();
            });
        },
        onInputFocus() {
            this.inputCodeFocus = true;
        },
        onInputBlur() {
            this.inputCodeFocus = false;
        },
    },
    mounted() {
        this.inputCodeFocus = !this.inputDisabled && this.autofocus;
        if (this.autofocus) {
            this.handleInputFocus();
        }
    },
};
</script>

<style lang="scss" scoped>
@keyframes flash {
    0% {
        opacity: 0;
    }
    50% {
        opacity: 1;
    }
    100% {
        opacity: 0;
    }
}
.klk-otp-validate_input-wrapper {
    width: 100%;
    overflow: hidden;
}

.klk-otp-validate_inputCode {
    width: 100%;
    padding: 0;
    height: 40px;
    font-size: 20px;
    overflow: hidden;
    border: none;
    outline: none;
    -webkit-tap-highlight-color: transparent;
    opacity: 0;
    margin-left: -100% !important;
}

.klk-otp-validate_labels {
    display: flex;
    width: 100%;
    height: 48px;
    justify-content: space-between;
    margin-top: -40px;

    .klk-otp-validate_label {
        font-size: 24px;
        font-weight: 600;
        color: #212121;
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
        width: 48px;
        height: 48px;
        background: #ffffff;
        border-radius: 12px;
        border: 1px solid #e6e6e6;
        text-align: center;
        box-sizing: border-box;

        &--small {
            width: 38px;
        }

        &--active {
            border-color: #4a4a4a;
            &::after {
                // 伪类实现光标效果
                content: ' ';
                display: inline-block;
                height: 24px;
                width: 2px;
                background: #212121;
                animation: flash 1s infinite steps(1, start);
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
            }
        }
    }
}
</style>
