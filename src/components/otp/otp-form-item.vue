<template>
    <div class="form-item">
        <klk-form-item
            ref="formItem"
            class="form-item-content"
            :class="{
                'form-item-content--error': errorMessage && !inputDisabled,
            }"
            v-bind="$attrs"
        >
            <slot></slot>
        </klk-form-item>
        <div v-if="errorMessage" class="error-container">
            <span class="error-text">{{ errorMessage }}</span>
        </div>
    </div>
</template>

<script>
export default {
    name: 'OtpFormItem',
    props: {
        errorMessage: {
            type: String,
            default: '',
        },
        inputDisabled: {
            type: Boolean,
            default: false,
        },
    },
    computed: {},
    methods: {},
};
</script>

<style lang="scss" scoped>
.form-item {
    .form-item-content {
        margin: 16px 0 0 !important;

        &--error {
            ::v-deep .klk-input-inner,
            ::v-deep .klk-otp-validate_label {
                border-color: #f44622;
            }
        }
    }

    .error-container {
        display: flex;
        align-items: center;
        color: #f44622;
        margin-top: 4px;

        .error-text {
            font-size: 14px;
            font-weight: 400;
        }
    }
}
</style>
