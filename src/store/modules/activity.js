// initial state
const state = {
    packageBookingInfoMap: {}, // cache and share selected package info
    // packageDetailMap: {}, // cache package detail info
    activityInfo: {}, //contains id permissions which will be used in child component

    active_package_id: 0,
};

// getters
const getters = {};

// actions
const actions = {};

// mutations
const mutations = {
    UPDATE_PACKAGE_BOOKING_INFO(state, packageBookingInfo) {
        //先进行单独package的update，防止部分update覆盖之前已经有的全部值
        let packageBookingInfoProcessed = Object.assign(
            {},
            state.packageBookingInfoMap[packageBookingInfo.package_id],
            packageBookingInfo,
        );
        //再进行整个package map 的覆盖
        state.packageBookingInfoMap = Object.assign(
            {},
            state.packageBookingInfoMap,
            {
                //replace old object to enable reactivity
                [packageBookingInfo.package_id]: packageBookingInfoProcessed,
            },
        );
    },
    UPDATE_ACTIVITY_INFO(state, activityInfo) {
        state.activityInfo = activityInfo;
    },
    UPDATE_ACTIVE_PACKAGE_ID(state, packageId) {
        state.active_package_id = packageId;
    },
};

export default {
    namespaced: true,
    state,
    getters,
    actions,
    mutations,
};
