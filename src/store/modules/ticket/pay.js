const state = {
    packageId: '',
    skuId: '',
    selectedDate: '',
    selectedTime: '',
    selectedQuality: [],
    packageSchedules: {},
    skuPrices: [],
};

// getters
const getters = {
    selectedArrangementId(state) {
        if (
            !state.selectedDate ||
            !state.selectedTime ||
            Object.keys(state.packageSchedules).length === 0
        ) {
            return '';
        }
        return state.packageSchedules[state.selectedDate].find(
            item => item.time === state.selectedTime,
        ).arrangement_id;
    },
};

// mutations
const mutations = {
    UPDATE_PACKAGE_ID(state, id) {
        state.packageId = id;
    },
    UPDATE_SKU_ID(state, id) {
        state.skuId = id;
    },
    UPDATE_SELECTED_DATE(state, date) {
        state.selectedDate = date;
        state.selectedTime = '';
    },
    UPDATE_SELECTED_TIME(state, time) {
        state.selectedTime = time;
    },
    UPDATE_PACKAGE_SCHEDULES(state, schedules) {
        state.packageSchedules = Object.assign({}, schedules);
    },
    UPDATE_SKU_PRICES(state, skuPrices) {
        state.skuPrices = [].concat(skuPrices);
    },
    UPDATE_SELECTED_QUALITY(state, quality) {
        state.selectedQuality = [].concat(quality);
    },
};
// actions
const actions = {};

export default {
    namespaced: true,
    state,
    getters,
    mutations,
    actions,
};
