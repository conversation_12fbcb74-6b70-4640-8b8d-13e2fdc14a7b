import { TEMPLATE_VERTICAL_MAP } from '@/common/const_data';

const state = {
    activityInfo: {},
    packages: [],
};

// getters
const getters = {};

// mutations
const mutations = {
    UPDATE_ACTIVITY_INFO(state, activityInfo) {
        activityInfo.verticalType =
            TEMPLATE_VERTICAL_MAP[activityInfo.template_id];
        state.activityInfo = activityInfo;
    },
    UPDATE_PACKAGES(state, packages) {
        state.packages = packages;
    },
    UPDATE_ACTIVITY_INFO_ITEM(state, itemObj) {
        state.activityInfo = Object.assign(state.activityInfo, itemObj);
    },
};
// actions
const actions = {
    checkActivityVersion(context, activityId) {
        return new Promise(resolve => {
            klook.ajaxGet(
                '/v1/agentwebserv/activity/version',
                { activity_id: activityId },
                res => {
                    if (res.success) {
                        resolve(res.result || 0);
                    } else {
                        resolve(0);
                    }
                },
            );
        });
    },
};

export default {
    namespaced: true,
    state,
    getters,
    mutations,
    actions,
};
