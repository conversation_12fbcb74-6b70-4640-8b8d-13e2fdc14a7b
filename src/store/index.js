import Vue from 'vue';
import Vuex from 'vuex';

import activity from './modules/activity';
import tmall from './modules/tmall';
import ticketActivity from './modules/ticket/activity';
import ticketPay from './modules/ticket/pay';

Vue.use(Vuex);

const store = new Vuex.Store({
    strict: process.env.NODE_ENV !== 'production',
    modules: {
        activity,
        // message,
        tmall,
        ticket: {
            namespaced: true,
            modules: {
                activity: ticketActivity,
                pay: ticketPay,
            },
        },
    },
    state: {
        initQianKun: false,
        shoppingCartStatus: true,
    },
    mutations: {
        UPDATE_INIT_QIAN_KUN(state, flag) {
            state.initQianKun = flag;
        },
        UPDATE_SHOPPING_CART_STATUS(state, status) {
            state.shoppingCartStatus = status;
        },
    },
});

if (module.hot) {
    // accept actions and mutations as hot modules
    module.hot.accept(
        ['./modules/activity', './modules/tmall', './modules/ticket/activity'],
        () => {
            let activity = require('./modules/activity').default;
            // let message = require('./modules/message').default;
            let tmall = require('./modules/tmall').default;
            let ticketActivity = require('./modules/ticket/activity').default;
            // swap in the new actions and mutations
            store.hotUpdate({
                modules: {
                    activity,
                    tmall,
                    ticketActivity,
                },
            });
        },
    );
}

export default store;
